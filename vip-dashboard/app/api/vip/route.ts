import { NextResponse } from 'next/server';
import path from 'path';

export async function GET() {
  try {
    // 加载Excel文件
    const filePath = path.join(process.cwd(), 'public', 'VIP_simple.xlsx');
    
    // 在服务器端，我们需要使用文件系统读取文件
    const fs = await import('fs');
    const buffer = fs.readFileSync(filePath);
    
    // 使用XLSX直接解析buffer
    const XLSX = await import('xlsx');
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    // 手动解析VIP用户数据
    const users = parseVIPUsersFromData(data as unknown[][]);
    const stats = calculateStatsFromUsers(users);

    return NextResponse.json({
      success: true,
      data: {
        users,
        stats,
      },
    });
  } catch (error) {
    console.error('Error processing VIP data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process VIP data' },
      { status: 500 }
    );
  }
}

function parseVIPUsersFromData(data: unknown[][]) {
  if (!data || data.length === 0) return [];

  const users = [];
  const headers = data[0] as string[];
  
  // 找到主要列的索引
  const vipIndex = headers.findIndex(h => h === 'VIP');
  const levelIndex = headers.findIndex(h => h === '等级');
  const prevBalanceIndex = headers.findIndex(h => h === '六月余额');
  const depositIndex = headers.findIndex(h => h === '本月存入');
  const consumptionIndex = headers.findIndex(h => h === '本月消费');
  const currentBalanceIndex = headers.findIndex(h => h === '剩余金额');

  // 处理每一行数据
  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    
    // 只处理有VIP用户名的行
    if (row[vipIndex] && typeof row[vipIndex] === 'string' && row[vipIndex].trim() !== '') {
      const user = {
        id: `vip-${i}`,
        name: row[vipIndex].toString().trim(),
        level: parseLevel(row[levelIndex]),
        previousBalance: parseNumber(row[prevBalanceIndex]),
        monthlyDeposit: parseNumber(row[depositIndex]),
        monthlyConsumption: parseNumber(row[consumptionIndex]),
        currentBalance: parseNumber(row[currentBalanceIndex]),
      };

      users.push(user);
    }
  }

  return users;
}

interface UserData {
  level: 'VIP1' | 'VIP2' | 'VIP3';
  monthlyDeposit: number;
  monthlyConsumption: number;
  currentBalance: number;
}

function calculateStatsFromUsers(users: UserData[]) {
  const stats = {
    totalUsers: users.length,
    levelDistribution: {
      VIP1: 0,
      VIP2: 0,
      VIP3: 0,
    },
    totalDeposits: 0,
    totalConsumption: 0,
    totalBalance: 0,
    averageBalance: 0,
  };

  users.forEach(user => {
    stats.levelDistribution[user.level as keyof typeof stats.levelDistribution]++;
    stats.totalDeposits += user.monthlyDeposit;
    stats.totalConsumption += user.monthlyConsumption;
    stats.totalBalance += user.currentBalance;
  });

  stats.averageBalance = users.length > 0 ? stats.totalBalance / users.length : 0;

  return stats;
}

function parseLevel(value: unknown): 'VIP1' | 'VIP2' | 'VIP3' {
  if (!value) return 'VIP1';
  const str = value.toString().toUpperCase();
  if (str.includes('VIP3')) return 'VIP3';
  if (str.includes('VIP2')) return 'VIP2';
  return 'VIP1';
}

function parseNumber(value: unknown): number {
  if (value === null || value === undefined || value === '') return 0;
  const num = parseFloat(value.toString());
  return isNaN(num) ? 0 : num;
}
