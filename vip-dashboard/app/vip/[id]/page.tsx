'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { VIPUser } from '@/types/vip';
import { ArrowLeft, Crown, TrendingUp, TrendingDown, Wallet, Calendar } from 'lucide-react';

export default function VIPDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [user, setUser] = useState<VIPUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchVIPUser();
  }, [params.id]);

  const fetchVIPUser = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/vip');
      const result = await response.json();
      
      if (result.success) {
        const foundUser = result.data.users.find((u: VIPUser) => u.id === params.id);
        if (foundUser) {
          setUser(foundUser);
        } else {
          setError('用户未找到');
        }
      } else {
        setError(result.error || 'Failed to load data');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error fetching VIP user:', err);
    } finally {
      setLoading(false);
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'VIP1':
        return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'VIP2':
        return 'text-purple-600 bg-purple-100 border-purple-200';
      case 'VIP3':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-green-600';
    if (balance < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载用户信息中...</p>
        </div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">错误: {error || '用户未找到'}</p>
          <button 
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 返回按钮 */}
        <button
          onClick={() => router.back()}
          className="flex items-center text-gray-600 hover:text-gray-900 mb-6"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          返回列表
        </button>

        {/* 用户头部信息 */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
          <div className="flex items-center space-x-6">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <Crown className="h-10 w-10 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{user.name}</h1>
              <span className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium border ${getLevelColor(user.level)}`}>
                {user.level}
              </span>
            </div>
          </div>
        </div>

        {/* 财务概览 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="bg-gray-100 rounded-md p-3">
                <Wallet className="h-6 w-6 text-gray-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">六月余额</p>
                <p className={`text-2xl font-bold ${getBalanceColor(user.previousBalance)}`}>
                  ¥{user.previousBalance.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="bg-green-100 rounded-md p-3">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">本月存入</p>
                <p className="text-2xl font-bold text-green-600">
                  ¥{user.monthlyDeposit.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="bg-red-100 rounded-md p-3">
                <TrendingDown className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">本月消费</p>
                <p className="text-2xl font-bold text-red-600">
                  ¥{user.monthlyConsumption.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="bg-purple-100 rounded-md p-3">
                <Wallet className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">当前余额</p>
                <p className={`text-2xl font-bold ${getBalanceColor(user.currentBalance)}`}>
                  ¥{user.currentBalance.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 财务分析 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">财务分析</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">净变化</div>
              <div className={`text-2xl font-bold ${getBalanceColor(user.currentBalance - user.previousBalance)}`}>
                ¥{(user.currentBalance - user.previousBalance).toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">相比六月余额</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">存消比</div>
              <div className="text-2xl font-bold text-blue-600">
                {user.monthlyConsumption > 0 ? (user.monthlyDeposit / user.monthlyConsumption).toFixed(2) : '∞'}
              </div>
              <div className="text-sm text-gray-600">存入/消费比率</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">资金利用率</div>
              <div className="text-2xl font-bold text-purple-600">
                {user.monthlyDeposit > 0 ? ((user.monthlyConsumption / user.monthlyDeposit) * 100).toFixed(1) : '0'}%
              </div>
              <div className="text-sm text-gray-600">消费/存入比例</div>
            </div>
          </div>
        </div>

        {/* 交易历史占位符 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center mb-4">
            <Calendar className="h-5 w-5 text-gray-400 mr-2" />
            <h2 className="text-xl font-semibold text-gray-900">交易历史</h2>
          </div>
          <div className="text-center py-8 text-gray-500">
            <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>详细的日交易记录功能正在开发中</p>
            <p className="text-sm">将显示每日存入和消费的详细记录</p>
          </div>
        </div>
      </div>
    </div>
  );
}
