# VIP简表管理系统

一个现代化的VIP用户财务管理系统，基于Next.js构建，用于管理和查看VIP用户的财务信息。

## 功能特性

### 📊 数据概览
- **VIP用户列表**: 显示所有VIP用户的基本信息，包括等级、余额等关键数据
- **统计卡片**: 总用户数、总存入、总消费、总余额等关键指标
- **VIP等级分布**: 可视化显示VIP1、VIP2、VIP3的用户分布

### 👤 用户管理
- **用户详情页**: 查看单个VIP用户的完整财务记录
- **财务分析**: 净变化、存消比、资金利用率等分析指标
- **等级标识**: 清晰的VIP等级标识和颜色区分

### 📈 数据可视化
- **饼图**: VIP等级分布可视化
- **柱状图**: 余额排行榜（前10名）
- **详细表格**: 完整的用户余额详情列表

### 📱 响应式设计
- **移动端适配**: 完美支持手机、平板等各种设备
- **现代化UI**: 使用Tailwind CSS构建的美观界面
- **交互体验**: 流畅的页面切换和悬停效果

## 技术栈

- **前端框架**: Next.js 15 (React 18)
- **样式系统**: Tailwind CSS
- **图表库**: Recharts
- **数据处理**: XLSX (Excel文件解析)
- **图标库**: Lucide React
- **类型系统**: TypeScript

## 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 开发模式
```bash
npm run dev
```
访问 http://localhost:3000

### 3. 生产构建
```bash
npm run build
npm start
```

## 数据格式

系统支持以下Excel数据格式：

### 主要字段
- **VIP**: 用户名
- **等级**: VIP1, VIP2, VIP3
- **六月余额**: 上月结余
- **本月存入**: 当月存入金额
- **本月消费**: 当月消费金额
- **剩余金额**: 当前余额

## 页面说明

### 首页 (/)
- 显示所有VIP用户的卡片列表
- 关键统计指标概览
- VIP等级分布图表

### 统计页面 (/stats)
- 详细的数据可视化图表
- 余额排行榜
- 完整的用户数据表格

### 用户详情页 (/vip/[id])
- 单个用户的完整财务信息
- 财务分析指标
