(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[266],{2764:(e,s,t)=>{Promise.resolve().then(t.bind(t,5820))},5820:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(5155),l=t(2115),r=t(2038),c=t(4529),i=t(447),n=t(2977),d=t(2204),x=t(8369),m=t(7428),o=t(8047),h=t(9969),p=t(770),u=t(8503),g=t(2470),j=t(9819),N=t(2479),b=t(278),y=t(5695);function v(){let e=(0,y.useRouter)(),[s,t]=(0,l.useState)([]),[v,f]=(0,l.useState)(null),[w,k]=(0,l.useState)(!0),[P,I]=(0,l.useState)(null);(0,l.useEffect)(()=>{V()},[]);let V=async()=>{try{k(!0);let e=await fetch("/api/vip"),s=await e.json();s.success?(t(s.data.users),f(s.data.stats)):I(s.error||"Failed to load data")}catch(e){I("Network error occurred"),console.error("Error fetching VIP data:",e)}finally{k(!1)}};if(w)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"加载统计数据中..."})]})});if(P)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-red-600 mb-4",children:["错误: ",P]}),(0,a.jsx)("button",{onClick:V,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"重试"})]})});let C=v?[{name:"VIP1",value:v.levelDistribution.VIP1,color:"#3B82F6"},{name:"VIP2",value:v.levelDistribution.VIP2,color:"#8B5CF6"},{name:"VIP3",value:v.levelDistribution.VIP3,color:"#EAB308"}]:[],S=s.sort((e,s)=>s.currentBalance-e.currentBalance).slice(0,10).map(e=>({name:e.name,balance:e.currentBalance,deposit:e.monthlyDeposit,consumption:e.monthlyConsumption})),B=s.map(e=>({name:e.name,balance:e.currentBalance,level:e.level})).sort((e,s)=>s.balance-e.balance);return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-6",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"返回首页"]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"VIP数据统计"}),(0,a.jsx)("p",{className:"text-gray-600",children:"详细的VIP用户数据分析和可视化"})]}),v&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-blue-100 rounded-md p-3",children:(0,a.jsx)(g.A,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总用户数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:v.totalUsers})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-green-100 rounded-md p-3",children:(0,a.jsx)(j.A,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"平均余额"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:["\xa5",v.averageBalance.toLocaleString()]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-purple-100 rounded-md p-3",children:(0,a.jsx)(N.A,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"存消比"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:v.totalConsumption>0?(v.totalDeposits/v.totalConsumption).toFixed(2):"∞"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-red-100 rounded-md p-3",children:(0,a.jsx)(b.A,{className:"h-6 w-6 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"净流入"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:["\xa5",(v.totalDeposits-v.totalConsumption).toLocaleString()]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"VIP等级分布"}),(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(r.u,{width:"100%",height:"100%",children:(0,a.jsxs)(c.r,{children:[(0,a.jsx)(i.F,{data:C,cx:"50%",cy:"50%",labelLine:!1,label:e=>"".concat(e.name||"",": ").concat(e.value||0," (").concat((100*(e.percent||0)).toFixed(0),"%)"),outerRadius:80,fill:"#8884d8",dataKey:"value",children:C.map((e,s)=>(0,a.jsx)(n.f,{fill:e.color},"cell-".concat(s)))}),(0,a.jsx)(d.m,{})]})})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"余额排行榜 (前10名)"}),(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(r.u,{width:"100%",height:"100%",children:(0,a.jsxs)(x.E,{data:S,margin:{top:5,right:30,left:20,bottom:5},children:[(0,a.jsx)(m.d,{strokeDasharray:"3 3"}),(0,a.jsx)(o.W,{dataKey:"name"}),(0,a.jsx)(h.h,{}),(0,a.jsx)(d.m,{formatter:e=>["\xa5".concat(Number(e).toLocaleString()),"余额"]}),(0,a.jsx)(p.y,{dataKey:"balance",fill:"#3B82F6"})]})})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"用户余额详情"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"排名"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户名"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"等级"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"当前余额"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"本月存入"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"本月消费"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:B.map((e,t)=>{var l,r;return(0,a.jsxs)("tr",{className:t%2==0?"bg-white":"bg-gray-50",children:[(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",t+1]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("VIP1"===e.level?"bg-blue-100 text-blue-800":"VIP2"===e.level?"bg-purple-100 text-purple-800":"bg-yellow-100 text-yellow-800"),children:e.level})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("span",{className:e.balance>=0?"text-green-600":"text-red-600",children:["\xa5",e.balance.toLocaleString()]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-green-600",children:["\xa5",(null==(l=s.find(s=>s.name===e.name))?void 0:l.monthlyDeposit.toLocaleString())||"0"]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-red-600",children:["\xa5",(null==(r=s.find(s=>s.name===e.name))?void 0:r.monthlyConsumption.toLocaleString())||"0"]})]},e.name)})})]})})]})]})})}}},e=>{e.O(0,[79,441,964,358],()=>e(e.s=2764)),_N_E=e.O()}]);