(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[79],{2:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i,a=null,o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),u=()=>{null!==a&&(e.apply(i,a),i=void 0,a=null)},c=null,s=()=>{null!=c&&clearTimeout(c),c=setTimeout(()=>{c=null,l&&u(),f()},t)},f=()=>{null!==c&&(clearTimeout(c),c=null),i=void 0,a=null},h=function(...e){if(r?.aborted)return;i=this,a=e;let t=null==c;s(),o&&t&&u()};return h.schedule=s,h.cancel=f,h.flush=()=>{u()},r?.addEventListener("abort",f,{once:!0}),h}},12:(e,t,r)=>{"use strict";r.d(t,{J:()=>J});var n=r(2115);r(9145);var i={notify(){},get:()=>[]},a="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,o="undefined"!=typeof navigator&&"ReactNative"===navigator.product,l=a||o?n.useLayoutEffect:n.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var u=Symbol.for("react-redux-context"),c="undefined"!=typeof globalThis?globalThis:{},s=function(){if(!n.createContext)return{};let e=c[u]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),f=function(e){let{children:t,context:r,serverState:a,store:o}=e,u=n.useMemo(()=>{let e=function(e,t){let r,n=i,a=0,o=!1;function l(){s.onStateChange&&s.onStateChange()}function u(){if(a++,!r){let t,i;r=e.subscribe(l),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function c(){a--,r&&0===a&&(r(),r=void 0,n.clear(),n=i)}let s={addNestedSub:function(e){u();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:l,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,u())},tryUnsubscribe:function(){o&&(o=!1,c())},getListeners:()=>n};return s}(o);return{store:o,subscription:e,getServerState:a?()=>a:void 0}},[o,a]),c=n.useMemo(()=>o.getState(),[o]);return l(()=>{let{subscription:e}=u;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),c!==o.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[u,c]),n.createElement((r||s).Provider,{value:u},t)},h=r(3258),d=r(7681),p=r(8603),y=r(6948),v=r(8273),g=r(1901),m=r(3279);function b(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var w=r(3472),x=r(6518),O=r(1978),P=(0,d.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,O.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,O.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,O.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:A,removeDot:j,addArea:E,removeArea:M,addLine:S,removeLine:_}=P.actions,k=P.reducer,T={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},C=(0,d.Z0)({name:"brush",initialState:T,reducers:{setBrushSettings:(e,t)=>null==t.payload?T:t.payload}}),{setBrushSettings:D}=C.actions,N=C.reducer,I=r(9064),z=r(8679),R=(0,d.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,O.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,O.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:L,removeRadiusAxis:U,addAngleAxis:B,removeAngleAxis:$}=R.actions,F=R.reducer,K=r(1105),H=r(1407),Z=r(3733),G=r(2782),Y=(0,h.HY)({brush:N,cartesianAxis:w.CA,chartData:v.LV,graphicalItems:x.iZ,layout:g.Vp,legend:I.CU,options:p.lJ,polarAxis:F,polarOptions:K.J,referenceElements:k,rootProps:z.vE,tooltip:y.En}),W=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,d.U1)({reducer:Y,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([m.YF.middleware,m.fP.middleware,H.$7.middleware,Z.x.middleware,G.k.middleware]),devTools:{serialize:{replacer:b},name:"recharts-".concat(t)}})},q=r(1037),V=r(6706);function J(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,a=(0,q.r)(),o=(0,n.useRef)(null);if(a)return r;null==o.current&&(o.current=W(t,i));var l=V.E;return n.createElement(f,{context:l,store:o.current},r)}},63:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=e=>e.layout.width,i=e=>e.layout.height,a=e=>e.layout.scale,o=e=>e.layout.margin},187:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(3014),i=r(1154),a=r(6812),o=r(4827),l=r(6151);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},193:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>l});var n=r(4637),i=r(2115),a=r(6706),o=e=>e,l=()=>{var e=(0,i.useContext)(a.E);return e?e.store.dispatch:o},u=()=>{},c=()=>u,s=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:c,t?t.store.getState:u,t?t.store.getState:u,t?e:u,s)}},278:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(4620).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},301:(e,t,r)=>{"use strict";var n=r(2115),i=r(4171),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,u=n.useEffect,c=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var d=o(e,(f=c(function(){function e(e){if(!u){if(u=!0,o=e,e=n(e),void 0!==i&&h.hasValue){var t=h.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,u=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,i]))[0],f[1]);return u(function(){h.hasValue=!0,h.value=d},[d]),s(d),d}},329:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(997),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},348:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var n=r(2115),i=r(4707);function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.NF)(t)),a=(0,n.useRef)(e);return a.current!==e&&(r.current=(0,i.NF)(t),a.current=e),r.current}},423:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(6790),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=Infinity;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(i,a)))}},435:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var n=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}}},447:(e,t,r)=>{"use strict";r.d(t,{F:()=>eg,L:()=>ef});var n=r(2115),i=r(3758),a=r.n(i),o=r(3834),l=r(4106),u=r(7774),c=r(5540),s=r(1757),f=r(2775),h=r(6584),d=r(3076),p=r(4125),y=r(5276),v=e=>e.graphicalItems.polarItems,g=(0,l.Mz)([d.N,p.E],f.eo),m=(0,l.Mz)([v,f.DP,g],f.ec),b=(0,l.Mz)([m],f.rj),w=(0,l.Mz)([b,u.z3],f.Nk),x=(0,l.Mz)([w,f.DP,m],f.fb),O=(0,l.Mz)([w,f.DP,m],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,s.kr)(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,s.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),P=()=>void 0,A=(0,l.Mz)([f.DP,f.AV,P,O,P],f.wL),j=(0,l.Mz)([f.DP,h.fz,w,x,y.eC,d.N,A],f.tP),E=(0,l.Mz)([j,f.DP,f.xM],f.xp);function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,l.Mz)([f.DP,j,E,d.N],f.g1);var _=(e,t)=>t,k=[],T=(e,t,r)=>(null==r?void 0:r.length)===0?k:r,C=(0,l.Mz)([u.z3,_,T],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>S(S({},t.presentationProps),e.props))),null!=n)return n}),D=(0,l.Mz)([C,_,T],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=(0,s.kr)(e,t.nameKey,t.name);return a=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,s.uM)(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),N=(0,l.Mz)([v,_],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),I=(0,l.Mz)([C,N,T,c.HZ],(e,t,r,n)=>{if(null!=t&&null!=e)return ef({offset:n,pieSettings:t,displayedData:e,cells:r})}),z=r(193),R=r(4081),L=r(7670),U=r(1323),B=r(7273),$=r(2977),F=r(5658),K=r(593),H=r(8983),Z=r(4707),G=r(3439),Y=r(8844),W=r(8895),q=r(2829),V=r(2e3),J=r(662),X=r(1523),Q=r(348),ee=r(5888),et=r(950),er=["onMouseEnter","onClick","onMouseLeave"];function en(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ei(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?en(Object(r),!0).forEach(function(t){ea(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ea(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eo(){return(eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function el(e){var t=(0,n.useMemo)(()=>(0,F.J9)(e,!1),[e]),r=(0,n.useMemo)(()=>(0,F.aS)(e.children,$.f),[e.children]),i=(0,n.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),a=(0,z.G)(e=>D(e,i,r));return n.createElement(J._,{legendPayload:a})}function eu(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:u,tooltipType:c}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:(0,s.uM)(l,t),hide:u,type:c,color:o,unit:""}}}function ec(e){var{sectors:t,props:r,showLabels:i}=e,{label:a,labelLine:l,dataKey:u}=r;if(!i||!a||!t)return null;var c=(0,F.J9)(r,!1),f=(0,F.J9)(a,!1),h=(0,F.J9)(l,!1),d="object"==typeof a&&"offsetRadius"in a&&a.offsetRadius||20,p=t.map((e,t)=>{var r,i,p=(e.startAngle+e.endAngle)/2,y=(0,H.IZ)(e.cx,e.cy,e.outerRadius+d,p),v=ei(ei(ei(ei({},c),e),{},{stroke:"none"},f),{},{index:t,textAnchor:(r=y.x)>(i=e.cx)?"start":r<i?"end":"middle"},y),g=ei(ei(ei(ei({},c),e),{},{fill:"none",stroke:e.fill},h),{},{index:t,points:[(0,H.IZ)(e.cx,e.cy,e.outerRadius,p),y],key:"line"});return n.createElement(L.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},l&&((e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,o.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement(U.I,eo({},t,{type:"linear",className:r}))})(l,g),((e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var i=r;if("function"==typeof e&&(i=e(t),n.isValidElement(i)))return i;var a=(0,o.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement(B.E,eo({},t,{alignmentBaseline:"middle",className:a}),i)})(a,v,(0,s.kr)(e,u)))});return n.createElement(L.W,{className:"recharts-pie-labels"},p)}function es(e){var{sectors:t,activeShape:r,inactiveShape:i,allOtherPieProps:a,showLabels:o}=e,l=(0,z.G)(V.A2),{onMouseEnter:u,onClick:c,onMouseLeave:s}=a,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(a,er),h=(0,W.Cj)(u,a.dataKey),d=(0,W.Pg)(s),p=(0,W.Ub)(c,a.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,o)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var u=r&&String(o)===l,c=u?r:l?i:null,s=ei(ei({},e),{},{stroke:e.stroke,tabIndex:-1,[X.F0]:o,[X.um]:a.dataKey});return n.createElement(L.W,eo({tabIndex:-1,className:"recharts-pie-sector"},(0,G.XC)(f,e,o),{onMouseEnter:h(e,o),onMouseLeave:d(e,o),onClick:p(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),n.createElement(Y.y,eo({option:c,isActive:u,shapeType:"sector"},s)))}),n.createElement(ec,{sectors:t,props:a,showLabels:o}))}function ef(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:u,startAngle:c,endAngle:f,dataKey:h,nameKey:d,tooltipType:p}=i,y=Math.abs(i.minAngle),v=(0,Z.sA)(f-c)*Math.min(Math.abs(f-c),360),g=Math.abs(v),m=a.length<=1?0:null!=(t=i.paddingAngle)?t:0,b=a.filter(e=>0!==(0,s.kr)(e,h,0)).length,w=g-b*y-(g>=360?b:b-1)*m,x=a.reduce((e,t)=>{var r=(0,s.kr)(t,h,0);return e+((0,Z.Et)(r)?r:0)},0);return x>0&&(r=a.map((e,t)=>{var r,a=(0,s.kr)(e,h,0),f=(0,s.kr)(e,d,t),g=((e,t,r)=>{let n,i,a;var{top:o,left:l,width:u,height:c}=t,s=(0,H.lY)(u,c),f=l+(0,Z.F4)(e.cx,u,u/2),h=o+(0,Z.F4)(e.cy,c,c/2),d=(0,Z.F4)(e.innerRadius,s,0);return{cx:f,cy:h,innerRadius:d,outerRadius:(n=r,i=e.outerRadius,a=s,"function"==typeof i?i(n):(0,Z.F4)(i,a,.8*a)),maxRadius:e.maxRadius||Math.sqrt(u*u+c*c)/2}})(i,l,e),b=((0,Z.Et)(a)?a:0)/x,O=ei(ei({},e),o&&o[t]&&o[t].props),P=(r=t?n.endAngle+(0,Z.sA)(v)*m*(0!==a):c)+(0,Z.sA)(v)*((0!==a?y:0)+b*w),A=(r+P)/2,j=(g.innerRadius+g.outerRadius)/2,E=[{name:f,value:a,payload:O,dataKey:h,type:p}],M=(0,H.IZ)(g.cx,g.cy,j,A);return n=ei(ei(ei(ei({},i.presentationProps),{},{percent:b,cornerRadius:u,name:f,tooltipPayload:E,midAngle:A,middleRadius:j,tooltipPosition:M},O),g),{},{value:(0,s.kr)(e,h),startAngle:r,endAngle:P,payload:O,paddingAngle:(0,Z.sA)(v)*m})})),r}function eh(e){var{props:t,previousSectorsRef:r}=e,{sectors:i,isAnimationActive:o,animationBegin:l,animationDuration:u,animationEasing:c,activeShape:s,inactiveShape:f,onAnimationStart:h,onAnimationEnd:d}=t,p=(0,Q.n)(t,"recharts-pie-"),y=r.current,[v,g]=(0,n.useState)(!0),m=(0,n.useCallback)(()=>{"function"==typeof d&&d(),g(!1)},[d]),b=(0,n.useCallback)(()=>{"function"==typeof h&&h(),g(!0)},[h]);return n.createElement(et.i,{begin:l,duration:u,isActive:o,easing:c,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:m,key:p},e=>{var{t:o}=e,l=[],u=(i&&i[0]).startAngle;return i.forEach((e,t)=>{var r=y&&y[t],n=t>0?a()(e,"paddingAngle",0):0;if(r){var i=(0,Z.Dj)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),c=ei(ei({},e),{},{startAngle:u+n,endAngle:u+i(o)+n});l.push(c),u=c.endAngle}else{var{endAngle:s,startAngle:f}=e,h=(0,Z.Dj)(0,s-f)(o),d=ei(ei({},e),{},{startAngle:u+n,endAngle:u+h+n});l.push(d),u=d.endAngle}}),r.current=l,n.createElement(L.W,null,n.createElement(es,{sectors:l,activeShape:s,inactiveShape:f,allOtherPieProps:t,showLabels:!v}))})}function ed(e){var{sectors:t,isAnimationActive:r,activeShape:i,inactiveShape:a}=e,o=(0,n.useRef)(null),l=o.current;return r&&t&&t.length&&(!l||l!==t)?n.createElement(eh,{props:e,previousSectorsRef:o}):n.createElement(es,{sectors:t,activeShape:i,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function ep(e){var{hide:t,className:r,rootTabIndex:i}=e,a=(0,o.$)("recharts-pie",r);return t?null:n.createElement(L.W,{tabIndex:i,className:a},n.createElement(ed,e))}var ey={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!K.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function ev(e){var t=(0,ee.e)(e,ey),r=(0,n.useMemo)(()=>(0,F.aS)(e.children,$.f),[e.children]),i=(0,F.J9)(t,!1),a=(0,n.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:i}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,i]),o=(0,z.G)(e=>I(e,a,r));return n.createElement(n.Fragment,null,n.createElement(q.r,{fn:eu,args:ei(ei({},t),{},{sectors:o})}),n.createElement(ep,eo({},t,{sectors:o})))}class eg extends n.PureComponent{render(){return n.createElement(n.Fragment,null,n.createElement(R.v,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(el,this.props),n.createElement(ev,this.props),this.props.children)}constructor(){super(...arguments),ea(this,"id",(0,Z.NF)("recharts-pie-"))}}ea(eg,"displayName","Pie"),ea(eg,"defaultProps",ey)},553:e=>{var t={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==t.call(e)}},593:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},634:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},662:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,_:()=>s});var n=r(2115),i=r(1037),a=r(6584),o=r(193),l=r(9064),u=()=>{};function c(e){var{legendPayload:t}=e,r=(0,o.j)(),a=(0,i.r)();return(0,n.useEffect)(()=>a?u:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,a,t]),null}function s(e){var{legendPayload:t}=e,r=(0,o.j)(),i=(0,o.G)(a.fz);return(0,n.useEffect)(()=>"centric"!==i&&"radial"!==i?u:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,i,t]),null}},678:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var u=o[0],c=null==u?void 0:l(u.positions,a);if(null!=c)return c;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}}},688:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},706:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},718:(e,t,r)=>{"use strict";r.d(t,{W:()=>a,h:()=>i});var n=r(4106),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),a=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},749:(e,t,r)=>{e.exports=r(3120).isPlainObject},770:(e,t,r)=>{"use strict";r.d(t,{y:()=>eG,L:()=>eZ});var n=r(2115),i=r(3834),a=r(7670),o=r(5658),l=r(4081),u=r(1037),c=["children"],s=()=>{},f=(0,n.createContext)({addErrorBar:s,removeErrorBar:s}),h=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function d(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,c);return n.createElement(h.Provider,{value:r},t)}var p=e=>{var{children:t,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,data:c,stackId:s,hide:h,type:d,barSize:p}=e,[y,v]=n.useState([]),g=(0,n.useCallback)(e=>{v(t=>[...t,e])},[v]),m=(0,n.useCallback)(e=>{v(t=>t.filter(t=>t!==e))},[v]),b=(0,u.r)();return n.createElement(f.Provider,{value:{addErrorBar:g,removeErrorBar:m}},n.createElement(l.p,{type:d,data:c,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,errorBars:y,stackId:s,hide:h,barSize:p,isPanorama:b}),t)};function y(e){var{addErrorBar:t,removeErrorBar:r}=(0,n.useContext)(f);return(0,n.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var v=r(8180),g=r(5888),m=r(950),b=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e){var{direction:t,width:r,dataKey:i,isAnimationActive:l,animationBegin:u,animationDuration:c,animationEasing:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,b),d=(0,o.J9)(f,!1),{data:p,dataPointFormatter:y,xAxisId:g,yAxisId:w,errorBarOffset:O}=(0,n.useContext)(h),P=(0,v.ZI)(g),A=(0,v.gi)(w);if((null==P?void 0:P.scale)==null||(null==A?void 0:A.scale)==null||null==p||"x"===t&&"number"!==P.type)return null;var j=p.map(e=>{var o,f,{x:h,y:p,value:v,errorVal:g}=y(e,i,t);if(!g)return null;var b=[];if(Array.isArray(g)?[o,f]=g:o=f=g,"x"===t){var{scale:w}=P,j=p+O,E=j+r,M=j-r,S=w(v-o),_=w(v+f);b.push({x1:_,y1:E,x2:_,y2:M}),b.push({x1:S,y1:j,x2:_,y2:j}),b.push({x1:S,y1:E,x2:S,y2:M})}else if("y"===t){var{scale:k}=A,T=h+O,C=T-r,D=T+r,N=k(v-o),I=k(v+f);b.push({x1:C,y1:I,x2:D,y2:I}),b.push({x1:T,y1:N,x2:T,y2:I}),b.push({x1:C,y1:N,x2:D,y2:N})}var z="".concat(h+O,"px ").concat(p+O,"px");return n.createElement(a.W,x({className:"recharts-errorBar",key:"bar-".concat(b.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},d),b.map(e=>{var t=l?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(m.i,{from:{transform:"scaleY(0)",transformOrigin:z},to:{transform:"scaleY(1)",transformOrigin:z},begin:u,easing:s,isActive:l,duration:c,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:z}},n.createElement("line",x({},e,{style:t})))}))});return n.createElement(a.W,{className:"recharts-errorBars"},j)}var P=(0,n.createContext)(void 0);function A(e){var{direction:t,children:r}=e;return n.createElement(P.Provider,{value:t},r)}var j={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function E(e){var t,r,i=(t=e.direction,r=(0,n.useContext)(P),null!=t?t:null!=r?r:"x"),{width:a,isAnimationActive:o,animationBegin:l,animationDuration:u,animationEasing:c}=(0,g.e)(e,j);return n.createElement(n.Fragment,null,n.createElement(y,{dataKey:e.dataKey,direction:i}),n.createElement(O,x({},e,{direction:i,width:a,isAnimationActive:o,animationBegin:l,animationDuration:u,animationEasing:c})))}class M extends n.Component{render(){return n.createElement(E,this.props)}}w(M,"defaultProps",j),w(M,"displayName","ErrorBar");var S=r(2977),_=r(6858),k=r.n(_),T=r(7949),C=r(1757),D=r(4707),N=["valueAccessor"],I=["data","dataKey","clockWise","id","textBreakAll"];function z(){return(z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function U(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var B=e=>Array.isArray(e.value)?k()(e.value):e.value;function $(e){var{valueAccessor:t=B}=e,r=U(e,N),{data:i,dataKey:l,clockWise:u,id:c,textBreakAll:s}=r,f=U(r,I);return i&&i.length?n.createElement(a.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,D.uy)(l)?t(e,r):(0,C.kr)(e&&e.payload,l),a=(0,D.uy)(c)?{}:{id:"".concat(c,"-").concat(r)};return n.createElement(T.J,z({},(0,o.J9)(e,!0),f,a,{parentViewBox:e.parentViewBox,value:i,textBreakAll:s,viewBox:T.J.parseViewBox((0,D.uy)(u)?e:L(L({},e),{},{clockWise:u})),key:"label-".concat(r),index:r}))})):null}$.displayName="LabelList",$.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:a}=e,l=(0,o.aS)(a,$).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label,r?!0===r?n.createElement($,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,T.Z)(r)?n.createElement($,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement($,z({data:t},r,{key:"labelList-implicit"})):null:null),...l]:l};var F=r(593),K=r(3439),H=r(8844),Z=["x","y"];function G(){return(G=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function W(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function q(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Z),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),u=parseInt("".concat(t.width||i.width),10);return W(W(W(W(W({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:u,name:t.name,radius:t.radius})}function V(e){return n.createElement(H.y,G({shapeType:"rectangle",propTransformer:q,activeClassName:"recharts-active-bar"},e))}var J=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,D.Et)(e))return e;var i=(0,D.Et)(r)||(0,D.uy)(r);return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},X=r(8895),Q=r(2829),ee=r(193),et=r(6518),er=()=>{var e=(0,ee.j)();return(0,n.useEffect)(()=>(e((0,et.lm)()),()=>{e((0,et.Ch)())})),null},en=r(2775);function ei(e,t){var r,n,i=(0,ee.G)(t=>(0,en.Rl)(t,e)),a=(0,ee.G)(e=>(0,en.sf)(e,t)),o=null!=(r=null==i?void 0:i.allowDataOverflow)?r:en.PU.allowDataOverflow,l=null!=(n=null==a?void 0:a.allowDataOverflow)?n:en.cd.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function ea(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,a=(0,v.oM)(),{needClipX:o,needClipY:l,needClip:u}=ei(t,r);if(!u)return null;var{x:c,y:s,width:f,height:h}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:o?c:c-f/2,y:l?s:s-h/2,width:o?f:2*f,height:l?h:2*h}))}var eo=r(6584),el=r(4106),eu=r(7774),ec=r(5540),es=r(5276),ef=r(6790);function eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ed(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eh(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ep=(e,t,r,n,i)=>i,ey=(e,t,r)=>{var n=null!=r?r:e;if(!(0,D.uy)(n))return(0,D.F4)(n,t,0)},ev=(0,el.Mz)([eo.fz,en.ld,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function eg(e){return null!=e.stackId&&null!=e.dataKey}var em=(0,el.Mz)([ev,es.x3,(e,t,r)=>"horizontal"===(0,eo.fz)(e)?(0,en.BQ)(e,"xAxis",t):(0,en.BQ)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(eg),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:ey(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:ey(t,r,e.barSize)}))]}),eb=(e,t,r,n)=>{var i,a;return"horizontal"===(0,eo.fz)(e)?(i=(0,en.Gx)(e,"xAxis",t,n),a=(0,en.CR)(e,"xAxis",t,n)):(i=(0,en.Gx)(e,"yAxis",r,n),a=(0,en.CR)(e,"yAxis",r,n)),(0,C.Hj)(i,a)},ew=(0,el.Mz)([em,es.JN,es._5,es.gY,(e,t,r,n,i)=>{var a,o,l,u,c=(0,eo.fz)(e),s=(0,es.JN)(e),{maxBarSize:f}=i,h=(0,D.uy)(f)?s:f;return"horizontal"===c?(l=(0,en.Gx)(e,"xAxis",t,n),u=(0,en.CR)(e,"xAxis",t,n)):(l=(0,en.Gx)(e,"yAxis",r,n),u=(0,en.CR)(e,"yAxis",r,n)),null!=(a=null!=(o=(0,C.Hj)(l,u,!0))?o:h)?a:0},eb,(e,t,r,n,i)=>i.maxBarSize],(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=(0,D.F4)(e,r,0,!0),u=[];if((0,ef.H)(n[0].barSize)){var c=!1,s=r/o,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(o-1)*l)>=r&&(f-=(o-1)*l,l=0),f>=r&&s>0&&(c=!0,s*=.9,f=o*s);var h={offset:((r-f)/2|0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:h.offset+h.size+l,size:c?s:null!=(r=t.barSize)?r:0}}];return h=n[n.length-1].position,n},u)}else{var d=(0,D.F4)(t,r,0,!0);r-2*d-(o-1)*l<=0&&(l=0);var p=(r-2*d-(o-1)*l)/o;p>1&&(p>>=0);var y=(0,ef.H)(i)?Math.min(p,i):p;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d+(p+l)*r+(p-y)/2,size:y}}],u)}return a}}(r,n,i!==a?i:a,e,(0,D.uy)(o)?t:o);return i!==a&&null!=l&&(l=l.map(e=>ed(ed({},e),{},{position:ed(ed({},e.position),{},{offset:e.position.offset-i/2})}))),l}),ex=(0,el.Mz)([ew,ep],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),eO=(0,el.Mz)([en.ld,ep],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),eP=(0,el.Mz)([(e,t,r,n)=>"horizontal"===(0,eo.fz)(e)?(0,en.TC)(e,"yAxis",r,n):(0,en.TC)(e,"xAxis",t,n),ep],(e,t)=>{if(!e||(null==t?void 0:t.dataKey)==null)return;var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}}),eA=(0,el.Mz)([ec.HZ,(e,t,r,n)=>(0,en.Gx)(e,"xAxis",t,n),(e,t,r,n)=>(0,en.Gx)(e,"yAxis",r,n),(e,t,r,n)=>(0,en.CR)(e,"xAxis",t,n),(e,t,r,n)=>(0,en.CR)(e,"yAxis",r,n),ex,eo.fz,eu.HS,eb,eP,eO,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,u,c,s,f)=>{var h,{chartData:d,dataStartIndex:p,dataEndIndex:y}=l;if(null!=s&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=u){var{data:v}=s;if(null!=(h=null!=v&&v.length>0?v:null==d?void 0:d.slice(p,y+1)))return eZ({layout:o,barSettings:s,pos:a,bandSize:u,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:c,displayedData:h,offset:e,cells:f})}}),ej=r(2e3),eE=r(662),eM=r(348),eS=["onMouseEnter","onMouseLeave","onClick"],e_=["value","background","tooltipPosition"],ek=["onMouseEnter","onClick","onMouseLeave"];function eT(){return(eT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function eC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eD(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eC(Object(r),!0).forEach(function(t){eN(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eN(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eI(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function ez(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:(0,C.uM)(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function eR(e){var t=(0,ee.G)(ej.A2),{data:r,dataKey:i,background:a,allOtherBarProps:l}=e,{onMouseEnter:u,onMouseLeave:c,onClick:s}=l,f=eI(l,eS),h=(0,X.Cj)(u,i),d=(0,X.Pg)(c),p=(0,X.Ub)(s,i);if(!a||null==r)return null;var y=(0,o.J9)(a,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:l,tooltipPosition:u}=e,c=eI(e,e_);if(!l)return null;var s=h(e,r),v=d(e,r),g=p(e,r),m=eD(eD(eD(eD(eD({option:a,isActive:String(r)===t},c),{},{fill:"#eee"},l),y),(0,K.XC)(f,e,r)),{},{onMouseEnter:s,onMouseLeave:v,onClick:g,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(V,eT({key:"background-bar-".concat(r)},m))}))}function eL(e){var{data:t,props:r,showLabels:i}=e,l=(0,o.J9)(r,!1),{shape:u,dataKey:c,activeBar:s}=r,f=(0,ee.G)(ej.A2),h=(0,ee.G)(ej.Xb),{onMouseEnter:d,onClick:p,onMouseLeave:y}=r,v=eI(r,ek),g=(0,X.Cj)(d,c),m=(0,X.Pg)(y),b=(0,X.Ub)(p,c);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=s&&String(t)===f&&(null==h||c===h),i=eD(eD(eD({},l),e),{},{isActive:r,option:r?s:u,index:t,dataKey:c});return n.createElement(a.W,eT({className:"recharts-bar-rectangle"},(0,K.XC)(v,e,t),{onMouseEnter:g(e,t),onMouseLeave:m(e,t),onClick:b(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(V,i))}),i&&$.renderCallByParent(r,t)):null}function eU(e){var{props:t,previousRectanglesRef:r}=e,{data:i,layout:o,isAnimationActive:l,animationBegin:u,animationDuration:c,animationEasing:s,onAnimationEnd:f,onAnimationStart:h}=t,d=r.current,p=(0,eM.n)(t,"recharts-bar-"),[y,v]=(0,n.useState)(!1),g=(0,n.useCallback)(()=>{"function"==typeof f&&f(),v(!1)},[f]),b=(0,n.useCallback)(()=>{"function"==typeof h&&h(),v(!0)},[h]);return n.createElement(m.i,{begin:u,duration:c,isActive:l,easing:s,from:{t:0},to:{t:1},onAnimationEnd:g,onAnimationStart:b,key:p},e=>{var{t:l}=e,u=1===l?i:i.map((e,t)=>{var r=d&&d[t];if(r){var n=(0,D.Dj)(r.x,e.x),i=(0,D.Dj)(r.y,e.y),a=(0,D.Dj)(r.width,e.width),u=(0,D.Dj)(r.height,e.height);return eD(eD({},e),{},{x:n(l),y:i(l),width:a(l),height:u(l)})}if("horizontal"===o){var c=(0,D.Dj)(0,e.height)(l);return eD(eD({},e),{},{y:e.y+e.height-c,height:c})}var s=(0,D.Dj)(0,e.width)(l);return eD(eD({},e),{},{width:s})});return l>0&&(r.current=u),n.createElement(a.W,null,n.createElement(eL,{props:t,data:u,showLabels:!y}))})}function eB(e){var{data:t,isAnimationActive:r}=e,i=(0,n.useRef)(null);return r&&t&&t.length&&(null==i.current||i.current!==t)?n.createElement(eU,{previousRectanglesRef:i,props:e}):n.createElement(eL,{props:e,data:t,showLabels:!0})}var e$=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,C.kr)(e,t)}};class eF extends n.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:o,xAxisId:l,yAxisId:u,needClip:c,background:s,id:f,layout:h}=this.props;if(e)return null;var d=(0,i.$)("recharts-bar",o),p=(0,D.uy)(f)?this.id:f;return n.createElement(a.W,{className:d},c&&n.createElement("defs",null,n.createElement(ea,{clipPathId:p,xAxisId:l,yAxisId:u})),n.createElement(a.W,{className:"recharts-bar-rectangles",clipPath:c?"url(#clipPath-".concat(p,")"):null},n.createElement(eR,{data:t,dataKey:r,background:s,allOtherBarProps:this.props}),n.createElement(eB,this.props)),n.createElement(A,{direction:"horizontal"===h?"y":"x"},this.props.children))}constructor(){super(...arguments),eN(this,"id",(0,D.NF)("recharts-bar-"))}}var eK={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!F.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function eH(e){var t,{xAxisId:r,yAxisId:i,hide:a,legendType:l,minPointSize:c,activeBar:s,animationBegin:f,animationDuration:h,animationEasing:p,isAnimationActive:y}=(0,g.e)(e,eK),{needClip:v}=ei(r,i),m=(0,eo.WX)(),b=(0,u.r)(),w=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:c,stackId:(0,C.$8)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,c,e.stackId]),x=(0,o.aS)(e.children,S.f),O=(0,ee.G)(e=>eA(e,r,i,b,w,x));if("vertical"!==m&&"horizontal"!==m)return null;var P=null==O?void 0:O[0];return t=null==P||null==P.height||null==P.width?0:"vertical"===m?P.height/2:P.width/2,n.createElement(d,{xAxisId:r,yAxisId:i,data:O,dataPointFormatter:e$,errorBarOffset:t},n.createElement(eF,eT({},e,{layout:m,needClip:v,data:O,xAxisId:r,yAxisId:i,hide:a,legendType:l,minPointSize:c,activeBar:s,animationBegin:f,animationDuration:h,animationEasing:p,isAnimationActive:y})))}function eZ(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:u,yAxisTicks:c,stackedData:s,displayedData:f,offset:h,cells:d}=e,p="horizontal"===t?l:o,y=s?p.scale.domain():null,v=(0,C.DW)({numericAxis:p});return f.map((e,f)=>{s?g=(0,C._f)(s[f],y):Array.isArray(g=(0,C.kr)(e,r))||(g=[v,g]);var p=J(n,0)(g[1],f);if("horizontal"===t){var g,m,b,w,x,O,P,[A,j]=[l.scale(g[0]),l.scale(g[1])];m=(0,C.y2)({axis:o,ticks:u,bandSize:a,offset:i.offset,entry:e,index:f}),b=null!=(P=null!=j?j:A)?P:void 0,w=i.size;var E=A-j;if(x=(0,D.M8)(E)?0:E,O={x:m,y:h.top,width:w,height:h.height},Math.abs(p)>0&&Math.abs(x)<Math.abs(p)){var M=(0,D.sA)(x||p)*(Math.abs(p)-Math.abs(x));b-=M,x+=M}}else{var[S,_]=[o.scale(g[0]),o.scale(g[1])];if(m=S,b=(0,C.y2)({axis:l,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),w=_-S,x=i.size,O={x:h.left,y:b,width:h.width,height:x},Math.abs(p)>0&&Math.abs(w)<Math.abs(p)){var k=(0,D.sA)(w||p)*(Math.abs(p)-Math.abs(w));w+=k}}return eD(eD({},e),{},{x:m,y:b,width:w,height:x,value:s?g:g[1],payload:e,background:O,tooltipPosition:{x:m+w/2,y:b+x/2}},d&&d[f]&&d[f].props)})}class eG extends n.PureComponent{render(){return n.createElement(p,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(er,null),n.createElement(eE.A,{legendPayload:(e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,C.uM)(r,t),payload:e}]})(this.props)}),n.createElement(Q.r,{fn:ez,args:this.props}),n.createElement(eH,this.props))}}eN(eG,"displayName","Bar"),eN(eG,"defaultProps",eK)},870:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(3014),i=r(8751);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},950:(e,t,r)=>{"use strict";r.d(t,{i:()=>M});var n=r(2115),i=r(8138),a=r.n(i),o=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),u=(e,t)=>r=>l(o(e,t),r),c=function(){let e,t;for(var r,n,i,a,c=arguments.length,s=Array(c),f=0;f<c;f++)s[f]=arguments[f];if(1===s.length)switch(s[0]){case"linear":[r,i,n,a]=[0,0,1,1];break;case"ease":[r,i,n,a]=[.25,.1,.25,1];break;case"ease-in":[r,i,n,a]=[.42,0,1,1];break;case"ease-out":[r,i,n,a]=[.42,0,.58,1];break;case"ease-in-out":[r,i,n,a]=[0,0,.58,1];break;default:var h=s[0].split("(");"cubic-bezier"===h[0]&&4===h[1].split(")")[0].split(",").length&&([r,i,n,a]=h[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===s.length&&([r,i,n,a]=s);var d=u(r,n),p=u(i,a),y=(e=r,t=n,r=>l([...o(e,t).map((e,t)=>e*t).slice(1),0],r)),v=e=>e>1?1:e<0?0:e,g=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=d(r)-t,a=y(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=v(r-i/a)}return p(r)};return g.isStepper=!1,g},s=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i};function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d=(e,t)=>Object.keys(t).reduce((r,n)=>h(h({},r),{},{[n]:e(n,t[n])}),{});function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var v=(e,t,r)=>e+(t-e)*r,g=e=>{var{from:t,to:r}=e;return t!==r},m=(e,t,r)=>{var n=d((t,r)=>{if(g(r)){var[n,i]=e(r.from,r.to,r.velocity);return y(y({},r),{},{from:n,velocity:i})}return r},t);return r<1?d((e,t)=>g(t)?y(y({},t),{},{velocity:v(t.velocity,n[e].velocity,r),from:v(t.from,n[e].from,r)}):t,t):m(e,n,r-1)};class b{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var w=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){A(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function A(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class j extends n.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:u}=this.state;if(r){if(!t){this.state&&u&&(n&&u[n]!==o||!n&&u!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(e.to,o)||!e.canBegin||!e.isActive){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||i?l:e.to;this.state&&u&&(n&&u[n]!==s||!n&&u!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(P(P({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var t,r,n,i,a,o,l,u,f,h,p,b,w,x,O,P,A,j,E,M,S,_,k,T,C,{from:D,to:N,duration:I,easing:z,begin:R,onAnimationEnd:L,onAnimationStart:U}=e,B=(_=(e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return c(e);case"spring":return s();default:if("cubic-bezier"===e.split("(")[0])return c(e)}return"function"==typeof e?e:null})(z),k=this.changeStyle,T=this.manager.getTimeoutController(),C=[Object.keys(D),Object.keys(N)].reduce((e,t)=>e.filter(e=>t.includes(e))),!0===_.isStepper?(t=D,r=N,n=_,i=C,a=k,o=T,u=i.reduce((e,n)=>y(y({},e),{},{[n]:{from:t[n],velocity:0,to:r[n]}}),{}),f=null,h=e=>{l||(l=e);var i=(e-l)/n.dt;u=m(n,u,i),a(y(y(y({},t),r),d((e,t)=>t.from,u))),l=e,Object.values(u).filter(g).length&&(f=o.setTimeout(h))},()=>(f=o.setTimeout(h),()=>{f()})):(p=D,b=N,w=_,x=I,O=C,P=k,A=T,E=null,M=O.reduce((e,t)=>y(y({},e),{},{[t]:[p[t],b[t]]}),{}),S=e=>{j||(j=e);var t=(e-j)/x,r=d((e,r)=>v(...r,w(t)),M);if(P(y(y(y({},p),b),r)),t<1)E=A.setTimeout(S);else{var n=d((e,t)=>v(...t,w(1)),M);P(y(y(y({},p),b),n))}},()=>(E=A.setTimeout(S),()=>{E()}))),$=()=>{this.stopJSAnimation=B()};this.manager.start([U,R,$,I,L])}runAnimation(e){let t;var{begin:r,duration:n,attributeName:i,to:a,easing:o,onAnimationStart:l,onAnimationEnd:u,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof o||"function"==typeof c||"spring"===o)return void this.runJSAnimation(e);var s=i?{[i]:a}:a,f=(t=Object.keys(s),t.map(e=>"".concat(e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase()))," ").concat(n,"ms ").concat(o)).join(","));this.manager.start([l,r,P(P({},s),{},{transition:f}),n,u])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:u,to:c,canBegin:s,onAnimationEnd:f,shouldReAnimate:h,onAnimationReStart:d,animationManager:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,w),v=n.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!l||0===v||i<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,P(P({},y),{},{style:P(P({},t),g),className:r}))};return 1===v?m(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>m(e)))}constructor(e,t){super(e,t),A(this,"mounted",!1),A(this,"manager",null),A(this,"stopJSAnimation",null),A(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:u}=this.props;if(this.manager=u,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}A(j,"displayName","Animate"),A(j,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var E=(0,n.createContext)(null);function M(e){var t,r,i,a,o,l,u,c=(0,n.useContext)(E);return n.createElement(j,x({},e,{animationManager:null!=(l=null!=(u=e.animationManager)?u:c)?l:(t=new b,r=()=>null,i=!1,a=null,o=e=>{if(!i){if(Array.isArray(e)){if(!e.length)return;var[n,...l]=e;if("number"==typeof n){a=t.setTimeout(o.bind(null,l),n);return}o(n),a=t.setTimeout(o.bind(null,l));return}"object"==typeof e&&r(e),"function"==typeof e&&e()}},{stop:()=>{i=!0},start:e=>{i=!1,a&&(a(),a=null),o(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>t})}))}},997:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},1037:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var n=r(2115),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},1078:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},1090:(e,t,r)=>{"use strict";var n=r(2700).hp;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(6749),a=r(1343),o=r(9270),l=r(6167),u=r(9578);t.isEqualWith=function(e,t,r){return function e(t,r,c,s,f,h,d){let p=d(t,r,c,s,f,h);if(void 0!==p)return p;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,s,f){if(Object.is(r,c))return!0;let h=o.getTag(r),d=o.getTag(c);if(h===l.argumentsTag&&(h=l.objectTag),d===l.argumentsTag&&(d=l.objectTag),h!==d)return!1;switch(h){case l.stringTag:return r.toString()===c.toString();case l.numberTag:{let e=r.valueOf(),t=c.valueOf();return u.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),c.valueOf());case l.regexpTag:return r.source===c.source&&r.flags===c.flags;case l.functionTag:return r===c}let p=(s=s??new Map).get(r),y=s.get(c);if(null!=p&&null!=y)return p===c;s.set(r,c),s.set(c,r);try{switch(h){case l.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,s,f))return!1;return!0;case l.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,c,s,f));if(-1===o)return!1;n.splice(o,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,s,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case l.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case l.errorTag:return r.name===c.name&&r.message===c.message;case l.objectTag:{if(!(t(r.constructor,c.constructor,s,f)||i.isPlainObject(r)&&i.isPlainObject(c)))return!1;let n=[...Object.keys(r),...a.getSymbols(r)],o=[...Object.keys(c),...a.getSymbols(c)];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],a=r[i];if(!Object.hasOwn(c,i))return!1;let o=c[i];if(!e(a,o,i,r,c,s,f))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(c)}}(t,r,h,d)}(e,t,void 0,void 0,void 0,void 0,r)}},1102:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4184);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},1105:(e,t,r)=>{"use strict";r.d(t,{J:()=>a,U:()=>i});var n=(0,r(7681).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:i}=n.actions,a=n.reducer},1121:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4938),i=r(4227),a=r(8658),o=r(9945);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},1143:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},1145:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},1154:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},1323:(e,t,r)=>{"use strict";r.d(t,{I:()=>$});var n=r(2115);function i(){}function a(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function o(e){this._context=e}function l(e){this._context=e}function u(e){this._context=e}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class c{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function f(e){this._context=e}function h(e){return new f(e)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function d(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function p(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function y(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function v(e){this._context=e}function g(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function w(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function x(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,p(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,y(this,p(this,r=d(this,e,t)),r);break;default:y(this,this._t0,r=d(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(g.prototype=Object.create(v.prototype)).point=function(e,t){v.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=w(e),i=w(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},x.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(8101),P=r(9344),A=r(2785);function j(e){return e[0]}function E(e){return e[1]}function M(e,t){var r=(0,P.A)(!0),n=null,i=h,a=null,o=(0,A.i)(l);function l(l){var u,c,s,f=(l=(0,O.A)(l)).length,h=!1;for(null==n&&(a=i(s=o())),u=0;u<=f;++u)!(u<f&&r(c=l[u],u,l))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(c,u,l),+t(c,u,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?j:(0,P.A)(e),t="function"==typeof t?t:void 0===t?E:(0,P.A)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.A)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.A)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,P.A)(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function S(e,t,r){var n=null,i=(0,P.A)(!0),a=null,o=h,l=null,u=(0,A.i)(c);function c(c){var s,f,h,d,p,y=(c=(0,O.A)(c)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(p=u())),s=0;s<=y;++s){if(!(s<y&&i(d=c[s],s,c))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),h=s-1;h>=f;--h)l.point(g[h],m[h]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+e(d,s,c),m[s]=+t(d,s,c),l.point(n?+n(d,s,c):g[s],r?+r(d,s,c):m[s]))}if(p)return l=null,p+""||null}function s(){return M().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?j:(0,P.A)(+e),t="function"==typeof t?t:void 0===t?(0,P.A)(0):(0,P.A)(+t),r="function"==typeof r?r:void 0===r?E:(0,P.A)(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.A)(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.A)(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,P.A)(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.A)(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.A)(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,P.A)(+e),c):r},c.lineX0=c.lineY0=function(){return s().x(e).y(t)},c.lineY1=function(){return s().x(e).y(r)},c.lineX1=function(){return s().x(n).y(t)},c.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,P.A)(!!e),c):i},c.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),c):o},c.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),c):a},c}var _=r(3834),k=r(3439),T=r(5658),C=r(4707),D=r(6790);function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var R={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new u(e)},curveBasis:function(e){return new o(e)},curveBumpX:function(e){return new c(e,!0)},curveBumpY:function(e){return new c(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:h,curveMonotoneX:function(e){return new v(e)},curveMonotoneY:function(e){return new g(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new x(e,.5)},curveStepAfter:function(e){return new x(e,1)},curveStepBefore:function(e){return new x(e,0)}},L=e=>(0,D.H)(e.x)&&(0,D.H)(e.y),U=e=>e.x,B=e=>e.y,$=e=>{var{className:t,points:r,path:i,pathRef:a}=e;if((!r||!r.length)&&!i)return null;var o=r&&r.length?(e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,C.Zb)(e));return("curveMonotone"===r||"curveBump"===r)&&t?R["".concat(r).concat("vertical"===t?"Y":"X")]:R[r]||h})(r,a),u=o?n.filter(L):n;if(Array.isArray(i)){var c=o?i.filter(e=>L(e)):i,s=u.map((e,t)=>z(z({},e),{},{base:c[t]}));return(t="vertical"===a?S().y(B).x1(U).x0(e=>e.base.x):S().x(U).y1(B).y0(e=>e.base.y)).defined(L).curve(l),t(s)}return(t="vertical"===a&&(0,C.Et)(i)?S().y(B).x1(U).x0(i):(0,C.Et)(i)?S().x(U).y1(B).y0(i):M().x(U).y(B)).defined(L).curve(l),t(u)})(e):i;return n.createElement("path",N({},(0,T.J9)(e,!1),(0,k._U)(e),{className:(0,_.$)("recharts-curve",t),d:null===o?void 0:o,ref:a}))}},1343:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},1407:(e,t,r)=>{"use strict";r.d(t,{$7:()=>f,Ru:()=>s,uZ:()=>c});var n=r(7681),i=r(6948),a=r(2e3),o=r(5934),l=r(2775),u=r(423),c=(0,n.VP)("keyDown"),s=(0,n.VP)("focus"),f=(0,n.Nc)();f.startListening({actionCreator:c,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,c=e.payload;if("ArrowRight"===c||"ArrowLeft"===c||"Enter"===c){var s=Number((0,u.P)(n,(0,a.n4)(r))),f=(0,a.R4)(r);if("Enter"===c){var h=(0,o.pg)(r,"axis","hover",String(n.index));t.dispatch((0,i.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:h}));return}var d=s+("ArrowRight"===c?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=f&&!(d>=f.length)&&!(d<0)){var p=(0,o.pg)(r,"axis","hover",String(d));t.dispatch((0,i.o4)({active:!0,activeIndex:d.toString(),activeDataKey:void 0,activeCoordinate:p}))}}}}}),f.startListening({actionCreator:s,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var a=(0,o.pg)(r,"axis","hover",String("0"));t.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:a}))}}}})},1494:(e,t,r)=>{e.exports=r(8136).throttle},1523:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},1679:(e,t,r)=>{e.exports=r(7536).sortBy},1741:(e,t)=>{"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,a=u(e),o=a[0],l=a[1],c=new i((o+l)*3/4-l),s=0,f=l>0?o-4:o;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[s++]=t>>16&255,c[s++]=t>>8&255,c[s++]=255&t;return 2===l&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[s++]=255&t),1===l&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[s++]=t>>8&255,c[s++]=255&t),c},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,a=[],o=0,l=n-i;o<l;o+=16383)a.push(function(e,t,n){for(var i,a=[],o=t;o<n;o+=3)i=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]),a.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return a.join("")}(e,o,o+16383>l?l:o+16383));return 1===i?a.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&a.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),a.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,l=a.length;o<l;++o)r[o]=a[o],n[a.charCodeAt(o)]=o;function u(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},1757:(e,t,r)=>{"use strict";r.d(t,{qx:()=>C,IH:()=>T,s0:()=>b,gH:()=>m,SW:()=>L,YB:()=>P,bk:()=>R,Hj:()=>D,DW:()=>_,y2:()=>S,PW:()=>x,Mk:()=>k,$8:()=>M,yy:()=>E,Rh:()=>O,GF:()=>N,uM:()=>I,kr:()=>g,r4:()=>z,_L:()=>w,_f:()=>A});var n=r(1679),i=r.n(n),a=r(3758),o=r.n(a);function l(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var u=r(8101),c=r(9344);function s(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function h(e){let t=[];return t.key=e,t}var d=r(4707),p=r(8983);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e,t,r){return(0,d.uy)(e)||(0,d.uy)(t)?r:(0,d.vh)(t)?o()(e,t,r):"function"==typeof t?t(e):r}var m=(e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var u=0;u<l;u++){var c=u>0?r[u-1].coordinate:r[l-1].coordinate,s=r[u].coordinate,f=u>=l-1?r[0].coordinate:r[u+1].coordinate,h=void 0;if((0,d.sA)(s-c)!==(0,d.sA)(f-s)){var p=[];if((0,d.sA)(f-s)===(0,d.sA)(i[1]-i[0])){h=f;var y=s+i[1]-i[0];p[0]=Math.min(y,(y+c)/2),p[1]=Math.max(y,(y+c)/2)}else{h=c;var v=f+i[1]-i[0];p[0]=Math.min(s,(v+s)/2),p[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(h+s)/2),Math.max(s,(h+s)/2)];if(e>g[0]&&e<=g[1]||e>=p[0]&&e<=p[1]){({index:o}=r[u]);break}}else{var m=Math.min(c,f),b=Math.max(c,f);if(e>(m+s)/2&&e<=(b+s)/2){({index:o}=r[u]);break}}}else if(t){for(var w=0;w<l;w++)if(0===w&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w>0&&w<l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w===l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2){({index:o}=t[w]);break}}return o},b=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&(0,d.Et)(e[a]))return v(v({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&(0,d.Et)(e[o]))return v(v({},e),{},{[o]:e[o]+(i||0)})}return e},w=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,x=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},O=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:u,categoricalDomain:c,tickCount:s,ticks:f,niceTicks:h,axisType:p}=e;if(!o)return null;var y="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,v=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/y:0;return(v="angleAxis"===p&&a&&a.length>=2?2*(0,d.sA)(a[0]-a[1])*v:v,t&&(f||h))?(f||h||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+v,value:e,offset:v,index:t})).filter(e=>!(0,d.M8)(e.coordinate)):u&&c?c.map((e,t)=>({coordinate:o(e)+v,value:e,index:t,offset:v})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+v,value:e,offset:v,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+v,value:n?n[e]:e,index:t,offset:v}))},P=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},A=(e,t)=>{if(!t||2!==t.length||!(0,d.Et)(t[0])||!(0,d.Et)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!(0,d.Et)(e[0])||e[0]<r)&&(i[0]=r),(!(0,d.Et)(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},j={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=(0,d.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,u=0;o<r;++o)u+=e[o][n][1]||0;i[n][1]+=i[n][0]=-u/2}l(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var u=0,c=0,s=0;u<i;++u){for(var f=e[t[u]],h=f[o][1]||0,d=(h-(f[o-1][1]||0))/2,p=0;p<u;++p){var y=e[t[p]];d+=(y[o][1]||0)-(y[o-1][1]||0)}c+=h,s+=d*h}r[o-1][1]+=r[o-1][0]=a,c&&(a-=s/c)}r[o-1][1]+=r[o-1][0]=a,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=(0,d.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},E=(e,t,r)=>{var n=j[r];return(function(){var e=(0,c.A)([]),t=s,r=l,n=f;function i(i){var a,o,l=Array.from(e.apply(this,arguments),h),c=l.length,s=-1;for(let e of i)for(a=0,++s;a<c;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=(0,u.A)(t(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,c.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,c.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?s:"function"==typeof e?e:(0,c.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?l:e,i):r},i})().keys(t).value((e,t)=>+g(e,t,0)).order(s).offset(n)(e)};function M(e){return null==e?void 0:String(e)}var S=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=g(a,t.dataKey,t.scale.domain()[o]);return(0,d.uy)(l)?null:t.scale(l)-i/2+n},_=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},k=(e,t,r)=>{if(null!=e)return(e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]])(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=(e=>{var t=e.flat(2).filter(d.Et);return[Math.min(...t),Math.max(...t)]})(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},T=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,C=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,D=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=i()(t,e=>e.coordinate),o=1/0,l=1,u=a.length;l<u;l++){var c=a[l],s=a[l-1];o=Math.min((c.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function N(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return v(v({},t),{},{dataKey:r,payload:n,value:i,name:a})}function I(e,t){return e?String(e):"string"==typeof t?t:void 0}function z(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,p.yy)({x:e,y:t},n):null}var R=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:u}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,l,u)),{},{angle:u,radius:l})}return{x:0,y:0}},L=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},1864:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},1901:(e,t,r)=>{"use strict";r.d(t,{B_:()=>i,JK:()=>a,Vp:()=>u,gX:()=>o,hF:()=>l});var n=(0,r(7681).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:i,setLayout:a,setChartSize:o,setScale:l}=n.actions,u=n.reducer},1978:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>c,a6:()=>s,h4:()=>Z,jM:()=>H,ss:()=>F});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function l(e){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var u=Object.getPrototypeOf;function c(e){return!!e&&!!e[o]}function s(e){return!!e&&(h(e)||Array.isArray(e)||!!e[a]||!!e.constructor?.[a]||g(e)||m(e))}var f=Object.prototype.constructor.toString();function h(e){if(!e||"object"!=typeof e)return!1;let t=u(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function d(e,t){0===p(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function p(e){let t=e[o];return t?t.type_:Array.isArray(e)?1:g(e)?2:3*!!m(e)}function y(e,t){return 2===p(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function v(e,t,r){let n=p(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function g(e){return e instanceof Map}function m(e){return e instanceof Set}function b(e){return e.copy_||e.base_}function w(e,t){if(g(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=h(e);if(!0!==t&&("class_only"!==t||r)){let t=u(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[o];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(u(e),t)}}function x(e,t=!1){return P(e)||c(e)||!s(e)||(p(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>x(t,!0))),e}function O(){l(2)}function P(e){return Object.isFrozen(e)}var A={};function j(e){let t=A[e];return t||l(0,e),t}function E(e,t){t&&(j("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function M(e){S(e),e.drafts_.forEach(k),e.drafts_=null}function S(e){e===n&&(n=e.parent_)}function _(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function k(e){let t=e[o];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function T(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[o].modified_&&(M(t),l(4)),s(e)&&(e=C(t,e),t.parent_||N(t,e)),t.patches_&&j("Patches").generateReplacementPatches_(r[o].base_,e,t.patches_,t.inversePatches_)):e=C(t,r,[]),M(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function C(e,t,r){if(P(t))return t;let n=t[o];if(!n)return d(t,(i,a)=>D(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return N(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),d(i,(i,o)=>D(e,n,t,i,o,r,a)),N(e,t,!1),r&&e.patches_&&j("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function D(e,t,r,n,i,a,o){if(c(i)){let o=C(e,i,a&&t&&3!==t.type_&&!y(t.assigned_,n)?a.concat(n):void 0);if(v(r,n,o),!c(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(s(i)&&!P(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;C(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(e,i)}}function N(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&x(t,r)}var I={get(e,t){if(t===o)return e;let r=b(e);if(!y(r,t)){var n=e,i=r,a=t;let o=L(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let l=r[t];return e.finalized_||!s(l)?l:l===R(e.base_,t)?(B(e),e.copy_[t]=$(l,e)):l},has:(e,t)=>t in b(e),ownKeys:e=>Reflect.ownKeys(b(e)),set(e,t,r){let n=L(b(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=R(b(e),t),i=n?.[o];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(e.base_,t)))return!0;B(e),U(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==R(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,B(e),U(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){l(11)},getPrototypeOf:e=>u(e.base_),setPrototypeOf(){l(12)}},z={};function R(e,t){let r=e[o];return(r?b(r):e)[t]}function L(e,t){if(!(t in e))return;let r=u(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=u(r)}}function U(e){!e.modified_&&(e.modified_=!0,e.parent_&&U(e.parent_))}function B(e){e.copy_||(e.copy_=w(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function $(e,t){let r=g(e)?j("MapSet").proxyMap_(e,t):m(e)?j("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=I;r&&(a=[i],o=z);let{revoke:l,proxy:u}=Proxy.revocable(a,o);return i.draft_=u,i.revoke_=l,u}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function F(e){return c(e)||l(10,e),function e(t){let r;if(!s(t)||P(t))return t;let n=t[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=w(t,n.scope_.immer_.useStrictShallowCopy_)}else r=w(t,!0);return d(r,(t,n)=>{v(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}d(I,(e,t)=>{z[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),z.deleteProperty=function(e,t){return z.set.call(this,e,t,void 0)},z.set=function(e,t,r){return I.set.call(this,e[0],t,r,e[0])};var K=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&l(6),void 0!==r&&"function"!=typeof r&&l(7),s(e)){let i=_(this),a=$(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?M(i):S(i)}return E(i,r),T(n,i)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&x(n,!0),r){let t=[],i=[];j("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){s(e)||l(8),c(e)&&(e=F(e));let t=_(this),r=$(e,void 0);return r[o].isManual_=!0,S(t),r}finishDraft(e,t){let r=e&&e[o];r&&r.isManual_||l(9);let{scope_:n}=r;return E(n,t),T(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=j("Patches").applyPatches_;return c(e)?n(e,t):this.produce(e,e=>n(e,t))}},H=K.produce;function Z(e){return e}K.produceWithPatches.bind(K),K.setAutoFreeze.bind(K),K.setUseStrictShallowCopy.bind(K),K.applyPatches.bind(K),K.createDraft.bind(K),K.finishDraft.bind(K)},2e3:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>ea,eE:()=>ec,Xb:()=>eo,A2:()=>ei,yn:()=>es,Dn:()=>A,gL:()=>q,fl:()=>V,R4:()=>Q,Re:()=>O,n4:()=>k});var n=r(4106),i=r(2775),a=r(6584),o=r(1757),l=r(7774),u=r(5276),c=r(4707),s=r(6100),f=r(3429),h=r(3300),d=r(7718),p=r(423),y=r(678),v=r(63),g=r(5540),m=r(8292),b=r(4084),w=r(3984),x=r(4403),O=e=>{var t=(0,a.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},P=e=>e.tooltip.settings.axisId,A=e=>{var t=O(e),r=P(e);return(0,i.Hd)(e,t,r)},j=(0,n.Mz)([A,a.fz,i.um,u.iO,O],i.sr),E=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),M=(0,n.Mz)([O,P],i.eo),S=(0,n.Mz)([E,A,M],i.ec),_=(0,n.Mz)([S],i.rj),k=(0,n.Mz)([_,l.LF],i.Nk),T=(0,n.Mz)([k,A,S],i.fb),C=(0,n.Mz)([A],i.S5),D=(0,n.Mz)([k,S,u.eC],i.MK),N=(0,n.Mz)([D,l.LF,O],i.pM),I=(0,n.Mz)([S],i.IO),z=(0,n.Mz)([k,A,I,O],i.kz),R=(0,n.Mz)([i.Kr,O,P],i.P9),L=(0,n.Mz)([R,O],i.Oz),U=(0,n.Mz)([i.gT,O,P],i.P9),B=(0,n.Mz)([U,O],i.q),$=(0,n.Mz)([i.$X,O,P],i.P9),F=(0,n.Mz)([$,O],i.bb),K=(0,n.Mz)([L,F,B],i.yi),H=(0,n.Mz)([A,C,N,z,K],i.wL),Z=(0,n.Mz)([A,a.fz,k,T,u.eC,O,H],i.tP),G=(0,n.Mz)([Z,A,j],i.xp),Y=(0,n.Mz)([A,Z,G,O],i.g1),W=e=>{var t=O(e),r=P(e);return(0,i.D5)(e,t,r,!1)},q=(0,n.Mz)([A,W],s.I),V=(0,n.Mz)([A,j,Y,q],i.Qn),J=(0,n.Mz)([a.fz,T,A,O],i.tF),X=(0,n.Mz)([a.fz,T,A,O],i.iv),Q=(0,n.Mz)([a.fz,A,j,V,W,J,X,O],(e,t,r,n,i,a,l,u)=>{if(t){var{type:s}=t,f=(0,o._L)(e,u);if(n){var h="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,d="category"===s&&n.bandwidth?n.bandwidth()/h:0;return(d="angleAxis"===u&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,c.sA)(i[0]-i[1])*d:d,f&&l)?l.map((e,t)=>({coordinate:n(e)+d,value:e,index:t,offset:d})):n.domain().map((e,t)=>({coordinate:n(e)+d,value:a?a[e]:e,index:t,offset:d}))}}}),ee=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),et=e=>e.tooltip.settings.trigger,er=e=>e.tooltip.settings.defaultIndex,en=(0,n.Mz)([w.J,ee,et,er],d.i),ei=(0,n.Mz)([en,k],p.P),ea=(0,n.Mz)([Q,ei],h.E),eo=(0,n.Mz)([en],e=>{if(e)return e.dataKey}),el=(0,n.Mz)([w.J,ee,et,er],m.q),eu=(0,n.Mz)([v.Lp,v.A$,a.fz,g.HZ,Q,er,el,b.x],y.o),ec=(0,n.Mz)([en,eu],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),es=(0,n.Mz)([en],e=>e.active),ef=(0,n.Mz)([el,ei,l.LF,A,ea,b.x,ee],x.N);(0,n.Mz)([ef],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))})},2038:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(3834),i=r(2115),a=r(1494),o=r.n(a),l=r(4707),u=r(1145);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:c="100%",height:f="100%",minWidth:h=0,minHeight:d,maxHeight:p,children:y,debounce:v=0,id:g,className:m,onResize:b,style:w={}}=e,x=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>x.current);var[P,A]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),j=(0,i.useCallback)((e,t)=>{A(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;j(r,n),null==(t=O.current)||t.call(O,r,n)};v>0&&(e=o()(e,v,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=x.current.getBoundingClientRect();return j(r,n),t.observe(x.current),()=>{t.disconnect()}},[j,v]);var E=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=P;if(e<0||t<0)return null;(0,u.R)((0,l._3)(c)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,f),(0,u.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(c)?e:c,a=(0,l._3)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),p&&a>p&&(a=p)),(0,u.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,c,f,h,d,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:a,style:s({width:n,height:a},e.props.style)}))},[r,y,f,p,d,h,P,c]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.$)("recharts-responsive-container",m),style:s(s({},w),{},{width:c,height:f,minWidth:h,minHeight:d,maxHeight:p}),ref:x},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},E))})},2204:(e,t,r)=>{"use strict";r.d(t,{m:()=>en});var n=r(2115),i=r(7650),a=r(1679),o=r.n(a),l=r(3834),u=r(4707);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e){return Array.isArray(e)&&(0,u.vh)(e[0])&&(0,u.vh)(e[1])?e.join(" ~ "):e}var d=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:d,itemSorter:p,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=e,w=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),x=f({margin:0},a),O=!(0,u.uy)(g),P=O?g:"",A=(0,l.$)("recharts-default-tooltip",y),j=(0,l.$)("recharts-tooltip-label",v);return O&&m&&null!=s&&(P=m(g,s)),n.createElement("div",c({className:A,style:w},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:j,style:x},n.isValidElement(P)?P:"".concat(P)),(()=>{if(s&&s.length){var e=(p?o()(s,p):s).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||d||h,{value:o,name:l}=e,c=o,p=l;if(a){var y=a(o,l,e,r,s);if(Array.isArray(y))[c,p]=y;else{if(null==y)return null;c=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,u.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,u.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},p="recharts-tooltip-wrapper",y={visibility:"hidden"};function v(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:s}=e;if(a&&(0,u.Et)(a[n]))return a[n];var f=r[n]-l-(i>0?i:0),h=r[n]+i;if(t[n])return o[n]?f:h;var d=c[n];return null==d?0:o[n]?f<d?Math.max(h,d):Math.max(f,d):null==s?0:h+l>d+s?Math.max(f,d):Math.max(h,d)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class w extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:c,isAnimationActive:s,offset:f,position:h,reverseDirection:d,useTranslate3d:g,viewBox:b,wrapperStyle:w,lastBoundingBox:x,innerRef:O,hasPortalFromProps:P}=this.props,{cssClasses:A,cssProperties:j}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:c,reverseDirection:s,tooltipBox:f,useTranslate3d:h,viewBox:d}=e;return{cssProperties:t=f.height>0&&f.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=v({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.width,viewBox:d,viewBoxDimension:d.width}),translateY:n=v({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.height,viewBox:d,viewBoxDimension:d.height}),useTranslate3d:h}):y,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,l.$)(p,{["".concat(p,"-right")]:(0,u.Et)(r)&&t&&(0,u.Et)(t.x)&&r>=t.x,["".concat(p,"-left")]:(0,u.Et)(r)&&t&&(0,u.Et)(t.x)&&r<t.x,["".concat(p,"-bottom")]:(0,u.Et)(n)&&t&&(0,u.Et)(t.y)&&n>=t.y,["".concat(p,"-top")]:(0,u.Et)(n)&&t&&(0,u.Et)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:f,position:h,reverseDirection:d,tooltipBox:{height:x.height,width:x.width},useTranslate3d:g,viewBox:b}),E=P?{}:m(m({transition:s&&e?"transform ".concat(r,"ms ").concat(i):void 0},j),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&c?"visible":"hidden",position:"absolute",top:0,left:0}),M=m(m({},E),{},{visibility:!this.state.dismissed&&e&&c?"visible":"hidden"},w);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:A,style:M,ref:O},a)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}}var x=r(593),O=r(3006),P=r.n(O),A=r(6584),j=r(5378),E=r(1323),M=r(5658),S=["x","y","top","left","width","height","className"];function _(){return(_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var T=e=>{var{x:t=0,y:r=0,top:i=0,left:a=0,width:o=0,height:c=0,className:s}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:a,width:o,height:c},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,S));return(0,u.Et)(t)&&(0,u.Et)(r)&&(0,u.Et)(o)&&(0,u.Et)(c)&&(0,u.Et)(i)&&(0,u.Et)(a)?n.createElement("path",_({},(0,M.J9)(f,!0),{className:(0,l.$)("recharts-cross",s),d:"M".concat(t,",").concat(i,"v").concat(c,"M").concat(a,",").concat(r,"h").concat(o)})):null},C=r(4144),D=r(8983);function N(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[(0,D.IZ)(t,r,n,i),(0,D.IZ)(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}var I=r(4597),z=r(193),R=r(1757),L=r(2e3);function U(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?U(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var $=r(5934);function F(){return(F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Z(e){var t,r,i,{coordinate:a,payload:o,index:u,offset:c,tooltipAxisBandSize:s,layout:f,cursor:h,tooltipEventType:d,chartName:p}=e;if(!h||!a||"ScatterChart"!==p&&"axis"!==d)return null;if("ScatterChart"===p)r=a,i=T;else if("BarChart"===p)t=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-t:c.left+.5,y:"horizontal"===f?c.top+.5:a.y-t,width:"horizontal"===f?s:c.width-1,height:"horizontal"===f?c.height-1:s},i=C.M;else if("radial"===f){var{cx:y,cy:v,radius:g,startAngle:m,endAngle:b}=N(a);r={cx:y,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=I.h}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return N(t);else{var{cx:l,cy:u,innerRadius:c,outerRadius:s,angle:f}=t,h=(0,D.IZ)(l,u,c,f),d=(0,D.IZ)(l,u,s,f);n=h.x,i=h.y,a=d.x,o=d.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,c)},i=E.I;var w="object"==typeof h&&"className"in h?h.className:void 0,x=H(H(H(H({stroke:"#ccc",pointerEvents:"none"},c),r),(0,M.J9)(h,!1)),{},{payload:o,payloadIndex:u,className:(0,l.$)("recharts-tooltip-cursor",w)});return(0,n.isValidElement)(h)?(0,n.cloneElement)(h,x):(0,n.createElement)(i,x)}function G(e){var t,r,i,a=(t=(0,z.G)(L.Dn),r=(0,z.G)(L.R4),i=(0,z.G)(L.fl),(0,R.Hj)(B(B({},t),{},{scale:i}),r)),o=(0,A.W7)(),l=(0,A.WX)(),u=(0,$.fW)();return n.createElement(Z,F({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:o,layout:l,tooltipAxisBandSize:a,chartName:u}))}var Y=r(6609),W=r(6948),q=r(4341),V=r(3429),J=r(5888);function X(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?X(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ee(e){return e.dataKey}var et=[],er={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!x.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function en(e){var t,r,a=(0,J.e)(e,er),{active:o,allowEscapeViewBox:l,animationDuration:u,animationEasing:c,content:s,filterNull:f,isAnimationActive:h,offset:p,payloadUniqBy:y,position:v,reverseDirection:g,useTranslate3d:m,wrapperStyle:b,cursor:x,shared:O,trigger:E,defaultIndex:M,portal:S,axisId:_}=a,k=(0,z.j)(),T="number"==typeof M?String(M):M;(0,n.useEffect)(()=>{k((0,W.UF)({shared:O,trigger:E,axisId:_,active:o,defaultIndex:T}))},[k,O,E,_,o,T]);var C=(0,A.sk)(),D=(0,j.$)(),N=(0,V.Td)(O),{activeIndex:I,isActive:R}=(0,z.G)(e=>(0,$.yn)(e,N,E,T)),L=(0,z.G)(e=>(0,$.u9)(e,N,E,T)),U=(0,z.G)(e=>(0,$.BZ)(e,N,E,T)),B=(0,z.G)(e=>(0,$.dS)(e,N,E,T)),F=(0,Y.X)(),K=null!=o?o:R,[H,Z]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}([L,K]),X="axis"===N?U:void 0;(0,q.m7)(N,E,B,X,I,K);var en=null!=S?S:F;if(null==en)return null;var ei=null!=L?L:et;K||(ei=et),f&&ei.length&&(t=L.filter(e=>null!=e.value&&(!0!==e.hide||a.includeHidden)),ei=!0===y?P()(t,ee):"function"==typeof y?P()(t,y):t);var ea=ei.length>0,eo=n.createElement(w,{allowEscapeViewBox:l,animationDuration:u,animationEasing:c,isAnimationActive:h,active:K,coordinate:B,hasPayload:ea,offset:p,position:v,reverseDirection:g,useTranslate3d:m,viewBox:C,wrapperStyle:b,lastBoundingBox:H,innerRef:Z,hasPortalFromProps:!!S},(r=Q(Q({},a),{},{payload:ei,label:X,active:K,coordinate:B,accessibilityLayer:D}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(d,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(eo,en),K&&n.createElement(G,{cursor:x,tooltipEventType:N,coordinate:B,payload:L,index:I}))}},2216:function(e,t,r){var n;!function(i){"use strict";var a,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,u="[DecimalError] ",c=u+"Invalid argument: ",s=u+"Exponent out of range: ",f=Math.floor,h=Math.pow,d=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,p=f(1286742750677284.5),y={};function v(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,h=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?E(t,h):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),a>(u=(o=Math.ceil(h/7))>u?o+1:u+1)&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((u=c.length)-(a=s.length)<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/1e7|0,c[a]%=1e7;for(r&&(c.unshift(r),++i),u=c.length;0==c[--u];)c.pop();return t.d=c,t.e=i,l?E(t,h):t}function g(e,t,r){if(e!==~~e||e<t||e>r)throw Error(c+e)}function m(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=P(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=P(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return b(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return E(b(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return x(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(a))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(a)?new r(0):(l=!1,t=b(A(this,i),A(e,i),i),l=!0,E(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?M(this,e):v(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(u+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):E(new r(this),n)},y.naturalExponential=y.exp=function(){return w(this)},y.naturalLogarithm=y.ln=function(){return A(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?v(this,e):M(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(c+e);if(t=x(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,i,a,o,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(u+"NaN")}for(e=x(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=m(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new c(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(i.toString()),i=o=(r=c.precision)+3;;)if(n=(a=n).plus(b(this,a,o+2)).times(.5),m(a.d).slice(0,o)===(t=m(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(E(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return l=!0,E(n,r)},y.times=y.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this.constructor,h=this.d,d=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(c=h.length)<(s=d.length)&&(a=h,h=d,d=a,o=c,c=s,s=o),a=[],n=o=c+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+d[n]*h[i-n-1]+t,a[i--]=u%1e7|0,t=u/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l?E(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(g(e,0,1e9),void 0===t?t=n.rounding:g(t,0,8),E(r,e+x(r)+1,t))},y.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=S(n,!0):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=S(n=E(new i(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?S(this):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=S((n=E(new i(this),e+x(this)+1,t)).abs(),!1,e+x(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return E(new e(this),x(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,i,o,c,s=this,h=s.constructor,d=+(e=new h(e));if(!e.s)return new h(a);if(!(s=new h(s)).s){if(e.s<1)throw Error(u+"Infinity");return s}if(s.eq(a))return s;if(n=h.precision,e.eq(a))return E(s,n);if(c=(t=e.e)>=(r=e.d.length-1),o=s.s,c){if((r=d<0?-d:d)<=0x1fffffffffffff){for(i=new h(a),t=Math.ceil(n/7+4),l=!1;r%2&&_((i=i.times(s)).d,t),0!==(r=f(r/2));)_((s=s.times(s)).d,t);return l=!0,e.s<0?new h(a).div(i):E(i,n)}}else if(o<0)throw Error(u+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,l=!1,i=e.times(A(s,n+12)),l=!0,(i=w(i)).s=o,i},y.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=x(i),n=S(i,r<=a.toExpNeg||r>=a.toExpPos)):(g(e,1,1e9),void 0===t?t=a.rounding:g(t,0,8),r=x(i=E(new a(i),e,t)),n=S(i,e<=r||r<=a.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(g(e,1,1e9),void 0===t?t=r.rounding:g(t,0,8)),E(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=x(this),t=this.constructor;return S(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,c,s,f,h,d,p,y,v,g,m,b,w,O,P,A,j,M,S=n.constructor,_=n.s==i.s?1:-1,k=n.d,T=i.d;if(!n.s)return new S(n);if(!i.s)throw Error(u+"Division by zero");for(s=0,c=n.e-i.e,j=T.length,P=k.length,y=(p=new S(_)).d=[];T[s]==(k[s]||0);)++s;if(T[s]>(k[s]||0)&&--c,(b=null==a?a=S.precision:o?a+(x(n)-x(i))+1:a)<0)return new S(0);if(b=b/7+2|0,s=0,1==j)for(f=0,T=T[0],b++;(s<P||f)&&b--;s++)w=1e7*f+(k[s]||0),y[s]=w/T|0,f=w%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=e(T,f),k=e(k,f),j=T.length,P=k.length),O=j,g=(v=k.slice(0,j)).length;g<j;)v[g++]=0;(M=T.slice()).unshift(0),A=T[0],T[1]>=1e7/2&&++A;do f=0,(l=t(T,v,j,g))<0?(m=v[0],j!=g&&(m=1e7*m+(v[1]||0)),(f=m/A|0)>1?(f>=1e7&&(f=1e7-1),d=(h=e(T,f)).length,g=v.length,1==(l=t(h,v,d,g))&&(f--,r(h,j<d?M:T,d))):(0==f&&(l=f=1),h=T.slice()),(d=h.length)<g&&h.unshift(0),r(v,h,g),-1==l&&(g=v.length,(l=t(T,v,j,g))<1&&(f++,r(v,j<g?M:T,g))),g=v.length):0===l&&(f++,v=[0]),y[s++]=f,l&&v[0]?v[g++]=k[O]||0:(v=[k[O]],g=1);while((O++<P||void 0!==v[0])&&b--)}return y[0]||y.shift(),p.e=c,E(p,o?a+x(p)+1:a)}}();function w(e,t){var r,n,i,o,u,c=0,f=0,d=e.constructor,p=d.precision;if(x(e)>16)throw Error(s+x(e));if(!e.s)return new d(a);for(null==t?(l=!1,u=p):u=t,o=new d(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(u+=Math.log(h(2,f))/Math.LN10*2+5|0,r=n=i=new d(a),d.precision=u;;){if(n=E(n.times(e),u),r=r.times(++c),m((o=i.plus(b(n,r,u))).d).slice(0,u)===m(i.d).slice(0,u)){for(;f--;)i=E(i.times(i),u);return d.precision=p,null==t?(l=!0,E(i,p)):i}i=o}}function x(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(u+"LN10 precision limit exceeded");return E(new e(e.LN10),t)}function P(e){for(var t="";e--;)t+="0";return t}function A(e,t){var r,n,i,o,c,s,f,h,d,p=1,y=e,v=y.d,g=y.constructor,w=g.precision;if(y.s<1)throw Error(u+(y.s?"NaN":"-Infinity"));if(y.eq(a))return new g(0);if(null==t?(l=!1,h=w):h=t,y.eq(10))return null==t&&(l=!0),O(g,h);if(g.precision=h+=10,n=(r=m(v)).charAt(0),!(15e14>Math.abs(o=x(y))))return f=O(g,h+2,w).times(o+""),y=A(new g(n+"."+r.slice(1)),h-10).plus(f),g.precision=w,null==t?(l=!0,E(y,w)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((y=y.times(e)).d)).charAt(0),p++;for(o=x(y),n>1?(y=new g("0."+r),o++):y=new g(n+"."+r.slice(1)),s=c=y=b(y.minus(a),y.plus(a),h),d=E(y.times(y),h),i=3;;){if(c=E(c.times(d),h),m((f=s.plus(b(c,new g(i),h))).d).slice(0,h)===m(s.d).slice(0,h))return s=s.times(2),0!==o&&(s=s.plus(O(g,h+2,w).times(o+""))),s=b(s,new g(p),h),g.precision=w,null==t?(l=!0,E(s,w)):s;s=f,i+=2}}function j(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>p||e.e<-p))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function E(e,t,r){var n,i,a,o,u,c,d,y,v=e.d;for(o=1,a=v[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,d=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=v.length))return e;for(o=1,d=a=v[y];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(u=d/(a=h(10,o-i-1))%10|0,c=t<0||void 0!==v[y+1]||d%a,c=r<4?(u||c)&&(0==r||r==(e.s<0?3:2)):u>5||5==u&&(4==r||c||6==r&&(n>0?i>0?d/h(10,o-i):0:v[y-1])%10&1||r==(e.s<0?8:7))),t<1||!v[0])return c?(a=x(e),v.length=1,t=t-a-1,v[0]=h(10,(7-t%7)%7),e.e=f(-t/7)||0):(v.length=1,v[0]=e.e=e.s=0),e;if(0==n?(v.length=y,a=1,y--):(v.length=y+1,a=h(10,7-n),v[y]=i>0?(d/h(10,o-i)%h(10,i)|0)*a:0),c)for(;;)if(0==y){1e7==(v[0]+=a)&&(v[0]=1,++e.e);break}else{if(v[y]+=a,1e7!=v[y])break;v[y--]=0,a=1}for(n=v.length;0===v[--n];)v.pop();if(l&&(e.e>p||e.e<-p))throw Error(s+x(e));return e}function M(e,t){var r,n,i,a,o,u,c,s,f,h,d=e.constructor,p=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),l?E(t,p):t;if(c=e.d,h=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n){for((f=o<0)?(r=c,o=-o,u=h.length):(r=h,n=s,u=c.length),o>(i=Math.max(Math.ceil(p/7),u)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=c.length)<(u=h.length))&&(u=i),i=0;i<u;i++)if(c[i]!=h[i]){f=c[i]<h[i];break}o=0}for(f&&(r=c,c=h,h=r,t.s=-t.s),u=c.length,i=h.length-u;i>0;--i)c[u++]=0;for(i=h.length;i>o;){if(c[--i]<h[i]){for(a=i;a&&0===c[--a];)c[a]=1e7-1;--c[a],c[i]+=1e7}c[i]-=h[i]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,l?E(t,p):t):new d(0)}function S(e,t,r){var n,i=x(e),a=m(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+P(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+P(-i-1)+a,r&&(n=r-o)>0&&(a+=P(n))):i>=o?(a+=P(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+P(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=P(n))),e.s<0?"-"+a:a}function _(e,t){if(e.length>t)return e.length=t,!0}function k(e){if(!e||"object"!=typeof e)throw Error(u+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(c+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(c+r+": "+n);return this}(o=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(c+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return j(this,e.toString())}if("string"!=typeof e)throw Error(c+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,d.test(e))j(this,e);else throw Error(c+e)}if(a.prototype=y,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=k,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o)).default=o.Decimal=o,a=new o(1),void 0===(n=(function(){return o}).call(t,r,t,e))||(e.exports=n)}(0)},2393:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2806),i=r(688),a=r(2621);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},2470:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(4620).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},2479:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(4620).A)("wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},2577:(e,t,r)=>{"use strict";r.d(t,{f:()=>h});var n=r(4707),i=r(9471),a=r(593);class o{static create(e){return new o(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}!function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):e[t]=1e-4}(o,"EPS",1e-4);var l=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function u(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function c(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t,r){var o,{tick:s,ticks:h,viewBox:d,minTickGap:p,orientation:y,interval:v,tickFormatter:g,unit:m,angle:b}=e;if(!h||!h.length||!s)return[];if((0,n.Et)(v)||a.m.isSsr)return null!=(o=u(h,((0,n.Et)(v)?v:0)+1))?o:[];var w="top"===y||"bottom"===y?"width":"height",x=m&&"width"===w?(0,i.P)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},O=(e,n)=>{var a,o="function"==typeof g?g(e.value,n):e.value;return"width"===w?(a=(0,i.P)(o,{fontSize:t,letterSpacing:r}),l({width:a.width+x.width,height:a.height+x.height},b)):(0,i.P)(o,{fontSize:t,letterSpacing:r})[w]},P=h.length>=2?(0,n.sA)(h[1].coordinate-h[0].coordinate):1,A=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(d,P,w);return"equidistantPreserveStart"===v?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:s}=t,f=0,h=1,d=l;h<=o.length;)if(a=function(){var t,a=null==n?void 0:n[f];if(void 0===a)return{v:u(n,h)};var o=f,p=()=>(void 0===t&&(t=r(a,o)),t),y=a.coordinate,v=0===f||c(e,y,p,d,s);v||(f=0,d=l,h+=1),v&&(d=y+e*(p()/2+i),f+=h)}())return a.v;return[]}(P,A,O,h,p):("preserveStart"===v||"preserveStartEnd"===v?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:u,end:s}=t;if(a){var h=n[l-1],d=r(h,l-1),p=e*(h.coordinate+e*d/2-s);o[l-1]=h=f(f({},h),{},{tickCoord:p>0?h.coordinate-p*e:h.coordinate}),c(e,h.tickCoord,()=>d,u,s)&&(s=h.tickCoord-e*(d/2+i),o[l-1]=f(f({},h),{},{isShow:!0}))}for(var y=a?l-1:l,v=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var h=e*(a.coordinate-e*l()/2-u);o[t]=a=f(f({},a),{},{tickCoord:h<0?a.coordinate-h*e:a.coordinate})}else o[t]=a=f(f({},a),{},{tickCoord:a.coordinate});c(e,a.tickCoord,l,u,s)&&(u=a.tickCoord+e*(l()/2+i),o[t]=f(f({},a),{},{isShow:!0}))},g=0;g<y;g++)v(g);return o}(P,A,O,h,p,"preserveStartEnd"===v):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:u}=t,s=function(t){var n,s=a[t],h=()=>(void 0===n&&(n=r(s,t)),n);if(t===o-1){var d=e*(s.coordinate+e*h()/2-u);a[t]=s=f(f({},s),{},{tickCoord:d>0?s.coordinate-d*e:s.coordinate})}else a[t]=s=f(f({},s),{},{tickCoord:s.coordinate});c(e,s.tickCoord,h,l,u)&&(u=s.tickCoord-e*(h()/2+i),a[t]=f(f({},s),{},{isShow:!0}))},h=o-1;h>=0;h--)s(h);return a}(P,A,O,h,p)).filter(e=>e.isShow)}},2621:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(6238);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},2683:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},2700:(e,t,r)=>{"use strict";var n=r(1741),i=r(7016),a=r(553);function o(){return u.TYPED_ARRAY_SUPPORT?0x7fffffff:0x3fffffff}function l(e,t){if(o()<t)throw RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=u.prototype:(null===e&&(e=new u(t)),e.length=t),e}function u(e,t,r){if(!u.TYPED_ARRAY_SUPPORT&&!(this instanceof u))return new u(e,t,r);if("number"==typeof e){if("string"==typeof t)throw Error("If encoding is specified then the first argument must be a string");return f(this,e)}return c(this,e,t,r)}function c(e,t,r,n){if("number"==typeof t)throw TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw RangeError("'length' is out of bounds");return t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n),u.TYPED_ARRAY_SUPPORT?(e=t).__proto__=u.prototype:e=h(e,t),e}(e,t,r,n):"string"==typeof t?function(e,t,r){if(("string"!=typeof r||""===r)&&(r="utf8"),!u.isEncoding(r))throw TypeError('"encoding" must be a valid string encoding');var n=0|p(t,r),i=(e=l(e,n)).write(t,r);return i!==n&&(e=e.slice(0,i)),e}(e,t,r):function(e,t){if(u.isBuffer(t)){var r,n=0|d(t.length);return 0===(e=l(e,n)).length||t.copy(e,0,0,n),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t){return"number"!=typeof t.length||(r=t.length)!=r?l(e,0):h(e,t)}if("Buffer"===t.type&&a(t.data))return h(e,t.data)}throw TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function s(e){if("number"!=typeof e)throw TypeError('"size" argument must be a number');if(e<0)throw RangeError('"size" argument must not be negative')}function f(e,t){if(s(t),e=l(e,t<0?0:0|d(t)),!u.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function h(e,t){var r=t.length<0?0:0|d(t.length);e=l(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function d(e){if(e>=o())throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|e}function p(e,t){if(u.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return S(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return k(e).length;default:if(n)return S(e).length;t=(""+t).toLowerCase(),n=!0}}function y(e,t,r){var i,a,o,l=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n,i=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>i)&&(r=i);for(var a="",o=t;o<r;++o){a+=(n=e[o])<16?"0"+n.toString(16):n.toString(16)}return a}(this,t,r);case"utf8":case"utf-8":return b(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=this,a=t,o=r,0===a&&o===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(a,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}(this,t,r);default:if(l)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),l=!0}}function v(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function g(e,t,r,n,i){if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),isNaN(r*=1)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(i)return -1;else r=e.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=u.from(t,n)),u.isBuffer(t))return 0===t.length?-1:m(e,t,r,n,i);if("number"==typeof t){if(t&=255,u.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return m(e,[t],r,n,i)}throw TypeError("val must be string, number or Buffer")}function m(e,t,r,n,i){var a,o=1,l=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,l/=2,u/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var s=-1;for(a=r;a<l;a++)if(c(e,a)===c(t,-1===s?0:a-s)){if(-1===s&&(s=a),a-s+1===u)return s*o}else -1!==s&&(a-=a-s),s=-1}else for(r+u>l&&(r=l-u),a=r;a>=0;a--){for(var f=!0,h=0;h<u;h++)if(c(e,a+h)!==c(t,h)){f=!1;break}if(f)return a}return -1}t.hp=u,t.IS=50,u.TYPED_ARRAY_SUPPORT=void 0!==r.g.TYPED_ARRAY_SUPPORT?r.g.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),o(),u.poolSize=8192,u._augment=function(e){return e.__proto__=u.prototype,e},u.from=function(e,t,r){return c(null,e,t,r)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(e,t,r){return(s(e),e<=0)?l(null,e):void 0!==t?"string"==typeof r?l(null,e).fill(t,r):l(null,e).fill(t):l(null,e)},u.allocUnsafe=function(e){return f(null,e)},u.allocUnsafeSlow=function(e){return f(null,e)},u.isBuffer=function(e){return!!(null!=e&&e._isBuffer)},u.compare=function(e,t){if(!u.isBuffer(e)||!u.isBuffer(t))throw TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},u.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(e,t){if(!a(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return u.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=u.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(!u.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},u.byteLength=p,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)v(this,t,t+1);return this},u.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)v(this,t,t+3),v(this,t+1,t+2);return this},u.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)v(this,t,t+7),v(this,t+1,t+6),v(this,t+2,t+5),v(this,t+3,t+4);return this},u.prototype.toString=function(){var e=0|this.length;return 0===e?"":0==arguments.length?b(this,0,e):y.apply(this,arguments)},u.prototype.equals=function(e){if(!u.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===u.compare(this,e)},u.prototype.inspect=function(){var e="",r=t.IS;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},u.prototype.compare=function(e,t,r,n,i){if(!u.isBuffer(e))throw TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var a=i-n,o=r-t,l=Math.min(a,o),c=this.slice(n,i),s=e.slice(t,r),f=0;f<l;++f)if(c[f]!==s[f]){a=c[f],o=s[f];break}return a<o?-1:+(o<a)},u.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},u.prototype.indexOf=function(e,t,r){return g(this,e,t,r,!0)},u.prototype.lastIndexOf=function(e,t,r){return g(this,e,t,r,!1)};function b(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var a,o,l,u,c=e[i],s=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(s=c);break;case 2:(192&(a=e[i+1]))==128&&(u=(31&c)<<6|63&a)>127&&(s=u);break;case 3:a=e[i+1],o=e[i+2],(192&a)==128&&(192&o)==128&&(u=(15&c)<<12|(63&a)<<6|63&o)>2047&&(u<55296||u>57343)&&(s=u);break;case 4:a=e[i+1],o=e[i+2],l=e[i+3],(192&a)==128&&(192&o)==128&&(192&l)==128&&(u=(15&c)<<18|(63&a)<<12|(63&o)<<6|63&l)>65535&&u<1114112&&(s=u)}null===s?(s=65533,f=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|1023&s),n.push(s),i+=f}var h=n,d=h.length;if(d<=4096)return String.fromCharCode.apply(String,h);for(var p="",y=0;y<d;)p+=String.fromCharCode.apply(String,h.slice(y,y+=4096));return p}function w(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function x(e,t,r,n,i,a){if(!u.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function O(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,a=Math.min(e.length-r,2);i<a;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>(n?i:1-i)*8}function P(e,t,r,n){t<0&&(t=0xffffffff+t+1);for(var i=0,a=Math.min(e.length-r,4);i<a;++i)e[r+i]=t>>>(n?i:3-i)*8&255}function A(e,t,r,n,i,a){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function j(e,t,r,n,a){return a||A(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function E(e,t,r,n,a){return a||A(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}u.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,a,o,l,u,c,s,f,h=this.length-t;if((void 0===r||r>h)&&(r=h),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var d=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=t.length;if(a%2!=0)throw TypeError("Invalid hex string");n>a/2&&(n=a/2);for(var o=0;o<n;++o){var l=parseInt(t.substr(2*o,2),16);if(isNaN(l))break;e[r+o]=l}return o}(this,e,t,r);case"utf8":case"utf-8":return i=t,a=r,T(S(e,this.length-i),this,i,a);case"ascii":return o=t,l=r,T(_(e),this,o,l);case"latin1":case"binary":return function(e,t,r,n){return T(_(t),e,r,n)}(this,e,t,r);case"base64":return u=t,c=r,T(k(e),this,u,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return s=t,f=r,T(function(e,t){for(var r,n,i=[],a=0;a<e.length&&!((t-=2)<0);++a)n=(r=e.charCodeAt(a))>>8,i.push(r%256),i.push(n);return i}(e,this.length-s),this,s,f);default:if(d)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),d=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},u.prototype.slice=function(e,t){var r,n=this.length;if(e=~~e,t=void 0===t?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),u.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=u.prototype;else{var i=t-e;r=new u(i,void 0);for(var a=0;a<i;++a)r[a]=this[a+e]}return r},u.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||w(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n},u.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||w(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},u.prototype.readUInt8=function(e,t){return t||w(e,1,this.length),this[e]},u.prototype.readUInt16LE=function(e,t){return t||w(e,2,this.length),this[e]|this[e+1]<<8},u.prototype.readUInt16BE=function(e,t){return t||w(e,2,this.length),this[e]<<8|this[e+1]},u.prototype.readUInt32LE=function(e,t){return t||w(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},u.prototype.readUInt32BE=function(e,t){return t||w(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},u.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||w(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},u.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||w(e,t,this.length);for(var n=t,i=1,a=this[e+--n];n>0&&(i*=256);)a+=this[e+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*t)),a},u.prototype.readInt8=function(e,t){return(t||w(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},u.prototype.readInt16LE=function(e,t){t||w(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},u.prototype.readInt16BE=function(e,t){t||w(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},u.prototype.readInt32LE=function(e,t){return t||w(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},u.prototype.readInt32BE=function(e,t){return t||w(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},u.prototype.readFloatLE=function(e,t){return t||w(e,4,this.length),i.read(this,e,!0,23,4)},u.prototype.readFloatBE=function(e,t){return t||w(e,4,this.length),i.read(this,e,!1,23,4)},u.prototype.readDoubleLE=function(e,t){return t||w(e,8,this.length),i.read(this,e,!0,52,8)},u.prototype.readDoubleBE=function(e,t){return t||w(e,8,this.length),i.read(this,e,!1,52,8)},u.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t|=0,r|=0,!n){var i=Math.pow(2,8*r)-1;x(this,e,t,r,i,0)}var a=1,o=0;for(this[t]=255&e;++o<r&&(a*=256);)this[t+o]=e/a&255;return t+r},u.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t|=0,r|=0,!n){var i=Math.pow(2,8*r)-1;x(this,e,t,r,i,0)}var a=r-1,o=1;for(this[t+a]=255&e;--a>=0&&(o*=256);)this[t+a]=e/o&255;return t+r},u.prototype.writeUInt8=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,1,255,0),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},u.prototype.writeUInt16LE=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):O(this,e,t,!0),t+2},u.prototype.writeUInt16BE=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):O(this,e,t,!1),t+2},u.prototype.writeUInt32LE=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,4,0xffffffff,0),u.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):P(this,e,t,!0),t+4},u.prototype.writeUInt32BE=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,4,0xffffffff,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):P(this,e,t,!1),t+4},u.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t|=0,!n){var i=Math.pow(2,8*r-1);x(this,e,t,r,i-1,-i)}var a=0,o=1,l=0;for(this[t]=255&e;++a<r&&(o*=256);)e<0&&0===l&&0!==this[t+a-1]&&(l=1),this[t+a]=(e/o|0)-l&255;return t+r},u.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t|=0,!n){var i=Math.pow(2,8*r-1);x(this,e,t,r,i-1,-i)}var a=r-1,o=1,l=0;for(this[t+a]=255&e;--a>=0&&(o*=256);)e<0&&0===l&&0!==this[t+a+1]&&(l=1),this[t+a]=(e/o|0)-l&255;return t+r},u.prototype.writeInt8=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,1,127,-128),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},u.prototype.writeInt16LE=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):O(this,e,t,!0),t+2},u.prototype.writeInt16BE=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):O(this,e,t,!1),t+2},u.prototype.writeInt32LE=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,4,0x7fffffff,-0x80000000),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):P(this,e,t,!0),t+4},u.prototype.writeInt32BE=function(e,t,r){return e*=1,t|=0,r||x(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):P(this,e,t,!1),t+4},u.prototype.writeFloatLE=function(e,t,r){return j(this,e,t,!0,r)},u.prototype.writeFloatBE=function(e,t,r){return j(this,e,t,!1,r)},u.prototype.writeDoubleLE=function(e,t,r){return E(this,e,t,!0,r)},u.prototype.writeDoubleBE=function(e,t,r){return E(this,e,t,!1,r)},u.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("sourceStart out of bounds");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,a=n-r;if(this===e&&r<t&&t<n)for(i=a-1;i>=0;--i)e[i+t]=this[i+r];else if(a<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<a;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+a),t);return a},u.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i,a=e.charCodeAt(0);a<256&&(e=a)}if(void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=u.isBuffer(e)?e:S(new u(e,n).toString()),l=o.length;for(i=0;i<r-t;++i)this[i+t]=o[i%l]}return this};var M=/[^+\/0-9A-Za-z-_]/g;function S(e,t){t=t||1/0;for(var r,n=e.length,i=null,a=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return a}function _(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function k(e){return n.toByteArray(function(e){var t;if((e=((t=e).trim?t.trim():t.replace(/^\s+|\s+$/g,"")).replace(M,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function T(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}},2775:(e,t,r)=>{"use strict";r.d(t,{kz:()=>ir,fb:()=>n3,q:()=>im,tP:()=>iM,g1:()=>iN,iv:()=>i8,Nk:()=>n2,pM:()=>ie,Oz:()=>iv,tF:()=>i3,rj:()=>n0,ec:()=>nV,bb:()=>iw,xp:()=>iC,wL:()=>iA,sr:()=>i_,Qn:()=>iT,MK:()=>n7,IO:()=>nX,P9:()=>ic,S5:()=>io,PU:()=>nL,cd:()=>nB,eo:()=>nY,yi:()=>il,ZB:()=>i7,D5:()=>iF,iV:()=>iH,Hd:()=>nZ,Gx:()=>at,DP:()=>nH,BQ:()=>i5,_y:()=>an,AV:()=>iP,um:()=>nG,xM:()=>ik,gT:()=>ih,Kr:()=>iu,$X:()=>ip,TC:()=>n9,Zi:()=>i9,CR:()=>ae,ld:()=>nW,L$:()=>i0,Rl:()=>nU,Lw:()=>iJ,KR:()=>i1,sf:()=>n$,wP:()=>i2});var n,i,a,o,l,u,c,s={};r.r(s),r.d(s,{scaleBand:()=>x,scaleDiverging:()=>function e(){var t=eZ(r8()(eM));return t.copy=function(){return r5(t,e())},y.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=eQ(r8()).domain([.1,1,10]);return t.copy=function(){return r5(t,e()).base(t.base())},y.apply(t,arguments)},scaleDivergingPow:()=>r6,scaleDivergingSqrt:()=>r7,scaleDivergingSymlog:()=>function e(){var t=e2(r8());return t.copy=function(){return r5(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,ej),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,ej):[0,1],eZ(n)},scaleImplicit:()=>b,scaleLinear:()=>function e(){var t=eD();return t.copy=function(){return eT(t,e())},p.apply(t,arguments),eZ(t)},scaleLog:()=>function e(){let t=eQ(eC()).domain([1,10]);return t.copy=()=>eT(t,e()).base(t.base()),p.apply(t,arguments),t},scaleOrdinal:()=>w,scalePoint:()=>O,scalePow:()=>e6,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=N){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[z(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(k),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[z(a,e,0,i)]:t}function u(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,u()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,u()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},p.apply(eZ(l),arguments)},scaleRadial:()=>function e(){var t,r=eD(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(e9(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,ej)).map(e9)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},p.apply(a,arguments),eZ(a)},scaleSequential:()=>function e(){var t=eZ(r2()(eM));return t.copy=function(){return r5(t,e())},y.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=eQ(r2()).domain([1,10]);return t.copy=function(){return r5(t,e()).base(t.base())},y.apply(t,arguments)},scaleSequentialPow:()=>r3,scaleSequentialQuantile:()=>function e(){var t=[],r=eM;function n(e){if(null!=e&&!isNaN(e*=1))return r((z(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(k),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return tt(e);if(t>=1)return te(e);var n,i=(n-1)*t,a=Math.floor(i),o=te((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?tr:function(e=k){if(e===k)return tr;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,u=Math.log(o),c=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*c*(o-c)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*c/o+s)),h=Math.min(i,Math.floor(r+(o-l)*c/o+s));e(t,r,f,h,a)}let o=t[r],l=n,u=i;for(tn(t,n,r),a(t[i],o)>0&&tn(t,n,i);l<u;){for(tn(t,l,u),++l,--u;0>a(t[l],o);)++l;for(;a(t[u],o)>0;)--u}0===a(t[n],o)?tn(t,n,u):tn(t,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return t})(e,a).subarray(0,a+1));return o+(tt(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},y.apply(n,arguments)},scaleSequentialSqrt:()=>r4,scaleSequentialSymlog:()=>function e(){var t=e2(r2());return t.copy=function(){return r5(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleSqrt:()=>e7,scaleSymlog:()=>function e(){var t=e2(eC());return t.copy=function(){return eT(t,e()).constant(t.constant())},p.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[z(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(a,arguments)},scaleTime:()=>r0,scaleUtc:()=>r1,tickFormat:()=>eH});var f=r(4106),h=r(5651),d=r.n(h);function p(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function y(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class v extends Map{constructor(e,t=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(g(this,e))}has(e){return super.has(g(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function g({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function m(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function w(){var e=new v,t=[],r=[],n=b;function i(i){let a=e.get(i);if(void 0===a){if(n!==b)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new v,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return w(t,r).unknown(n)},p.apply(i,arguments),i}function x(){var e,t,r=w().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,u=0,c=0,s=.5;function f(){var r=n().length,f=o<a,h=f?o:a,d=f?a:o;e=(d-h)/Math.max(1,r-u+2*c),l&&(e=Math.floor(e)),h+=(d-h-e*(r-u))*s,t=e*(1-u),l&&(h=Math.round(h),t=Math.round(t));var p=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return h+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(u=Math.min(1,c=+e),f()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),f()):u},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return x(n(),[a,o]).round(l).paddingInner(u).paddingOuter(c).align(s)},p.apply(f(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(x.apply(null,arguments).paddingInner(1))}let P=Math.sqrt(50),A=Math.sqrt(10),j=Math.sqrt(2);function E(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),u=o/Math.pow(10,l),c=u>=P?10:u>=A?5:u>=j?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/c)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*c)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?E(e,t,2*r):[n,i,a]}function M(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?E(t,e,r):E(e,t,r);if(!(a>=i))return[];let l=a-i+1,u=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)u[e]=-((a-e)/o);else for(let e=0;e<l;++e)u[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)u[e]=-((i+e)/o);else for(let e=0;e<l;++e)u[e]=(i+e)*o;return u}function S(e,t,r){return E(e*=1,t*=1,r*=1)[2]}function _(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?S(t,e,r):S(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function k(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function T(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function C(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=k,r=(t,r)=>k(e(t),r),n=(t,r)=>e(t)-r):(t=e===k||e===T?e:D,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function D(){return 0}function N(e){return null===e?NaN:+e}let I=C(k),z=I.right;function R(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function L(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function U(){}I.left,C(N).center;var B="\\s*([+-]?\\d+)\\s*",$="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",F="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",K=/^#([0-9a-f]{3,8})$/,H=RegExp(`^rgb\\(${B},${B},${B}\\)$`),Z=RegExp(`^rgb\\(${F},${F},${F}\\)$`),G=RegExp(`^rgba\\(${B},${B},${B},${$}\\)$`),Y=RegExp(`^rgba\\(${F},${F},${F},${$}\\)$`),W=RegExp(`^hsl\\(${$},${F},${F}\\)$`),q=RegExp(`^hsla\\(${$},${F},${F},${$}\\)$`),V={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function J(){return this.rgb().formatHex()}function X(){return this.rgb().formatRgb()}function Q(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=K.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ee(t):3===r?new en(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?et(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?et(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=H.exec(e))?new en(t[1],t[2],t[3],1):(t=Z.exec(e))?new en(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=G.exec(e))?et(t[1],t[2],t[3],t[4]):(t=Y.exec(e))?et(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=W.exec(e))?ec(t[1],t[2]/100,t[3]/100,1):(t=q.exec(e))?ec(t[1],t[2]/100,t[3]/100,t[4]):V.hasOwnProperty(e)?ee(V[e]):"transparent"===e?new en(NaN,NaN,NaN,0):null}function ee(e){return new en(e>>16&255,e>>8&255,255&e,1)}function et(e,t,r,n){return n<=0&&(e=t=r=NaN),new en(e,t,r,n)}function er(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof U||(i=Q(i)),i)?new en((i=i.rgb()).r,i.g,i.b,i.opacity):new en:new en(e,t,r,null==n?1:n)}function en(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ei(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}`}function ea(){let e=eo(this.opacity);return`${1===e?"rgb(":"rgba("}${el(this.r)}, ${el(this.g)}, ${el(this.b)}${1===e?")":`, ${e})`}`}function eo(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function el(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function eu(e){return((e=el(e))<16?"0":"")+e.toString(16)}function ec(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ef(e,t,r,n)}function es(e){if(e instanceof ef)return new ef(e.h,e.s,e.l,e.opacity);if(e instanceof U||(e=Q(e)),!e)return new ef;if(e instanceof ef)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,u=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=u<.5?a+i:2-a-i,o*=60):l=u>0&&u<1?0:o,new ef(o,l,u,e.opacity)}function ef(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function eh(e){return(e=(e||0)%360)<0?e+360:e}function ed(e){return Math.max(0,Math.min(1,e||0))}function ep(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ey(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}R(U,Q,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:J,formatHex:J,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return es(this).formatHsl()},formatRgb:X,toString:X}),R(en,er,L(U,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new en(el(this.r),el(this.g),el(this.b),eo(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ei,formatHex:ei,formatHex8:function(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}${eu((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ea,toString:ea})),R(ef,function(e,t,r,n){return 1==arguments.length?es(e):new ef(e,t,r,null==n?1:n)},L(U,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ef(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ef(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new en(ep(e>=240?e-240:e+120,i,n),ep(e,i,n),ep(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ef(eh(this.h),ed(this.s),ed(this.l),eo(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=eo(this.opacity);return`${1===e?"hsl(":"hsla("}${eh(this.h)}, ${100*ed(this.s)}%, ${100*ed(this.l)}%${1===e?")":`, ${e})`}`}}));let ev=e=>()=>e;function eg(e,t){var r=t-e;return r?function(t){return e+t*r}:ev(isNaN(e)?t:e)}let em=function e(t){var r,n=1==(r=+t)?eg:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):ev(isNaN(e)?t:e)};function i(e,t){var r=n((e=er(e)).r,(t=er(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=eg(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function eb(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=er(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}function ew(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}eb(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return ey((r-n/t)*t,o,i,a,l)}}),eb(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return ey((r-n/t)*t,i,a,o,l)}});var ex=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eO=RegExp(ex.source,"g");function eP(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?ev(t):("number"===i?ew:"string"===i?(n=Q(t))?(t=n,em):function(e,t){var r,n,i,a,o,l=ex.lastIndex=eO.lastIndex=0,u=-1,c=[],s=[];for(e+="",t+="";(i=ex.exec(e))&&(a=eO.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),c[u]?c[u]+=o:c[++u]=o),(i=i[0])===(a=a[0])?c[u]?c[u]+=a:c[++u]=a:(c[++u]=null,s.push({i:u,x:ew(i,a)})),l=eO.lastIndex;return l<t.length&&(o=t.slice(l),c[u]?c[u]+=o:c[++u]=o),c.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)c[(r=s[n]).i]=r.x(e);return c.join("")})}:t instanceof Q?em:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=eP(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=eP(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ew:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function eA(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function ej(e){return+e}var eE=[0,1];function eM(e){return e}function eS(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function e_(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=eS(i,n),a=r(o,a)):(n=eS(n,i),a=r(a,o)),function(e){return a(n(e))}}function ek(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=eS(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=z(e,t,1,n)-1;return a[r](i[r](t))}}function eT(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eC(){var e,t,r,n,i,a,o=eE,l=eE,u=eP,c=eM;function s(){var e,t,r,u=Math.min(o.length,l.length);return c!==eM&&(e=o[0],t=o[u-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=u>2?ek:e_,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,u)))(e(c(t)))}return f.invert=function(r){return c(t((a||(a=n(l,o.map(e),ew)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,ej),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),u=eA,s()},f.clamp=function(e){return arguments.length?(c=!!e||eM,s()):c!==eM},f.interpolate=function(e){return arguments.length?(u=e,s()):u},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function eD(){return eC()(eM,eM)}var eN=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eI(e){var t;if(!(t=eN.exec(e)))throw Error("invalid format: "+e);return new ez({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ez(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function eR(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function eL(e){return(e=eR(Math.abs(e)))?e[1]:NaN}function eU(e,t){var r=eR(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}eI.prototype=ez.prototype,ez.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eB={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>eU(100*e,t),r:eU,s:function(e,t){var r=eR(e,t);if(!r)return e+"";var i=r[0],a=r[1],o=a-(n=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,l=i.length;return o===l?i:o>l?i+Array(o-l+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+Array(1-o).join("0")+eR(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function e$(e){return e}var eF=Array.prototype.map,eK=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eH(e,t,r,n){var i,l,u=_(e,t,r);switch((n=eI(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(l=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eL(c)/3)))-eL(Math.abs(u))))||(n.precision=l),o(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(l=Math.max(0,eL(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=u)))-eL(i))+1)||(n.precision=l-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(l=Math.max(0,-eL(Math.abs(u))))||(n.precision=l-("%"===n.type)*2)}return a(n)}function eZ(e){var t=e.domain;return e.ticks=function(e){var r=t();return M(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eH(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,u=a[o],c=a[l],s=10;for(c<u&&(i=u,u=c,c=i,i=o,o=l,l=i);s-- >0;){if((i=S(u,c,r))===n)return a[o]=u,a[l]=c,t(a);if(i>0)u=Math.floor(u/i)*i,c=Math.ceil(c/i)*i;else if(i<0)u=Math.ceil(u*i)/i,c=Math.floor(c*i)/i;else break;n=i}return e},e}function eG(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function eY(e){return Math.log(e)}function eW(e){return Math.exp(e)}function eq(e){return-Math.log(-e)}function eV(e){return-Math.exp(-e)}function eJ(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eX(e){return(t,r)=>-e(-t,r)}function eQ(e){let t,r,n=e(eY,eW),i=n.domain,o=10;function l(){var a,l;return t=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(l=o)?eJ:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=eX(t),r=eX(r),e(eq,eV)):e(eY,eW),n}return n.base=function(e){return arguments.length?(o=+e,l()):o},n.domain=function(e){return arguments.length?(i(e),l()):i()},n.ticks=e=>{let n,a,l=i(),u=l[0],c=l[l.length-1],s=c<u;s&&([u,c]=[c,u]);let f=t(u),h=t(c),d=null==e?10:+e,p=[];if(!(o%1)&&h-f<d){if(f=Math.floor(f),h=Math.ceil(h),u>0){for(;f<=h;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>c)break;p.push(a)}}else for(;f<=h;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>c)break;p.push(a)}2*p.length<d&&(p=M(u,c,d))}else p=M(f,h,Math.min(h-f,d)).map(r);return s?p.reverse():p},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=eI(i)).precision||(i.trim=!0),i=a(i)),e===1/0)return i;let l=Math.max(1,o*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*o<o-.5&&(n*=o),n<=l?i(e):""}},n.nice=()=>i(eG(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function e0(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function e1(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function e2(e){var t=1,r=e(e0(1),e1(t));return r.constant=function(r){return arguments.length?e(e0(t=+r),e1(t)):t},eZ(r)}function e5(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function e3(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function e4(e){return e<0?-e*e:e*e}function e8(e){var t=e(eM,eM),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(eM,eM):.5===r?e(e3,e4):e(e5(r),e5(1/r)):r},eZ(t)}function e6(){var e=e8(eC());return e.copy=function(){return eT(e,e6()).exponent(e.exponent())},p.apply(e,arguments),e}function e7(){return e6.apply(null,arguments).exponent(.5)}function e9(e){return Math.sign(e)*e*e}function te(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function tt(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function tr(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function tn(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}a=(i=function(e){var t,r,i,a=void 0===e.grouping||void 0===e.thousands?e$:(t=eF.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],u=0;i>0&&l>0&&(u+l+1>n&&(l=Math.max(1,n-u)),a.push(e.substring(i-=l,i+l)),!((u+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),o=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",u=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?e$:(i=eF.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),s=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",h=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=eI(e)).fill,r=e.align,i=e.sign,d=e.symbol,p=e.zero,y=e.width,v=e.comma,g=e.precision,m=e.trim,b=e.type;"n"===b?(v=!0,b="g"):eB[b]||(void 0===g&&(g=12),m=!0,b="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var w="$"===d?o:"#"===d&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===d?l:/[%p]/.test(b)?s:"",O=eB[b],P=/[defgprs%]/.test(b);function A(e){var o,l,s,d=w,A=x;if("c"===b)A=O(e)+A,e="";else{var j=(e*=1)<0||1/e<0;if(e=isNaN(e)?h:O(Math.abs(e),g),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),j&&0==+e&&"+"!==i&&(j=!1),d=(j?"("===i?i:f:"-"===i||"("===i?"":i)+d,A=("s"===b?eK[8+n/3]:"")+A+(j&&"("===i?")":""),P){for(o=-1,l=e.length;++o<l;)if(48>(s=e.charCodeAt(o))||s>57){A=(46===s?u+e.slice(o+1):e.slice(o))+A,e=e.slice(0,o);break}}}v&&!p&&(e=a(e,1/0));var E=d.length+e.length+A.length,M=E<y?Array(y-E+1).join(t):"";switch(v&&p&&(e=a(M+e,M.length?y-A.length:1/0),M=""),r){case"<":e=d+e+A+M;break;case"=":e=d+M+e+A;break;case"^":e=M.slice(0,E=M.length>>1)+d+e+A+M.slice(E);break;default:e=M+d+e+A}return c(e)}return g=void 0===g?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),A.toString=function(){return e+""},A}return{format:d,formatPrefix:function(e,t){var r=d(((e=eI(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(eL(t)/3))),i=Math.pow(10,-n),a=eK[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,o=i.formatPrefix;let ti=new Date,ta=new Date;function to(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>to(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(ti.setTime(+t),ta.setTime(+n),e(ti),e(ta),Math.floor(r(ti,ta))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let tl=to(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);tl.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?to(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):tl:null,tl.range;let tu=to(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());tu.range;let tc=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());tc.range;let ts=to(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());ts.range;let tf=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());tf.range;let th=to(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());th.range;let td=to(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);td.range;let tp=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);tp.range;let ty=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function tv(e){return to(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}ty.range;let tg=tv(0),tm=tv(1),tb=tv(2),tw=tv(3),tx=tv(4),tO=tv(5),tP=tv(6);function tA(e){return to(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tg.range,tm.range,tb.range,tw.range,tx.range,tO.range,tP.range;let tj=tA(0),tE=tA(1),tM=tA(2),tS=tA(3),t_=tA(4),tk=tA(5),tT=tA(6);tj.range,tE.range,tM.range,tS.range,t_.range,tk.range,tT.range;let tC=to(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tC.range;let tD=to(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tD.range;let tN=to(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tN.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,tN.range;let tI=to(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tz(e,t,r,n,i,a){let o=[[tu,1,1e3],[tu,5,5e3],[tu,15,15e3],[tu,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=C(([,,e])=>e).right(o,i);if(a===o.length)return e.every(_(t/31536e6,r/31536e6,n));if(0===a)return tl.every(Math.max(_(t,r,n),1));let[l,u]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(u)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}tI.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tI.range;let[tR,tL]=tz(tI,tD,tj,ty,th,ts),[tU,tB]=tz(tN,tC,tg,td,tf,tc);function t$(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tF(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tK(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tH={"-":"",_:" ",0:"0"},tZ=/^\s*\d+/,tG=/^%/,tY=/[\\^$*+?|[\]().{}]/g;function tW(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function tq(e){return e.replace(tY,"\\$&")}function tV(e){return RegExp("^(?:"+e.map(tq).join("|")+")","i")}function tJ(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tX(e,t,r){var n=tZ.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tQ(e,t,r){var n=tZ.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function t0(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=tZ.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function t3(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function t4(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function t8(e,t,r){var n=tZ.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t6(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t7(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t9(e,t,r){var n=tZ.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function re(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function rt(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function rr(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=tZ.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ri(e,t,r){var n=tZ.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ra(e,t,r){var n=tG.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ro(e,t,r){var n=tZ.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function rl(e,t,r){var n=tZ.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function ru(e,t){return tW(e.getDate(),t,2)}function rc(e,t){return tW(e.getHours(),t,2)}function rs(e,t){return tW(e.getHours()%12||12,t,2)}function rf(e,t){return tW(1+td.count(tN(e),e),t,3)}function rh(e,t){return tW(e.getMilliseconds(),t,3)}function rd(e,t){return rh(e,t)+"000"}function rp(e,t){return tW(e.getMonth()+1,t,2)}function ry(e,t){return tW(e.getMinutes(),t,2)}function rv(e,t){return tW(e.getSeconds(),t,2)}function rg(e){var t=e.getDay();return 0===t?7:t}function rm(e,t){return tW(tg.count(tN(e)-1,e),t,2)}function rb(e){var t=e.getDay();return t>=4||0===t?tx(e):tx.ceil(e)}function rw(e,t){return e=rb(e),tW(tx.count(tN(e),e)+(4===tN(e).getDay()),t,2)}function rx(e){return e.getDay()}function rO(e,t){return tW(tm.count(tN(e)-1,e),t,2)}function rP(e,t){return tW(e.getFullYear()%100,t,2)}function rA(e,t){return tW((e=rb(e)).getFullYear()%100,t,2)}function rj(e,t){return tW(e.getFullYear()%1e4,t,4)}function rE(e,t){var r=e.getDay();return tW((e=r>=4||0===r?tx(e):tx.ceil(e)).getFullYear()%1e4,t,4)}function rM(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tW(t/60|0,"0",2)+tW(t%60,"0",2)}function rS(e,t){return tW(e.getUTCDate(),t,2)}function r_(e,t){return tW(e.getUTCHours(),t,2)}function rk(e,t){return tW(e.getUTCHours()%12||12,t,2)}function rT(e,t){return tW(1+tp.count(tI(e),e),t,3)}function rC(e,t){return tW(e.getUTCMilliseconds(),t,3)}function rD(e,t){return rC(e,t)+"000"}function rN(e,t){return tW(e.getUTCMonth()+1,t,2)}function rI(e,t){return tW(e.getUTCMinutes(),t,2)}function rz(e,t){return tW(e.getUTCSeconds(),t,2)}function rR(e){var t=e.getUTCDay();return 0===t?7:t}function rL(e,t){return tW(tj.count(tI(e)-1,e),t,2)}function rU(e){var t=e.getUTCDay();return t>=4||0===t?t_(e):t_.ceil(e)}function rB(e,t){return e=rU(e),tW(t_.count(tI(e),e)+(4===tI(e).getUTCDay()),t,2)}function r$(e){return e.getUTCDay()}function rF(e,t){return tW(tE.count(tI(e)-1,e),t,2)}function rK(e,t){return tW(e.getUTCFullYear()%100,t,2)}function rH(e,t){return tW((e=rU(e)).getUTCFullYear()%100,t,2)}function rZ(e,t){return tW(e.getUTCFullYear()%1e4,t,4)}function rG(e,t){var r=e.getUTCDay();return tW((e=r>=4||0===r?t_(e):t_.ceil(e)).getUTCFullYear()%1e4,t,4)}function rY(){return"+0000"}function rW(){return"%"}function rq(e){return+e}function rV(e){return Math.floor(e/1e3)}function rJ(e){return new Date(e)}function rX(e){return e instanceof Date?+e:+new Date(+e)}function rQ(e,t,r,n,i,a,o,l,u,c){var s=eD(),f=s.invert,h=s.domain,d=c(".%L"),p=c(":%S"),y=c("%I:%M"),v=c("%I %p"),g=c("%a %d"),m=c("%b %d"),b=c("%B"),w=c("%Y");function x(e){return(u(e)<e?d:l(e)<e?p:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:w)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?h(Array.from(e,rX)):h().map(rJ)},s.ticks=function(t){var r=h();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?x:c(t)},s.nice=function(e){var r=h();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?h(eG(r,e)):s},s.copy=function(){return eT(s,rQ(e,t,r,n,i,a,o,l,u,c))},s}function r0(){return p.apply(rQ(tU,tB,tN,tC,tg,td,tf,tc,tu,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return p.apply(rQ(tR,tL,tI,tD,tj,tp,th,ts,tu,c).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var e,t,r,n,i,a=0,o=1,l=eM,u=!1;function c(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,u?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),c):[l(0),l(1)]}}return c.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),c):[a,o]},c.clamp=function(e){return arguments.length?(u=!!e,c):u},c.interpolator=function(e){return arguments.length?(l=e,c):l},c.range=s(eP),c.rangeRound=s(eA),c.unknown=function(e){return arguments.length?(i=e,c):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),c}}function r5(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function r3(){var e=e8(r2());return e.copy=function(){return r5(e,r3()).exponent(e.exponent())},y.apply(e,arguments)}function r4(){return r3.apply(null,arguments).exponent(.5)}function r8(){var e,t,r,n,i,a,o,l=0,u=.5,c=1,s=1,f=eM,h=!1;function d(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(h?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=eP);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(o){return arguments.length?([l,u,c]=o,e=a(l*=1),t=a(u*=1),r=a(c*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,d):[l,u,c]},d.clamp=function(e){return arguments.length?(h=!!e,d):h},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=p(eP),d.rangeRound=p(eA),d.unknown=function(e){return arguments.length?(o=e,d):o},function(o){return a=o,e=o(l),t=o(u),r=o(c),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,d}}function r6(){var e=e8(r8());return e.copy=function(){return r5(e,r6()).exponent(e.exponent())},y.apply(e,arguments)}function r7(){return r6.apply(null,arguments).exponent(.5)}u=(l=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,u=e.shortMonths,c=tV(i),s=tJ(i),f=tV(a),h=tJ(a),d=tV(o),p=tJ(o),y=tV(l),v=tJ(l),g=tV(u),m=tJ(u),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return u[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:ru,e:ru,f:rd,g:rA,G:rE,H:rc,I:rs,j:rf,L:rh,m:rp,M:ry,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rq,s:rV,S:rv,u:rg,U:rm,V:rw,w:rx,W:rO,x:null,X:null,y:rP,Y:rj,Z:rM,"%":rW},w={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return u[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:rS,e:rS,f:rD,g:rH,G:rG,H:r_,I:rk,j:rT,L:rC,m:rN,M:rI,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rq,s:rV,S:rz,u:rR,U:rL,V:rB,w:r$,W:rF,x:null,X:null,y:rK,Y:rZ,Z:rY,"%":rW},x={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return A(e,t,r,n)},d:t7,e:t7,f:ri,g:t3,G:t5,H:re,I:re,j:t9,L:rn,m:t6,M:rt,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:t8,Q:ro,s:rl,S:rr,u:tQ,U:t0,V:t1,w:tX,W:t2,x:function(e,t,n){return A(e,r,t,n)},X:function(e,t,r){return A(e,n,t,r)},y:t3,Y:t5,Z:t4,"%":ra};function O(e,t){return function(r){var n,i,a,o=[],l=-1,u=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===e.charCodeAt(l)&&(o.push(e.slice(u,l)),null!=(i=tH[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),u=l+1);return o.push(e.slice(u,l)),o.join("")}}function P(e,t){return function(r){var n,i,a=tK(1900,void 0,1);if(A(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=tF(tK(a.y,0,1))).getUTCDay())>4||0===i?tE.ceil(n):tE(n),n=tp.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=t$(tK(a.y,0,1))).getDay())>4||0===i?tm.ceil(n):tm(n),n=td.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?tF(tK(a.y,0,1)).getUTCDay():t$(tK(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,tF(a)):t$(a)}}function A(e,t,r,n){for(var i,a,o=0,l=t.length,u=r.length;o<l;){if(n>=u)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=x[(i=t.charAt(o++))in tH?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),w.x=O(r,w),w.X=O(n,w),w.c=O(t,w),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=P(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",w);return t.toString=function(){return e},t},utcParse:function(e){var t=P(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,c=l.utcFormat,l.utcParse;var r9=r(6584),ne=r(1757),nt=r(7774),nr=r(4707),nn=r(6790);function ni(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,nn.H)(t)&&(0,nn.H)(r))return!0}return!1}function na(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var no=r(2216),nl=r.n(no),nu=e=>e,nc={},ns=e=>function t(){let r;return 0==arguments.length||1==arguments.length&&(r=arguments.length<=0?void 0:arguments[0],r===nc)?t:e(...arguments)},nf=(e,t)=>1===e?t:ns(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==nc).length;return a>=e?t(...n):nf(e-a,ns(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>e===nc?r.shift():e),...r)}))}),nh=e=>nf(e.length,e),nd=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},np=nh((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),ny=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nu;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},nv=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),ng=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function nm(e){return 0===e?1:Math.floor(new(nl())(e).abs().log(10).toNumber())+1}function nb(e,t,r){for(var n=new(nl())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}nh((e,t,r)=>{var n=+e;return n+r*(t-n)}),nh((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),nh((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nw=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},nx=(e,t,r)=>{if(e.lte(0))return new(nl())(0);var n=nm(e.toNumber()),i=new(nl())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new(nl())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new(nl())(t?l.toNumber():Math.ceil(l.toNumber()))},nO=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(nl())(0),tickMin:new(nl())(0),tickMax:new(nl())(0)};var o=nx(new(nl())(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new(nl())(0):(i=new(nl())(e).add(t).div(2)).sub(new(nl())(i).mod(o))).sub(e).div(o).toNumber()),u=Math.ceil(new(nl())(t).sub(i).div(o).toNumber()),c=l+u+1;return c>r?nO(e,t,r,n,a+1):(c<r&&(u=t>0?u+(r-c):u,l=t>0?l:l+(r-c)),{step:o,tickMin:i.sub(new(nl())(l).mul(o)),tickMax:i.add(new(nl())(u).mul(o))})},nP=ng(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=nw([t,r]);if(o===-1/0||l===1/0){var u=l===1/0?[o,...nd(0,n-1).map(()=>1/0)]:[...nd(0,n-1).map(()=>-1/0),l];return t>r?nv(u):u}if(o===l){var c=new(nl())(1),s=new(nl())(o);if(!s.isint()&&i){var f=Math.abs(o);f<1?(c=new(nl())(10).pow(nm(o)-1),s=new(nl())(Math.floor(s.div(c).toNumber())).mul(c)):f>1&&(s=new(nl())(Math.floor(o)))}else 0===o?s=new(nl())(Math.floor((n-1)/2)):i||(s=new(nl())(Math.floor(o)));var h=Math.floor((n-1)/2);return ny(np(e=>s.add(new(nl())(e-h).mul(c)).toNumber()),nd)(0,n)}var{step:d,tickMin:p,tickMax:y}=nO(o,l,a,i,0),v=nb(p,y.add(new(nl())(.1).mul(d)),d);return t>r?nv(v):v}),nA=ng(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=nw([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),u=nx(new(nl())(o).sub(a).div(l-1),i,0),c=[...nb(new(nl())(a),new(nl())(o),u),o];return!1===i&&(c=c.map(e=>Math.round(e))),r>n?nv(c):c}),nj=r(63),nE=r(718),nM=r(5540),nS=r(3518),n_=r(5276),nk=r(8766),nT=r(3076),nC=r(4125),nD=r(6100),nN=r(1523);function nI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nI(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nR=[0,"auto"],nL={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},nU=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?nL:r},nB={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:nR,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nN.tQ},n$=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nB:r},nF={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nK=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nF:r},nH=(e,t,r)=>{switch(t){case"xAxis":return nU(e,r);case"yAxis":return n$(e,r);case"zAxis":return nK(e,r);case"angleAxis":return(0,nk.Be)(e,r);case"radiusAxis":return(0,nk.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nZ=(e,t,r)=>{switch(t){case"xAxis":return nU(e,r);case"yAxis":return n$(e,r);case"angleAxis":return(0,nk.Be)(e,r);case"radiusAxis":return(0,nk.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nG=e=>e.graphicalItems.countOfBars>0;function nY(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var nW=e=>e.graphicalItems.cartesianItems,nq=(0,f.Mz)([nT.N,nC.E],nY),nV=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),nJ=(0,f.Mz)([nW,nH,nq],nV),nX=e=>e.filter(e=>void 0===e.stackId),nQ=(0,f.Mz)([nJ],nX),n0=e=>e.map(e=>e.data).filter(Boolean).flat(1),n1=(0,f.Mz)([nJ],n0),n2=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},n5=(0,f.Mz)([n1,nt.HS],n2),n3=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,ne.kr)(e,t)}))):e.map(e=>({value:e})),n4=(0,f.Mz)([n5,nH,nJ],n3);function n8(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function n6(e){return e.filter(e=>(0,nr.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,nr.M8)(e))}var n7=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,a=i.map(e=>e.dataKey);return[n,{stackedData:(0,ne.yy)(e,a,r),graphicalItems:i}]})),n9=(0,f.Mz)([n5,nJ,n_.eC],n7),ie=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=(0,ne.Mk)(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},it=(0,f.Mz)([n9,nt.LF,nT.N],ie),ir=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(e=>n8(n,e)),l=(0,ne.kr)(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,nr.M8)(t)||!r.length?[]:n6(r.flatMap(r=>{var n,i,a=(0,ne.kr)(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,nn.H)(n)&&(0,nn.H)(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),ii=(0,f.Mz)(n5,nH,nQ,nT.N,ir);function ia(e){var{value:t}=e;if((0,nr.vh)(t)||t instanceof Date)return t}var io=e=>{var t;if(null==e||!("domain"in e))return nR;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=n6(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:nR},il=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},iu=e=>e.referenceElements.dots,ic=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),is=(0,f.Mz)([iu,nT.N,nC.E],ic),ih=e=>e.referenceElements.areas,id=(0,f.Mz)([ih,nT.N,nC.E],ic),ip=e=>e.referenceElements.lines,iy=(0,f.Mz)([ip,nT.N,nC.E],ic),iv=(e,t)=>{var r=n6(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ig=(0,f.Mz)(is,nT.N,iv),im=(e,t)=>{var r=n6(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ib=(0,f.Mz)([id,nT.N],im),iw=(e,t)=>{var r=n6(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ix=(0,f.Mz)(iy,nT.N,iw),iO=(0,f.Mz)(ig,ix,ib,(e,t,r)=>il(e,r,t)),iP=(0,f.Mz)([nH],io),iA=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if((0,nn.H)(i))r=i;else if("function"==typeof i)return;if((0,nn.H)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(ni(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(ni(n))return na(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if((0,nr.Et)(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&ne.IH.test(o)){var u=ne.IH.exec(o);if(null==u||null==t)i=void 0;else{var c=+u[1];i=t[0]-c}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if((0,nr.Et)(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&ne.qx.test(l)){var s=ne.qx.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var h=[i,a];if(ni(h))return null==t?h:na(h,t,r)}}}(t,il(r,i,(e=>{var t=n6(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]})(n)),e.allowDataOverflow)},ij=(0,f.Mz)([nH,iP,it,ii,iO],iA),iE=[0,1],iM=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:u}=e,c=(0,ne._L)(t,a);return c&&null==l?d()(0,r.length):"category"===u?((e,t,r)=>{var n=e.map(ia).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,nr.CG)(n))?d()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,c):"expand"===i?iE:o}},iS=(0,f.Mz)([nH,r9.fz,n5,n4,n_.eC,nT.N,ij],iM),i_=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat((0,nr.Zb)(a));return l in s?l:"point"}}},ik=(0,f.Mz)([nH,r9.fz,nG,n_.iO,nT.N],i_);function iT(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in s)return s[e]();var t="scale".concat((0,nr.Zb)(e));if(t in s)return s[t]()}}(t);if(null!=i){var a=i.domain(r).range(n);return(0,ne.YB)(a),a}}}var iC=(e,t,r)=>{var n=io(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(e))return nP(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&ni(e))return nA(e,t.tickCount,t.allowDecimals)}},iD=(0,f.Mz)([iS,nZ,ik],iC),iN=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&ni(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,iI=(0,f.Mz)([nH,iS,iD,nT.N],iN),iz=(0,f.Mz)(n4,nH,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(n6(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),iR=(0,f.Mz)(iz,r9.fz,n_.gY,nM.HZ,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,nn.H)(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=(0,nr.F4)(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),iL=(0,f.Mz)(nU,(e,t)=>{var r=nU(e,t);return null==r||"string"!=typeof r.padding?0:iR(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),iU=(0,f.Mz)(n$,(e,t)=>{var r=n$(e,t);return null==r||"string"!=typeof r.padding?0:iR(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),iB=(0,f.Mz)([nM.HZ,iL,nS.U,nS.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),i$=(0,f.Mz)([nM.HZ,r9.fz,iU,nS.U,nS.C,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),iF=(e,t,r,n)=>{var i;switch(t){case"xAxis":return iB(e,r,n);case"yAxis":return i$(e,r,n);case"zAxis":return null==(i=nK(e,r))?void 0:i.range;case"angleAxis":return(0,nk.Cv)(e);case"radiusAxis":return(0,nk.Dc)(e,r);default:return}},iK=(0,f.Mz)([nH,iF],nD.I),iH=(0,f.Mz)([nH,ik,iI,iK],iT);function iZ(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,f.Mz)(nJ,nT.N,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>n8(t,e)));var iG=(e,t)=>t,iY=(e,t,r)=>r,iW=(0,f.Mz)(nE.h,iG,iY,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iZ)),iq=(0,f.Mz)(nE.W,iG,iY,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iZ)),iV=(e,t)=>({width:e.width,height:t.height}),iJ=(0,f.Mz)(nM.HZ,nU,iV),iX=(0,f.Mz)(nj.A$,nM.HZ,iW,iG,iY,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=iV(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var u="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(u)*l.height,a+=(u?-1:1)*l.height}),o}),iQ=(0,f.Mz)(nj.Lp,nM.HZ,iq,iG,iY,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:nN.tQ,height:e.height}))(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var u="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(u)*l.width,a+=(u?-1:1)*l.width}),o}),i0=(e,t)=>{var r=(0,nM.HZ)(e),n=nU(e,t);if(null!=n){var i=iX(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},i1=(e,t)=>{var r=(0,nM.HZ)(e),n=n$(e,t);if(null!=n){var i=iQ(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},i2=(0,f.Mz)(nM.HZ,n$,(e,t)=>({width:"number"==typeof t.width?t.width:nN.tQ,height:e.height})),i5=(e,t,r)=>{switch(t){case"xAxis":return iJ(e,r).width;case"yAxis":return i2(e,r).height;default:return}},i3=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=(0,ne._L)(e,n),u=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&(0,nr.CG)(u))return u}},i4=(0,f.Mz)([r9.fz,n4,nH,nT.N],i3),i8=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,ne._L)(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},i6=(0,f.Mz)([r9.fz,n4,nZ,nT.N],i8),i7=(0,f.Mz)([r9.fz,(e,t,r)=>{switch(t){case"xAxis":return nU(e,r);case"yAxis":return n$(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},ik,iH,i4,i6,iF,iD,nT.N],(e,t,r,n,i,a,o,l,u)=>{if(null==t)return null;var c=(0,ne._L)(e,u);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:u,categoricalDomain:a,duplicateDomain:i,isCategorical:c,niceTicks:l,range:o,realScaleType:r,scale:n}}),i9=(0,f.Mz)([r9.fz,nZ,ik,iH,iD,iF,i4,i6,nT.N],(e,t,r,n,i,a,o,l,u)=>{if(null!=t&&null!=n){var c=(0,ne._L)(e,u),{type:s,ticks:f,tickCount:h}=t,d="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/d:0;p="angleAxis"===u&&null!=a&&a.length>=2?2*(0,nr.sA)(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!(0,nr.M8)(e.coordinate)):c&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(h).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),ae=(0,f.Mz)([r9.fz,nZ,iH,iF,i4,i6,nT.N],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,ne._L)(e,o),{tickCount:u}=t,c=0;return(c="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,nr.sA)(n[0]-n[1])*c:c,l&&a)?a.map((e,t)=>({coordinate:r(e)+c,value:e,index:t,offset:c})):r.ticks?r.ticks(u).map(e=>({coordinate:r(e)+c,value:e,offset:c})):r.domain().map((e,t)=>({coordinate:r(e)+c,value:i?i[e]:e,index:t,offset:c}))}}),at=(0,f.Mz)(nH,iH,(e,t)=>{if(null!=e&&null!=t)return nz(nz({},e),{},{scale:t})}),ar=(0,f.Mz)([nH,ik,iS,iK],iT);(0,f.Mz)((e,t,r)=>nK(e,r),ar,(e,t)=>{if(null!=e&&null!=t)return nz(nz({},e),{},{scale:t})});var an=(0,f.Mz)([r9.fz,nE.h,nE.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},2782:(e,t,r)=>{"use strict";r.d(t,{e:()=>p,k:()=>y});var n=r(7681),i=r(6948),a=r(3573),o=r(435),l=r(3429),u=r(1523),c=r(4106),s=r(4084),f=r(3984),h=(0,c.Mz)([f.J],e=>e.tooltipItemPayloads),d=(0,c.Mz)([h,s.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),p=(0,n.VP)("touchMove"),y=(0,n.Nc)();y.startListening({actionCreator:p,effect:(e,t)=>{var r=e.payload,n=t.getState(),c=(0,l.au)(n,n.tooltip.settings.shared);if("axis"===c){var s=(0,a.g)(n,(0,o.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==s?void 0:s.activeIndex)!=null&&t.dispatch((0,i.Nt)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate}))}else if("item"===c){var f,h=r.touches[0],p=document.elementFromPoint(h.clientX,h.clientY);if(!p||!p.getAttribute)return;var y=p.getAttribute(u.F0),v=null!=(f=p.getAttribute(u.um))?f:void 0,g=d(t.getState(),y,v);t.dispatch((0,i.RD)({activeDataKey:v,activeIndex:y,activeCoordinate:g}))}}})},2785:(e,t,r)=>{"use strict";r.d(t,{i:()=>u});let n=Math.PI,i=2*n,a=i-1e-6;function o(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?o:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return o;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,i,a){if(e*=1,t*=1,r*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,l=this._y1,u=r-e,c=i-t,s=o-e,f=l-t,h=s*s+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(h>1e-6)if(Math.abs(f*u-c*s)>1e-6&&a){let d=r-o,p=i-l,y=u*u+c*c,v=Math.sqrt(y),g=Math.sqrt(h),m=a*Math.tan((n-Math.acos((y+h-(d*d+p*p))/(2*v*g)))/2),b=m/g,w=m/v;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*f}`,this._append`A${a},${a},0,0,${+(f*d>s*p)},${this._x1=e+w*u},${this._y1=t+w*c}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,o,l,u){if(e*=1,t*=1,r*=1,u=!!u,r<0)throw Error(`negative radius: ${r}`);let c=r*Math.cos(o),s=r*Math.sin(o),f=e+c,h=t+s,d=1^u,p=u?o-l:l-o;null===this._x1?this._append`M${f},${h}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-h)>1e-6)&&this._append`L${f},${h}`,r&&(p<0&&(p=p%i+i),p>a?this._append`A${r},${r},0,1,${d},${e-c},${t-s}A${r},${r},0,1,${d},${this._x1=f},${this._y1=h}`:p>1e-6&&this._append`A${r},${r},0,${+(p>=n)},${d},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function u(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},2791:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],l]:e._events[u].push(l):(e._events[u]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var u,c,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(c=1,u=Array(f-1);c<f;c++)u[c-1]=arguments[c];s.fn.apply(s.context,u)}else{var h,d=s.length;for(c=0;c<d;c++)switch(s[c].once&&this.removeListener(e,s[c].fn,void 0,!0),f){case 1:s[c].fn.call(s[c].context);break;case 2:s[c].fn.call(s[c].context,t);break;case 3:s[c].fn.call(s[c].context,t,n);break;case 4:s[c].fn.call(s[c].context,t,n,i);break;default:if(!u)for(h=1,u=Array(f-1);h<f;h++)u[h-1]=arguments[h];s[c].fn.apply(s[c].context,u)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var u=0,c=[],s=l.length;u<s;u++)(l[u].fn!==t||i&&!l[u].once||n&&l[u].context!==n)&&c.push(l[u]);c.length?this._events[a]=1===c.length?c[0]:c:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},2795:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},2806:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},2829:(e,t,r)=>{"use strict";r.d(t,{r:()=>l});var n=r(2115),i=r(193),a=r(6948),o=r(1037);function l(e){var{fn:t,args:r}=e,l=(0,i.j)(),u=(0,o.r)();return(0,n.useEffect)(()=>{if(!u){var e=t(r);return l((0,a.Ix)(e)),()=>{l((0,a.XB)(e))}}},[t,r,l,u]),null}},2910:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(3014),i=r(706),a=r(1143),o=r(9578);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return u(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return c(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function u(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let u=0;u<e.length;u++){if(i.has(u))continue;let c=e[u],s=!1;if(r(c,o,a,e,t,n)&&(s=!0),s){i.add(u),l=!0;break}}if(!l)return!1}return!0}function c(e,t,r,n){return 0===t.size||e instanceof Set&&u([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,u){let c=r(t,n,i,a,o,u);return void 0!==c?!!c:l(t,n,e,u)},new Map)},t.isSetMatch=c},2977:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=e=>null;n.displayName="Cell"},3006:(e,t,r)=>{e.exports=r(1121).uniqBy},3014:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2910);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},3076:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var n=(e,t)=>t},3120:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},3258:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>c,Qd:()=>l,Tw:()=>f,Zz:()=>s,ve:()=>h,y$:()=>u});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function l(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function u(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(u)(e,t)}let a=e,c=t,s=new Map,f=s,h=0,d=!1;function p(){f===s&&(f=new Map,s.forEach((e,t)=>{f.set(t,e)}))}function y(){if(d)throw Error(n(3));return c}function v(e){if("function"!=typeof e)throw Error(n(4));if(d)throw Error(n(5));let t=!0;p();let r=h++;return f.set(r,e),function(){if(t){if(d)throw Error(n(6));t=!1,p(),f.delete(r),s=null}}}function g(e){if(!l(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(d)throw Error(n(9));try{d=!0,c=a(c,e)}finally{d=!1}return(s=f).forEach(e=>{e()}),e}return g({type:o.INIT}),{dispatch:g,subscribe:v,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));a=e,g({type:o.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(y())}return t(),{unsubscribe:v(t)}},[i](){return this}}}}}function c(e){let t,r=Object.keys(e),i={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(i[n]=e[n])}let a=Object.keys(i);try{Object.keys(i).forEach(e=>{let t=i[e];if(void 0===t(void 0,{type:o.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let o=!1,l={};for(let t=0;t<a.length;t++){let u=a[t],c=i[u],s=e[u],f=c(s,r);if(void 0===f)throw r&&r.type,Error(n(14));l[u]=f,o=o||f!==s}return(o=o||a.length!==Object.keys(e).length)?l:e}}function s(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,i)=>{let a=t(r,i),o=()=>{throw Error(n(15))},l={getState:a.getState,dispatch:(e,...t)=>o(e,...t)};return o=s(...e.map(e=>e(l)))(a.dispatch),{...a,dispatch:o}}}function h(e){return l(e)&&"type"in e&&"string"==typeof e.type}},3279:(e,t,r)=>{"use strict";r.d(t,{YF:()=>c,dj:()=>s,fP:()=>f,ky:()=>u});var n=r(7681),i=r(6948),a=r(3573),o=r(3429),l=r(435),u=(0,n.VP)("mouseClick"),c=(0,n.Nc)();c.startListening({actionCreator:u,effect:(e,t)=>{var r=e.payload,n=(0,a.g)(t.getState(),(0,l.w)(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,i.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s=(0,n.VP)("mouseMove"),f=(0,n.Nc)();f.startListening({actionCreator:s,effect:(e,t)=>{var r=e.payload,n=t.getState(),u=(0,o.au)(n,n.tooltip.settings.shared),c=(0,a.g)(n,(0,l.w)(r));"axis"===u&&((null==c?void 0:c.activeIndex)!=null?t.dispatch((0,i.Nt)({activeIndex:c.activeIndex,activeDataKey:void 0,activeCoordinate:c.activeCoordinate})):t.dispatch((0,i.xS)()))}})},3300:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(4707),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},3429:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Hw:()=>a,Td:()=>u,au:()=>l,xH:()=>i});var n=r(193),i=e=>e.options.defaultTooltipEventType,a=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function l(e,t){return o(t,i(e),a(e))}function u(e){return(0,n.G)(t=>l(t,e))}},3439:(e,t,r)=>{"use strict";r.d(t,{QQ:()=>i,VU:()=>o,XC:()=>c,_U:()=>u,j2:()=>l});var n=r(2115),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],u=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(e=>{l.includes(e)&&(i[e]=t||(t=>r[e](r,t)))}),i},c=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];l.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=e=>(a(t,r,e),null))}),n}},3472:(e,t,r)=>{"use strict";r.d(t,{CA:()=>y,MC:()=>c,QG:()=>p,Vi:()=>u,cU:()=>s,fR:()=>f});var n=r(7681),i=r(1978);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=o(o({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:u,removeXAxis:c,addYAxis:s,removeYAxis:f,addZAxis:h,removeZAxis:d,updateYAxisWidth:p}=l.actions,y=l.reducer},3518:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,U:()=>u});var n=r(4106),i=r(5540),a=r(63),o=r(4707),l=e=>e.brush,u=(0,n.Mz)([l,i.HZ,a.HK],(e,t,r)=>({height:e.height,x:(0,o.Et)(e.x)?e.x:t.left,y:(0,o.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(e.width)?e.width:t.width}))},3573:(e,t,r)=>{"use strict";r.d(t,{g:()=>c});var n=r(4106),i=r(6584),a=r(2e3),o=r(5540),l=r(5934),u=r(8766),c=(0,n.Mz)([(e,t)=>t,i.fz,u.D0,a.Re,a.gL,a.R4,l.r1,o.HZ],l.aX)},3733:(e,t,r)=>{"use strict";r.d(t,{x:()=>o,y:()=>a});var n=r(7681),i=r(2e3),a=(0,n.VP)("externalEvent"),o=(0,n.Nc)();o.startListening({actionCreator:a,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,i.eE)(r),activeDataKey:(0,i.Xb)(r),activeIndex:(0,i.A2)(r),activeLabel:(0,i.BZ)(r),activeTooltipIndex:(0,i.A2)(r),isTooltipActive:(0,i.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}})},3758:(e,t,r)=>{e.exports=r(4827).get},3834:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n})},3984:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},4006:(e,t,r)=>{"use strict";var n=r(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,u=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return l(function(){i.value=r,i.getSnapshot=t,c(i)&&s({inst:i})},[e,r,t]),o(function(){return c(i)&&s({inst:i}),e(function(){c(i)&&s({inst:i})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},4048:(e,t)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),u=Symbol.for("react.context"),c=Symbol.for("react.server_context"),s=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case i:case o:case a:case f:case h:return e;default:switch(e=e&&e.$$typeof){case c:case u:case s:case p:case d:case l:return e;default:return t}}case n:return t}}}(e)===i}},4061:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},4081:(e,t,r)=>{"use strict";r.d(t,{p:()=>c,v:()=>s});var n=r(2115),i=r(193),a=r(6518),o=r(1757);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){var t=(0,i.j)(),r=(0,n.useRef)(null);return(0,n.useEffect)(()=>{var n=u(u({},e),{},{stackId:(0,o.$8)(e.stackId)});null===r.current?t((0,a.g5)(n)):r.current!==n&&t((0,a.ZF)({prev:r.current,next:n})),r.current=n},[t,e]),(0,n.useEffect)(()=>()=>{r.current&&(t((0,a.Vi)(r.current)),r.current=null)},[t]),null}function s(e){var t=(0,i.j)();return(0,n.useEffect)(()=>(t((0,a.As)(e)),()=>{t((0,a.TK)(e))}),[t,e]),null}},4084:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},4090:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2795),i=r(329),a=r(1078);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},u=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:u.map(t=>{var r,n;return r=t,null==(n=e)||null==r?n:"object"==typeof r&&"key"in r?Object.hasOwn(n,r.key)?n[r.key]:l(n,r.path):"function"==typeof r?r(n):Array.isArray(r)?l(n,r):"object"==typeof n?n[r]:n})})).slice().sort((e,t)=>{for(let i=0;i<u.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},4106:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>w});var n=e=>Array.isArray(e)?e:[e],i=0,a=class{revision=i;_value;_lastValue;_isEqual=o;constructor(e,t=o){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function o(e,t){return e===t}function l(e){return e instanceof a||console.warn("Not a valid cell! ",e),e.value}var u=(e,t)=>!1;function c(){return function(e,t=o){return new a(null,t)}(0,u)}var s=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=c()),l(t)};Symbol();var f=0,h=Object.getPrototypeOf({}),d=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,p);tag=c();tags={};children={};collectionTag=null;id=f++},p={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in h)return n;if("object"==typeof n&&null!==n){var i;let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(i=n)?new y(i):new d(i)),r.tag&&l(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=c()).value=n),l(r),n}})(),ownKeys:e=>(s(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},y=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],v);tag=c();tags={};children={};collectionTag=null;id=f++},v={get:([e],t)=>("length"===t&&s(e),p.get(e,t)),ownKeys:([e])=>p.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>p.getOwnPropertyDescriptor(e,t),has:([e],t)=>p.has(e,t)},g="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function m(){return{s:0,v:void 0,o:null,p:null}}function b(e,t={}){let r,n=m(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=m(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=m(),e.set(t,o)):o=r}}let u=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new g(t):t}return u.s=1,u.v=t,t}return o.clearCache=()=>{n=m(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var w=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,o={},l=e.pop();"object"==typeof l&&(o=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:u,memoizeOptions:c=[],argsMemoize:s=b,argsMemoizeOptions:f=[],devModeChecks:h={}}={...r,...o},d=n(c),p=n(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),v=u(function(){return i++,l.apply(null,arguments)},...d);return Object.assign(s(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=v.apply(null,e)},...p),{resultFunc:l,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:u,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(b),x=Object.assign((e,t=w)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>x})},4125:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(e,t,r)=>r},4144:(e,t,r)=>{"use strict";r.d(t,{M:()=>f});var n=r(2115),i=r(3834),a=r(5658),o=r(5888),l=r(950);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,u=r>=0?1:-1,c=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(c,",").concat(e+u*s[0],",").concat(t)),a+="L ".concat(e+r-u*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(c,",\n        ").concat(e+r-u*s[2],",").concat(t+n)),a+="L ".concat(e+u*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var h=Math.min(o,i);a="M ".concat(e,",").concat(t+l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e+u*h,",").concat(t,"\n            L ").concat(e+r-u*h,",").concat(t,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e+r,",").concat(t+l*h,"\n            L ").concat(e+r,",").concat(t+n-l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e+r-u*h,",").concat(t+n,"\n            L ").concat(e+u*h,",").concat(t+n,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e,",").concat(t+n-l*h," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=e=>{var t=(0,o.e)(e,s),r=(0,n.useRef)(null),[f,h]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&h(e)}catch(e){}},[]);var{x:d,y:p,width:y,height:v,radius:g,className:m}=t,{animationEasing:b,animationDuration:w,animationBegin:x,isAnimationActive:O,isUpdateAnimationActive:P}=t;if(d!==+d||p!==+p||y!==+y||v!==+v||0===y||0===v)return null;var A=(0,i.$)("recharts-rectangle",m);return P?n.createElement(l.i,{canBegin:f>0,from:{width:y,height:v,x:d,y:p},to:{width:y,height:v,x:d,y:p},duration:w,animationEasing:b,isActive:P},e=>{var{width:i,height:o,x:s,y:h}=e;return n.createElement(l.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:w,isActive:O,easing:b},n.createElement("path",u({},(0,a.J9)(t,!0),{className:A,d:c(s,h,i,o,g),ref:r})))}):n.createElement("path",u({},(0,a.J9)(t,!0),{className:A,d:c(d,p,y,v,g)}))}},4171:(e,t,r)=>{"use strict";e.exports=r(4006)},4184:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(997);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},4227:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},4341:(e,t,r)=>{"use strict";r.d(t,{l3:()=>g,m7:()=>m});var n=r(2115),i=r(193),a=r(5276),o=new(r(2791)),l="recharts.syncEvent.tooltip",u="recharts.syncEvent.brush",c=r(8603),s=r(6948),f=r(5934),h=r(2e3);function d(e){return e.tooltip.syncInteraction}var p=r(6584),y=r(8273),v=()=>{};function g(){var e,t,r,f,d,g,m,b,w,x,O,P=(0,i.j)();(0,n.useEffect)(()=>{P((0,c.dl)())},[P]),e=(0,i.G)(a.lZ),t=(0,i.G)(a.pH),r=(0,i.j)(),f=(0,i.G)(a.hX),d=(0,i.G)(h.R4),g=(0,p.WX)(),m=(0,p.sk)(),b=(0,i.G)(e=>e.rootProps.className),(0,n.useEffect)(()=>{if(null==e)return v;var n=(n,i,a)=>{if(t!==a&&e===n){if("index"===f)return void r(i);if(null!=d){if("function"==typeof f){var o,l=f(d,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});o=d[l]}else"value"===f&&(o=d.find(e=>String(e.value)===i.payload.label));var{coordinate:u}=i.payload;if(null==o||!1===i.payload.active||null==u||null==m)return void r((0,s.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:c,y:h}=u,p=Math.min(c,m.x+m.width),y=Math.min(h,m.y+m.height),v={x:"horizontal"===g?o.coordinate:p,y:"horizontal"===g?y:o.coordinate};r((0,s.E1)({active:i.payload.active,coordinate:v,dataKey:i.payload.dataKey,index:String(o.index),label:i.payload.label}))}}};return o.on(l,n),()=>{o.off(l,n)}},[b,r,t,e,f,d,g,m]),w=(0,i.G)(a.lZ),x=(0,i.G)(a.pH),O=(0,i.j)(),(0,n.useEffect)(()=>{if(null==w)return v;var e=(e,t,r)=>{x!==r&&w===e&&O((0,y.M)(t))};return o.on(u,e),()=>{o.off(u,e)}},[O,x,w])}function m(e,t,r,u,c,h){var p=(0,i.G)(r=>(0,f.dp)(r,e,t)),y=(0,i.G)(a.pH),v=(0,i.G)(a.lZ),g=(0,i.G)(a.hX),m=(0,i.G)(d),b=null==m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=v&&null!=y){var e=(0,s.E1)({active:h,coordinate:r,dataKey:p,index:c,label:"number"==typeof u?String(u):u});o.emit(l,v,e,y)}},[b,r,p,c,u,y,v,g,h])}},4352:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4827);t.property=function(e){return function(t){return n.get(t,e)}}},4403:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(4707),i=r(1757);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(e,t,r,a,l,u,c)=>{if(null!=t&&null!=u){var{chartData:s,computedData:f,dataStartIndex:h,dataEndIndex:d}=r;return e.reduce((e,r)=>{var p,y,v,g,m,{dataDefinedOnItem:b,settings:w}=r,x=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((p=b,y=s,null!=p?p:y),h,d),O=null!=(v=null==w?void 0:w.dataKey)?v:null==a?void 0:a.dataKey,P=null==w?void 0:w.nameKey;return Array.isArray(g=null!=a&&a.dataKey&&Array.isArray(x)&&!Array.isArray(x[0])&&"axis"===c?(0,n.eP)(x,a.dataKey,l):u(x,t,f,P))?g.forEach(t=>{var r=o(o({},w),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,i.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,i.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,i.GF)({tooltipEntrySettings:w,dataKey:O,payload:g,value:(0,i.kr)(g,O),name:null!=(m=(0,i.kr)(g,P))?m:null==w?void 0:w.name})),e},[])}}},4529:(e,t,r)=>{"use strict";r.d(t,{r:()=>x});var n=r(2115),i=r(8603),a=r(12),o=r(6442),l=r(6653),u=r(7003),c=r(193),s=r(1105);function f(e){var t=(0,c.j)();return(0,n.useEffect)(()=>{t((0,s.U)(e))},[t,e]),null}var h=r(7891),d=r(5888),p=r(6790),y=["width","height","layout"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},m=(0,n.forwardRef)(function(e,t){var r,i=(0,d.e)(e.categoricalChartProps,g),{width:c,height:s,layout:m}=i,b=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,y);if(!(0,p.F)(c)||!(0,p.F)(s))return null;var{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:P}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:P,eventEmitter:void 0}},reduxStoreName:null!=(r=i.id)?r:w},n.createElement(o.TK,{chartData:i.data}),n.createElement(l.s,{width:c,height:s,layout:m,margin:i.margin}),n.createElement(u.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(f,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(h.L,v({width:c,height:s},b,{ref:t})))}),b=["item"],w={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},x=(0,n.forwardRef)((e,t)=>{var r=(0,d.e)(e,w);return n.createElement(m,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:i.uN,categoricalChartProps:r,ref:t})})},4597:(e,t,r)=>{"use strict";r.d(t,{h:()=>d});var n=r(2115),i=r(3834),a=r(5658),o=r(8983),l=r(4707),u=r(5888);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:l,cornerRadius:u,cornerIsExternal:c}=e,s=u*(l?1:-1)+n,f=Math.asin(u/s)/o.Kg,h=c?i:i+a*f,d=(0,o.IZ)(t,r,s,h);return{center:d,circleTangency:(0,o.IZ)(t,r,n,h),lineTangency:(0,o.IZ)(t,r,s*Math.cos(f*o.Kg),c?i-a*f:i),theta:f}},f=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:u}=e,c=((e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),359.999))(a,u),s=a+c,f=(0,o.IZ)(t,r,i,a),h=(0,o.IZ)(t,r,i,s),d="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(a>s),",\n    ").concat(h.x,",").concat(h.y,"\n  ");if(n>0){var p=(0,o.IZ)(t,r,n,a),y=(0,o.IZ)(t,r,n,s);d+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(a<=s),",\n            ").concat(p.x,",").concat(p.y," Z")}else d+="L ".concat(t,",").concat(r," Z");return d},h={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},d=e=>{var t,r=(0,u.e)(e,h),{cx:o,cy:d,innerRadius:p,outerRadius:y,cornerRadius:v,forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:w,className:x}=r;if(y<p||b===w)return null;var O=(0,i.$)("recharts-sector",x),P=y-p,A=(0,l.F4)(v,P,0,!0);return t=A>0&&360>Math.abs(b-w)?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:u,startAngle:c,endAngle:h}=e,d=(0,l.sA)(h-c),{circleTangency:p,lineTangency:y,theta:v}=s({cx:t,cy:r,radius:i,angle:c,sign:d,cornerRadius:a,cornerIsExternal:u}),{circleTangency:g,lineTangency:m,theta:b}=s({cx:t,cy:r,radius:i,angle:h,sign:-d,cornerRadius:a,cornerIsExternal:u}),w=u?Math.abs(c-h):Math.abs(c-h)-v-b;if(w<0)return o?"M ".concat(y.x,",").concat(y.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):f({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:h});var x="M ".concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(w>180),",").concat(+(d<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:P,theta:A}=s({cx:t,cy:r,radius:n,angle:c,sign:d,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),{circleTangency:j,lineTangency:E,theta:M}=s({cx:t,cy:r,radius:n,angle:h,sign:-d,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),S=u?Math.abs(c-h):Math.abs(c-h)-A-M;if(S<0&&0===a)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(j.x,",").concat(j.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(S>180),",").concat(+(d>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(P.x,",").concat(P.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x})({cx:o,cy:d,innerRadius:p,outerRadius:y,cornerRadius:Math.min(A,P/2),forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:w}):f({cx:o,cy:d,innerRadius:p,outerRadius:y,startAngle:b,endAngle:w}),n.createElement("path",c({},(0,a.J9)(r,!0),{className:O,d:t}))}},4620:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(2115);let i=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:u,className:c="",children:s,iconNode:f,...h}=e;return(0,n.createElement)("svg",{ref:t,...o,width:i,height:i,stroke:r,strokeWidth:u?24*Number(l)/Number(i):l,className:a("lucide",c),...!s&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(s)?s:[s]])}),u=(e,t)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:u,...c}=r;return(0,n.createElement)(l,{ref:o,iconNode:t,className:a("lucide-".concat(i(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),u),...c})});return r.displayName=i(e),r}},4625:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},4637:(e,t,r)=>{"use strict";e.exports=r(301)},4707:(e,t,r)=>{"use strict";r.d(t,{CG:()=>d,Dj:()=>p,Et:()=>u,F4:()=>h,M8:()=>o,NF:()=>f,Zb:()=>g,_3:()=>l,eP:()=>y,sA:()=>a,uy:()=>v,vh:()=>c});var n=r(3758),i=r.n(n),a=e=>0===e?0:e>0?1:-1,o=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,u=e=>("number"==typeof e||e instanceof Number)&&!o(e),c=e=>u(e)||"string"==typeof e,s=0,f=e=>{var t=++s;return"".concat(e||"").concat(t)},h=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!u(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return o(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},d=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},p=(e,t)=>u(e)&&u(t)?r=>e+r*(t-e):()=>t;function y(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var v=e=>null==e,g=e=>v(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},4827:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(634),i=r(2683),a=r(1154),o=r(1078);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var u=t,c=r,s=l;if(0===c.length)return s;let e=u;for(let t=0;t<c.length;t++){if(null==e||n.isUnsafeProperty(c[t]))return s;e=e[c[t]]}return void 0===e?s:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},4938:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},5276:(e,t,r)=>{"use strict";r.d(t,{JN:()=>n,_5:()=>i,eC:()=>l,gY:()=>a,hX:()=>s,iO:()=>u,lZ:()=>c,pH:()=>f,x3:()=>o});var n=e=>e.rootProps.maxBarSize,i=e=>e.rootProps.barGap,a=e=>e.rootProps.barCategoryGap,o=e=>e.rootProps.barSize,l=e=>e.rootProps.stackOffset,u=e=>e.options.chartName,c=e=>e.rootProps.syncId,s=e=>e.rootProps.syncMethod,f=e=>e.options.eventEmitter},5378:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(193),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},5540:(e,t,r)=>{"use strict";r.d(t,{c2:()=>g,HZ:()=>y,Ds:()=>v});var n=r(4106),i=r(3758),a=r.n(i),o=r(1679),l=r.n(o),u=e=>e.legend.settings;(0,n.Mz)([e=>e.legend.payload,u],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?l()(n,r):n});var c=r(1757),s=r(63),f=r(718),h=r(1523);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=(0,n.Mz)([s.Lp,s.A$,s.HK,e=>e.brush.height,f.h,f.W,u,e=>e.legend.size],(e,t,r,n,i,o,l,u)=>{var s=o.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:h.tQ;return p(p({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),f=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:p(p({},e),{},{[r]:a()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),d=p(p({},f),s),y=d.bottom;d.bottom+=n;var v=e-(d=(0,c.s0)(d,l,u)).left-d.right,g=t-d.top-d.bottom;return p(p({brushBottom:y},d),{},{width:Math.max(v,0),height:Math.max(g,0)})}),v=(0,n.Mz)(y,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),g=(0,n.Mz)(s.Lp,s.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},5642:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(1090),i=r(4625);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},5651:(e,t,r)=>{e.exports=r(9199).range},5658:(e,t,r)=>{"use strict";r.d(t,{J9:()=>p,aS:()=>d});var n=r(3758),i=r.n(n),a=r(2115),o=r(8052),l=r(4707),u=r(3439),c=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",s=null,f=null,h=e=>{if(e===s&&Array.isArray(f))return f;var t=[];return a.Children.forEach(e,e=>{(0,l.uy)(e)||((0,o.isFragment)(e)?t=t.concat(h(e.props.children)):t.push(e))}),f=t,s=e,t};function d(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>c(e)):[c(t)],h(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var p=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;((e,t,r,n)=>{var i,a=null!=(i=n&&(null===u.VU||void 0===u.VU?void 0:u.VU[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||u.QQ.includes(t))||r&&u.j2.includes(t)})(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i}},5695:(e,t,r)=>{"use strict";var n=r(8999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},5888:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{e:()=>i})},5934:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>D,aX:()=>z,dS:()=>C,dp:()=>_,fW:()=>O,pg:()=>T,r1:()=>E,u9:()=>N,yn:()=>I});var n=r(4106),i=r(1679),a=r.n(i),o=r(193),l=r(1757),u=r(7774),c=r(2e3),s=r(5276),f=r(6584),h=r(5540),d=r(63),p=r(3300),y=r(7718),v=r(423),g=r(678),m=r(8292),b=r(4084),w=r(3984),x=r(4403),O=()=>(0,o.G)(s.iO),P=(e,t)=>t,A=(e,t,r)=>r,j=(e,t,r,n)=>n,E=(0,n.Mz)(c.R4,e=>a()(e,e=>e.coordinate)),M=(0,n.Mz)([w.J,P,A,j],y.i),S=(0,n.Mz)([M,c.n4],v.P),_=(e,t,r)=>{if(null!=t){var n=(0,w.J)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},k=(0,n.Mz)([w.J,P,A,j],m.q),T=(0,n.Mz)([d.Lp,d.A$,f.fz,h.HZ,c.R4,j,k,b.x],g.o),C=(0,n.Mz)([M,T],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),D=(0,n.Mz)(c.R4,S,p.E),N=(0,n.Mz)([k,S,u.LF,c.Dn,D,b.x,P],x.N),I=(0,n.Mz)([M],e=>({isActive:e.active,activeIndex:e.index})),z=(e,t,r,n,i,a,o,u)=>{if(e&&t&&n&&i&&a){var c=(0,l.r4)(e.chartX,e.chartY,t,r,u);if(c){var s=(0,l.SW)(c,t),f=(0,l.gH)(s,o,a,n,i),h=(0,l.bk)(t,a,f,c);return{activeIndex:String(f),activeCoordinate:h}}}}},5963:(e,t,r)=>{"use strict";var n=r(2700).hp;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(1343),a=r(9270),o=r(6167),l=r(1143),u=r(7375);function c(e,t,r,i=new Map,f){let h=f?.(e,t,r,i);if(null!=h)return h;if(l.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,a]of(i.set(e,t),e))t.set(n,c(a,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(c(n,void 0,r,i,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(u.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),s(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(a.getTag(e)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),s(t,e,r,i,f),t}return e}function s(e,t,r=e,n,a){let o=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<o.length;i++){let l=o[i],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=c(t[l],l,r,n,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=s},6016:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5963),i=r(6167);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let u=t?.(r,a,o,l);if(null!=u)return u;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},6100:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},6142:(e,t,r)=>{"use strict";r.d(t,{u:()=>O});var n=r(2115),i=r(3758),a=r.n(i),o=r(3834);function l(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var u=r(7670),c=r(7273),s=r(7949),f=r(4707),h=r(3439),d=r(5658),p=r(2577),y=["viewBox"],v=["viewBox"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function x(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=w(e,y),i=this.props,{viewBox:a}=i,o=w(i,v);return!l(r,a)||!l(n,o)||!l(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:u,width:c,height:s,orientation:h,tickSize:d,mirror:p,tickMargin:y}=this.props,v=p?-1:1,g=e.tickSize||d,m=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(h){case"top":t=r=e.coordinate,o=(n=(i=u+!p*s)-v*g)-v*y,a=m;break;case"left":n=i=e.coordinate,a=(t=(r=l+!p*c)-v*g)-v*y,o=m;break;case"right":n=i=e.coordinate,a=(t=(r=l+p*c)+v*g)+v*y,o=m;break;default:t=r=e.coordinate,o=(n=(i=u+p*s)+v*g)+v*y,a=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:l,mirror:u,axisLine:c}=this.props,s=b(b(b({},(0,d.J9)(this.props,!1)),(0,d.J9)(c,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!u||"bottom"===l&&u);s=b(b({},s),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var h=+("left"===l&&!u||"right"===l&&u);s=b(b({},s),{},{x1:e+h*r,y1:t,x2:e+h*r,y2:t+i})}return n.createElement("line",g({},s,{className:(0,o.$)("recharts-cartesian-axis-line",a()(c,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,o.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,b(b({},t),{},{className:a}));else if("function"==typeof e)i=e(b(b({},t),{},{className:a}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,o.$)(l,e.className)),i=n.createElement(c.E,g({},t,{className:l}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:c,tickFormatter:s,unit:f}=this.props,y=(0,p.f)(b(b({},this.props),{},{ticks:r}),e,t),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),w=(0,d.J9)(this.props,!1),x=(0,d.J9)(c,!1),P=b(b({},w),{},{fill:"none"},(0,d.J9)(i,!1)),A=y.map((e,t)=>{var{line:r,tick:d}=this.getTickLineCoord(e),p=b(b(b(b({textAnchor:v,verticalAnchor:m},w),{},{stroke:"none",fill:l},x),d),{},{index:t,payload:e,visibleTicksCount:y.length,tickFormatter:s});return n.createElement(u.W,g({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,h.XC)(this.props,e,t)),i&&n.createElement("line",g({},P,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),c&&O.renderTickItem(c,p,"".concat("function"==typeof s?s(e.value,t):e.value).concat(f||"")))});return A.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},A):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(u.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),s.J.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}x(O,"displayName","CartesianAxis"),x(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},6151:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2683),i=r(7662),a=r(8879),o=r(1078);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},6167:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},6238:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},6442:(e,t,r)=>{"use strict";r.d(t,{TK:()=>l});var n=r(2115),i=r(8273),a=r(193),o=r(1037),l=e=>{var{chartData:t}=e,r=(0,a.j)(),l=(0,o.r)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,i.hq)(t)),()=>{r((0,i.hq)(void 0))}),[t,r,l]),null}},6518:(e,t,r)=>{"use strict";r.d(t,{As:()=>f,Ch:()=>l,TK:()=>h,Vi:()=>s,ZF:()=>c,g5:()=>u,iZ:()=>d,lm:()=>o});var n=r(7681),i=r(1978),a=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.h4)(t.payload))},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,a=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(r));a>-1&&(e.cartesianItems[a]=(0,i.h4)(n))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,i.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:o,removeBar:l,addCartesianGraphicalItem:u,replaceCartesianGraphicalItem:c,removeCartesianGraphicalItem:s,addPolarGraphicalItem:f,removePolarGraphicalItem:h}=a.actions,d=a.reducer},6584:(e,t,r)=>{"use strict";r.d(t,{W7:()=>s,WX:()=>p,fz:()=>d,rY:()=>h,sk:()=>u,yi:()=>f}),r(2115);var n=r(193),i=r(5540),a=r(63),o=r(1037),l=r(3518),u=()=>{var e,t=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),u=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&a&&u?{width:a.width-u.left-u.right,height:a.height-u.top-u.bottom,x:u.left,y:u.top}:r},c={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!=(e=(0,n.G)(i.HZ))?e:c},f=()=>(0,n.G)(a.Lp),h=()=>(0,n.G)(a.A$),d=e=>e.layout.layoutType,p=()=>(0,n.G)(d)},6609:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,X:()=>a});var n=r(2115),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},6653:(e,t,r)=>{"use strict";r.d(t,{s:()=>l});var n=r(2115),i=r(1037),a=r(1901),o=r(193);function l(e){var{layout:t,width:r,height:l,margin:u}=e,c=(0,o.j)(),s=(0,i.r)();return(0,n.useEffect)(()=>{s||(c((0,a.JK)(t)),c((0,a.gX)({width:r,height:l})),c((0,a.B_)(u)))},[c,s,t,r,l,u]),null}},6706:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(2115).createContext)(null)},6749:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},6790:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},6812:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(6016);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},6858:(e,t,r)=>{e.exports=r(2393).last},6948:(e,t,r)=>{"use strict";r.d(t,{E1:()=>v,En:()=>m,Ix:()=>l,ML:()=>d,Nt:()=>p,RD:()=>s,UF:()=>c,XB:()=>u,jF:()=>y,k_:()=>a,o4:()=>g,oP:()=>f,xS:()=>h});var n=r(7681),i=r(1978),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:u,setTooltipSettingsState:c,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:h,setActiveClickItemIndex:d,setMouseOverAxisIndex:p,setMouseClickAxisIndex:y,setSyncInteraction:v,setKeyboardInteraction:g}=o.actions,m=o.reducer},7003:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var n=r(2115),i=r(8679),a=r(193);function o(e){var t=(0,a.j)();return(0,n.useEffect)(()=>{t((0,i.mZ)(e))},[t,e]),null}},7016:(e,t)=>{t.read=function(e,t,r,n,i){var a,o,l=8*i-n-1,u=(1<<l)-1,c=u>>1,s=-7,f=r?i-1:0,h=r?-1:1,d=e[t+f];for(f+=h,a=d&(1<<-s)-1,d>>=-s,s+=l;s>0;a=256*a+e[t+f],f+=h,s-=8);for(o=a&(1<<-s)-1,a>>=-s,s+=n;s>0;o=256*o+e[t+f],f+=h,s-=8);if(0===a)a=1-c;else{if(a===u)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,n),a-=c}return(d?-1:1)*o*Math.pow(2,a-n)},t.write=function(e,t,r,n,i,a){var o,l,u,c=8*a-i-1,s=(1<<c)-1,f=s>>1,h=5960464477539062e-23*(23===i),d=n?0:a-1,p=n?1:-1,y=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(l=+!!isNaN(t),o=s):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+f>=1?t+=h/u:t+=h*Math.pow(2,1-f),t*u>=2&&(o++,u/=2),o+f>=s?(l=0,o=s):o+f>=1?(l=(t*u-1)*Math.pow(2,i),o+=f):(l=t*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;e[r+d]=255&l,d+=p,l/=256,i-=8);for(o=o<<i|l,c+=i;c>0;e[r+d]=255&o,d+=p,o/=256,c-=8);e[r+d-p]|=128*y}},7273:(e,t,r)=>{"use strict";r.d(t,{E:()=>M});var n=r(2115),i=r(3834),a=r(4707),o=r(593),l=r(5658),u=r(9471),c=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,h=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,d={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p=Object.keys(d);class y{static parse(e){var t,[,r,n]=null!=(t=h.exec(e))?t:[];return new y(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.M8)(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,a.M8)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),p.includes(t)&&(this.num=e*d[t],this.unit="px")}}function v(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=c.exec(t))?r:[],o=y.parse(null!=n?n:""),l=y.parse(null!=a?a:""),u="*"===i?o.multiply(l):o.divide(l);if(u.isNaN())return"NaN";t=t.replace(c,u.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,h,d,p]=null!=(f=s.exec(t))?f:[],v=y.parse(null!=h?h:""),g=y.parse(null!=p?p:""),m="+"===d?v.add(g):v.subtract(g);if(m.isNaN())return"NaN";t=t.replace(s,m.toString())}return t}var g=/\(([^()]*)\)/;function m(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=g.exec(r));){var[,n]=t;r=r.replace(g,v(n))}return r}(t),t=v(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],w=["dx","dy","angle","className","breakAll"];function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var P=/[ \f\n\r\t\v\u2028\u2029]+/,A=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,a.uy)(t)||(i=r?t.toString().split(""):t.toString().split(P));var o=i.map(e=>({word:e,width:(0,u.P)(e,n).width})),l=r?0:(0,u.P)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:l}}catch(e){return null}},j=e=>[{words:(0,a.uy)(e)?[]:e.toString().split(P)}],E="#808080",M=(0,n.forwardRef)((e,t)=>{var r,{x:u=0,y:c=0,lineHeight:s="1em",capHeight:f="0.71em",scaleToFit:h=!1,textAnchor:d="start",verticalAnchor:p="end",fill:y=E}=e,v=O(e,b),g=(0,n.useMemo)(()=>(e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:l,maxLines:u}=e;if((t||r)&&!o.m.isSsr){var c=A({breakAll:l,children:n,style:i});if(!c)return j(n);var{wordsWithComputedWidth:s,spaceWidth:f}=c;return((e,t,r,n,i)=>{var o,{maxLines:l,children:u,style:c,breakAll:s}=e,f=(0,a.Et)(l),h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},d=h(t),p=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(d.length>l||p(d).width>Number(n)))return d;for(var y=e=>{var t=h(A({breakAll:s,style:c,children:u.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||p(t).width>Number(n),t]},v=0,g=u.length-1,m=0;v<=g&&m<=u.length-1;){var b=Math.floor((v+g)/2),[w,x]=y(b-1),[O]=y(b);if(w||O||(v=b+1),w&&O&&(g=b-1),!w&&O){o=x;break}m++}return o||d})({breakAll:l,children:n,maxLines:u,style:i},s,f,t,r)}return j(n)})({breakAll:v.breakAll,children:v.children,maxLines:v.maxLines,scaleToFit:h,style:v.style,width:v.width}),[v.breakAll,v.children,v.maxLines,h,v.style,v.width]),{dx:P,dy:M,angle:S,className:_,breakAll:k}=v,T=O(v,w);if(!(0,a.vh)(u)||!(0,a.vh)(c))return null;var C=u+((0,a.Et)(P)?P:0),D=c+((0,a.Et)(M)?M:0);switch(p){case"start":r=m("calc(".concat(f,")"));break;case"middle":r=m("calc(".concat((g.length-1)/2," * -").concat(s," + (").concat(f," / 2))"));break;default:r=m("calc(".concat(g.length-1," * -").concat(s,")"))}var N=[];if(h){var I=g[0].width,{width:z}=v;N.push("scale(".concat((0,a.Et)(z)?z/I:1,")"))}return S&&N.push("rotate(".concat(S,", ").concat(C,", ").concat(D,")")),N.length&&(T.transform=N.join(" ")),n.createElement("text",x({},(0,l.J9)(T,!0),{ref:t,x:C,y:D,className:(0,i.$)("recharts-text",_),textAnchor:d,fill:y.includes("url")?E:y}),g.map((e,t)=>{var i=e.words.join(k?"":" ");return n.createElement("tspan",{x:C,dy:0===t?r:s,key:"".concat(i,"-").concat(t)},i)}))});M.displayName="Text"},7375:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},7428:(e,t,r)=>{"use strict";r.d(t,{d:()=>C});var n=r(2115),i=r(1145),a=r(4707),o=r(5658),l=r(1757),u=r(2577),c=r(6142),s=r(6584),f=r(2775),h=r(193),d=r(1037),p=r(5888),y=["x1","y1","x2","y2","key"],v=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var P=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:o,height:l,ry:u}=e;return n.createElement("rect",{x:i,y:a,ry:u,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function A(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:a,x2:l,y2:u,key:c}=t,s=O(t,y),f=(0,o.J9)(s,!1),{offset:h}=f,d=O(f,v);r=n.createElement("line",x({},d,{x1:i,y1:a,x2:l,y2:u,fill:"none",key:c}))}return r}function j(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,u=O(e,g),c=a.map((e,n)=>A(i,w(w({},u),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function E(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,u=O(e,m),c=a.map((e,n)=>A(i,w(w({},u),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function M(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:o,height:l,horizontalPoints:u,horizontal:c=!0}=e;if(!c||!t||!t.length)return null;var s=u.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,u)=>{var c=s[u+1]?s[u+1]-e:a+l-e;if(c<=0)return null;var f=u%t.length;return n.createElement("rect",{key:"react-".concat(u),y:e,x:i,height:c,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function S(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:l,height:u,verticalPoints:c}=e;if(!t||!r||!r.length)return null;var s=c.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,t)=>{var c=s[t+1]?s[t+1]-e:a+l-e;if(c<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:c,height:u,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var _=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,u.f)(w(w(w({},c.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},k=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,u.f)(w(w(w({},c.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function C(e){var t=(0,s.yi)(),r=(0,s.rY)(),o=(0,s.W7)(),l=w(w({},(0,p.e)(e,T)),{},{x:(0,a.Et)(e.x)?e.x:o.left,y:(0,a.Et)(e.y)?e.y:o.top,width:(0,a.Et)(e.width)?e.width:o.width,height:(0,a.Et)(e.height)?e.height:o.height}),{xAxisId:u,yAxisId:c,x:y,y:v,width:g,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:A}=l,C=(0,d.r)(),D=(0,h.G)(e=>(0,f.ZB)(e,"xAxis",u,C)),N=(0,h.G)(e=>(0,f.ZB)(e,"yAxis",c,C));if(!(0,a.Et)(g)||g<=0||!(0,a.Et)(m)||m<=0||!(0,a.Et)(y)||y!==+y||!(0,a.Et)(v)||v!==+v)return null;var I=l.verticalCoordinatesGenerator||_,z=l.horizontalCoordinatesGenerator||k,{horizontalPoints:R,verticalPoints:L}=l;if((!R||!R.length)&&"function"==typeof z){var U=O&&O.length,B=z({yAxis:N?w(w({},N),{},{ticks:U?O:N.ticks}):void 0,width:t,height:r,offset:o},!!U||b);(0,i.R)(Array.isArray(B),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof B,"]")),Array.isArray(B)&&(R=B)}if((!L||!L.length)&&"function"==typeof I){var $=A&&A.length,F=I({xAxis:D?w(w({},D),{},{ticks:$?A:D.ticks}):void 0,width:t,height:r,offset:o},!!$||b);(0,i.R)(Array.isArray(F),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof F,"]")),Array.isArray(F)&&(L=F)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(P,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(M,x({},l,{horizontalPoints:R})),n.createElement(S,x({},l,{verticalPoints:L})),n.createElement(j,x({},l,{offset:o,horizontalPoints:R,xAxis:D,yAxis:N})),n.createElement(E,x({},l,{offset:o,verticalPoints:L,xAxis:D,yAxis:N})))}C.displayName="CartesianGrid"},7536:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4090),i=r(1864),a=r(7583);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},7583:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7662),i=r(2621),a=r(706),o=r(9578);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},7662:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},7670:(e,t,r)=>{"use strict";r.d(t,{W:()=>u});var n=r(2115),i=r(3834),a=r(5658),o=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=n.forwardRef((e,t)=>{var{children:r,className:u}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),s=(0,i.$)("recharts-layer",u);return n.createElement("g",l({className:s},(0,a.J9)(c,!0),{ref:t}),r)})},7681:(e,t,r)=>{"use strict";r.d(t,{U1:()=>d,VP:()=>u,Nc:()=>q,Z0:()=>g});var n=r(3258);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var a=i(),o=r(1978),l=(r(9538),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)});function u(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(V(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var c=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function s(e){return(0,o.a6)(e)?(0,o.jM)(e,()=>{}):e}function f(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var h=e=>t=>{setTimeout(t,e)};function d(e){let t,r,o,u=function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},l=new c;return t&&("boolean"==typeof t?l.push(a):l.push(i(t.extraArgument))),l},{reducer:s,middleware:f,devTools:d=!0,duplicateMiddlewareCheck:p=!0,preloadedState:y,enhancers:v}=e||{};if("function"==typeof s)t=s;else if((0,n.Qd)(s))t=(0,n.HY)(s);else throw Error(V(1));r="function"==typeof f?f(u):u();let g=n.Zz;d&&(g=l({trace:!1,..."object"==typeof d&&d}));let m=(o=(0,n.Tw)(...r),function(e){let{autoBatch:t=!0}=e??{},r=new c(o);return t&&r.push(((e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,u="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:h(10):"callback"===e.type?e.queueNotification:h(e.timeout),c=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,u(c)),n.dispatch(e)}finally{i=!0}}})})("object"==typeof t?t:void 0)),r}),b=g(..."function"==typeof v?v(m):m());return(0,n.y$)(t,y,b)}function p(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(V(28));if(n in r)throw Error(V(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var y=Symbol.for("rtk-slice-createasyncthunk"),v=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(v||{}),g=function({creators:e}={}){let t=e?.asyncThunk?.[y];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(V(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(a),c={},h={},d={},y=[],v={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(V(12));if(r in h)throw Error(V(13));return h[r]=t,v},addMatcher:(e,t)=>(y.push({matcher:e,reducer:t}),v),exposeAction:(e,t)=>(d[e]=t,v),exposeCaseReducer:(e,t)=>(c[e]=t,v)};function g(){let[t={},r=[],n]="function"==typeof e.extraReducers?p(e.extraReducers):[e.extraReducers],i={...t,...h};return function(e,t){let r,[n,i,a]=p(t);if("function"==typeof e)r=()=>s(e());else{let t=s(e);r=()=>t}function l(e=r(),t){let u=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===u.filter(e=>!!e).length&&(u=[a]),u.reduce((e,r)=>{if(r)if((0,o.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,o.a6)(e))return(0,o.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return l.getInitialState=r,l}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of y)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}l.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(V(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:u,settled:c,options:s}=r,f=i(e,a,s);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),u&&n.addCase(f.rejected,u),c&&n.addMatcher(f.settled,c),n.exposeCaseReducer(t,{fulfilled:o||m,pending:l||m,rejected:u||m,settled:c||m})}(o,i,v,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(V(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?u(e,o):u(e))}(o,i,v)});let b=e=>e,w=new Map,x=new WeakMap;function O(e,t){return r||(r=g()),r(e,t)}function P(){return r||(r=g()),r.getInitialState()}function A(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=f(x,n,P)),i}function i(t=b){let n=f(w,r,()=>new WeakMap);return f(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>f(x,t,P),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let j={name:n,reducer:O,actions:d,caseReducers:c,getInitialState:P,...A(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:O},r),{...j,...A(n,!0)}}};return j}}();function m(){}var b="listener",w="completed",x="cancelled",O=`task-${x}`,P=`task-${w}`,A=`${b}-${x}`,j=`${b}-${w}`,E=class{constructor(e){this.code=e,this.message=`task ${x} (reason: ${e})`}name="TaskAbortError";message},M=(e,t)=>{if("function"!=typeof e)throw TypeError(V(32))},S=()=>{},_=(e,t=S)=>(e.catch(t),e),k=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),T=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},C=e=>{if(e.aborted){let{reason:t}=e;throw new E(t)}};function D(e,t){let r=S;return new Promise((n,i)=>{let a=()=>i(new E(e.reason));if(e.aborted)return void a();r=k(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=S})}var N=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof E?"cancelled":"rejected",error:e}}finally{t?.()}},I=e=>t=>_(D(e,t).then(t=>(C(e),t))),z=e=>{let t=I(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:R}=Object,L={},U="listenerMiddleware",B=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=u(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(V(21));return M(a,"options.listener"),{predicate:i,type:t,effect:a}},$=R(e=>{let{type:t,predicate:r,effect:n}=B(e);return{id:((e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t})(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(V(22))}}},{withTypes:()=>$}),F=(e,t)=>{let{type:r,effect:n,predicate:i}=B(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},K=e=>{e.pending.forEach(e=>{T(e,A)})},H=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},Z=R(u(`${U}/add`),{withTypes:()=>Z}),G=u(`${U}/removeAll`),Y=R(u(`${U}/remove`),{withTypes:()=>Y}),W=(...e)=>{console.error(`${U}/error`,...e)},q=(e={})=>{let t=new Map,{extra:r,onError:i=W}=e;M(i,"onError");let a=e=>(e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&K(e)}))(F(t,e)??$(e));R(a,{withTypes:()=>a});let o=e=>{let r=F(t,e);return r&&(r.unsubscribe(),e.cancelActive&&K(r)),!!r};R(o,{withTypes:()=>o});let l=async(e,n,o,l)=>{let u=new AbortController,c=((e,t)=>{let r=async(r,n)=>{C(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await D(t,Promise.race(a));return C(t),e}finally{i()}};return(e,t)=>_(r(e,t))})(a,u.signal),s=[];try{e.pending.add(u),await Promise.resolve(e.effect(n,R({},o,{getOriginalState:l,condition:(e,t)=>c(e,t).then(Boolean),take:c,delay:z(u.signal),pause:I(u.signal),extra:r,signal:u.signal,fork:((e,t)=>(r,n)=>{M(r,"taskExecutor");let i=new AbortController;k(e,()=>T(i,e.reason));let a=N(async()=>{C(e),C(i.signal);let t=await r({pause:I(i.signal),delay:z(i.signal),signal:i.signal});return C(i.signal),t},()=>T(i,P));return n?.autoJoin&&t.push(a.catch(S)),{result:I(e)(a),cancel(){T(i,O)}}})(u.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==u&&(T(e,A),r.delete(e))})},cancel:()=>{T(u,A),e.pending.delete(u)},throwIfCancelled:()=>{C(u.signal)}})))}catch(e){e instanceof E||H(i,e,{raisedBy:"effect"})}finally{await Promise.all(s),T(u,j),e.pending.delete(u)}},u=(e=>()=>{e.forEach(K),e.clear()})(t);return{middleware:e=>r=>c=>{let s;if(!(0,n.ve)(c))return r(c);if(Z.match(c))return a(c.payload);if(G.match(c))return void u();if(Y.match(c))return o(c.payload);let f=e.getState(),h=()=>{if(f===L)throw Error(V(23));return f};try{if(s=r(c),t.size>0){let r=e.getState();for(let n of Array.from(t.values())){let t=!1;try{t=n.predicate(c,r,f)}catch(e){t=!1,H(i,e,{raisedBy:"predicate"})}t&&l(n,c,e,h)}}}finally{f=L}return s},startListening:a,stopListening:o,clearListeners:u}};function V(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original")},7718:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(6948);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r,i)=>{if(null==t)return n.k_;var o=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==o)return n.k_;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=o.index){if(l)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},7774:(e,t,r)=>{"use strict";r.d(t,{HS:()=>o,LF:()=>i,z3:()=>a});var n=r(4106),i=e=>e.chartData,a=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,n)=>n?a(e):i(e)},7831:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2);t.debounce=function(e,t=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:a=!1,trailing:o=!0,maxWait:l}=r,u=[,,];a&&(u[0]="leading"),o&&(u[1]="trailing");let c=null,s=n.debounce(function(...t){i=e.apply(this,t),c=null},t,{edges:u}),f=function(...t){return null!=l&&(null===c&&(c=Date.now()),Date.now()-c>=l)?(i=e.apply(this,t),c=Date.now(),s.cancel(),s.schedule(),i):(s.apply(this,t),i)};return f.cancel=s.cancel,f.flush=()=>(s.flush(),i),f}},7891:(e,t,r)=>{"use strict";r.d(t,{L:()=>L});var n=r(2115),i=r(5658),a=r(6584),o=r(5378),l=r(1037),u=r(3834),c=["children","width","height","viewBox","className","style","title","desc"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var f=(0,n.forwardRef)((e,t)=>{var{children:r,width:a,height:o,viewBox:l,className:f,style:h,title:d,desc:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,c),v=l||{width:a,height:o,x:0,y:0},g=(0,u.$)("recharts-surface",f);return n.createElement("svg",s({},(0,i.J9)(y,!0,"svg"),{className:g,width:a,height:o,style:h,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height),ref:t}),n.createElement("title",null,d),n.createElement("desc",null,p),r)}),h=r(193),d=r(3518),p=r(6790),y=["children"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={width:"100%",height:"100%"},m=(0,n.forwardRef)((e,t)=>{var r,i,l=(0,a.yi)(),u=(0,a.rY)(),c=(0,o.$)();if(!(0,p.F)(l)||!(0,p.F)(u))return null;var{children:s,otherAttributes:h,title:d,desc:y}=e;return r="number"==typeof h.tabIndex?h.tabIndex:c?0:void 0,i="string"==typeof h.role?h.role:c?"application":void 0,n.createElement(f,v({},h,{title:d,desc:y,role:i,tabIndex:r,width:l,height:u,style:g,ref:t}),s)}),b=e=>{var{children:t}=e,r=(0,h.G)(d.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(f,{width:i,height:a,x:l,y:o},t)},w=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y);return(0,l.r)()?n.createElement(b,null,r):n.createElement(m,v({ref:t},i),r)}),x=r(6948),O=r(3279),P=r(4341),A=r(1407),j=r(63),E=r(1901),M=r(3733),S=r(2782),_=r(6609),k=(0,n.createContext)(null);function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var C=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:l,onDoubleClick:c,onMouseDown:s,onMouseEnter:f,onMouseLeave:d,onMouseMove:y,onMouseUp:v,onTouchEnd:g,onTouchMove:m,onTouchStart:b,style:w,width:C}=e,D=(0,h.j)(),[N,I]=(0,n.useState)(null),[z,R]=(0,n.useState)(null);(0,P.l3)();var L=function(){var e=(0,h.j)(),[t,r]=(0,n.useState)(null),i=(0,h.G)(j.et);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,p.H)(r)&&r!==i&&e((0,E.hF)(r))}},[t,e,i]),r}(),U=(0,n.useCallback)(e=>{L(e),"function"==typeof t&&t(e),I(e),R(e)},[L,t,I,R]),B=(0,n.useCallback)(e=>{D((0,O.ky)(e)),D((0,M.y)({handler:o,reactEvent:e}))},[D,o]),$=(0,n.useCallback)(e=>{D((0,O.dj)(e)),D((0,M.y)({handler:f,reactEvent:e}))},[D,f]),F=(0,n.useCallback)(e=>{D((0,x.xS)()),D((0,M.y)({handler:d,reactEvent:e}))},[D,d]),K=(0,n.useCallback)(e=>{D((0,O.dj)(e)),D((0,M.y)({handler:y,reactEvent:e}))},[D,y]),H=(0,n.useCallback)(()=>{D((0,A.Ru)())},[D]),Z=(0,n.useCallback)(e=>{D((0,A.uZ)(e.key))},[D]),G=(0,n.useCallback)(e=>{D((0,M.y)({handler:l,reactEvent:e}))},[D,l]),Y=(0,n.useCallback)(e=>{D((0,M.y)({handler:c,reactEvent:e}))},[D,c]),W=(0,n.useCallback)(e=>{D((0,M.y)({handler:s,reactEvent:e}))},[D,s]),q=(0,n.useCallback)(e=>{D((0,M.y)({handler:v,reactEvent:e}))},[D,v]),V=(0,n.useCallback)(e=>{D((0,M.y)({handler:b,reactEvent:e}))},[D,b]),J=(0,n.useCallback)(e=>{D((0,S.e)(e)),D((0,M.y)({handler:m,reactEvent:e}))},[D,m]),X=(0,n.useCallback)(e=>{D((0,M.y)({handler:g,reactEvent:e}))},[D,g]);return n.createElement(_.$.Provider,{value:N},n.createElement(k.Provider,{value:z},n.createElement("div",{className:(0,u.$)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:C,height:a},w),onClick:B,onContextMenu:G,onDoubleClick:Y,onFocus:H,onKeyDown:Z,onMouseDown:W,onMouseEnter:$,onMouseLeave:F,onMouseMove:K,onMouseUp:q,onTouchEnd:X,onTouchMove:J,onTouchStart:V,ref:U},r)))}),D=r(4707),N=r(8180),I=(0,n.createContext)(void 0),z=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,D.NF)("recharts"),"-clip")),i=(0,N.oM)();if(null==i)return null;var{x:a,y:o,width:l,height:u}=i;return n.createElement(I.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:a,y:o,height:u,width:l}))),t)},R=["children","className","width","height","style","compact","title","desc"],L=(0,n.forwardRef)((e,t)=>{var{children:r,className:a,width:o,height:l,style:u,compact:c,title:s,desc:f}=e,h=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,R),d=(0,i.J9)(h,!1);return c?n.createElement(w,{otherAttributes:d,title:s,desc:f},r):n.createElement(C,{className:a,style:u,width:o,height:l,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(w,{otherAttributes:d,title:s,desc:f,ref:t},n.createElement(z,null,r)))})},7949:(e,t,r)=>{"use strict";r.d(t,{J:()=>g,Z:()=>v});var n=r(2115),i=r(3834),a=r(7273),o=r(5658),l=r(4707),u=r(8983),c=r(6584),s=["offset"],f=["labelRef"];function h(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v=e=>null!=e&&"function"==typeof e;function g(e){var t,{offset:r=5}=e,d=p({offset:r},h(e,s)),{viewBox:v,position:g,value:m,children:b,content:w,className:x="",textBreakAll:O,labelRef:P}=d,A=(0,c.sk)(),j=v||A;if(!j||(0,l.uy)(m)&&(0,l.uy)(b)&&!(0,n.isValidElement)(w)&&"function"!=typeof w)return null;if((0,n.isValidElement)(w)){var{labelRef:E}=d,M=h(d,f);return(0,n.cloneElement)(w,M)}if("function"==typeof w){if(t=(0,n.createElement)(w,d),(0,n.isValidElement)(t))return t}else t=(e=>{var{value:t,formatter:r}=e,n=(0,l.uy)(e.children)?t:e.children;return"function"==typeof r?r(n):n})(d);var S="cx"in j&&(0,l.Et)(j.cx),_=(0,o.J9)(d,!0);if(S&&("insideStart"===g||"insideEnd"===g||"end"===g))return((e,t,r)=>{let a,o;var c,s,{position:f,viewBox:h,offset:d,className:p}=e,{cx:v,cy:g,innerRadius:m,outerRadius:b,startAngle:w,endAngle:x,clockWise:O}=h,P=(m+b)/2,A=(a=w,o=x,(0,l.sA)(o-a)*Math.min(Math.abs(o-a),360)),j=A>=0?1:-1;"insideStart"===f?(c=w+j*d,s=O):"insideEnd"===f?(c=x-j*d,s=!O):"end"===f&&(c=x+j*d,s=O),s=A<=0?s:!s;var E=(0,u.IZ)(v,g,P,c),M=(0,u.IZ)(v,g,P,c+(s?1:-1)*359),S="M".concat(E.x,",").concat(E.y,"\n    A").concat(P,",").concat(P,",0,1,").concat(+!s,",\n    ").concat(M.x,",").concat(M.y),_=(0,l.uy)(e.id)?(0,l.NF)("recharts-radial-line-"):e.id;return n.createElement("text",y({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",p)}),n.createElement("defs",null,n.createElement("path",{id:_,d:S})),n.createElement("textPath",{xlinkHref:"#".concat(_)},t))})(d,t,_);var k=S?(e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:c,endAngle:s}=t,f=(c+s)/2;if("outside"===n){var{x:h,y:d}=(0,u.IZ)(i,a,l+r,f);return{x:h,y:d,textAnchor:h>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y}=(0,u.IZ)(i,a,(o+l)/2,f);return{x:p,y,textAnchor:"middle",verticalAnchor:"middle"}})(d):((e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:u,height:c}=t,s=c>=0?1:-1,f=s*n,h=s>0?"end":"start",d=s>0?"start":"end",y=u>=0?1:-1,v=y*n,g=y>0?"end":"start",m=y>0?"start":"end";if("top"===i)return p(p({},{x:a+u/2,y:o-s*n,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(o-r.y,0),width:u}:{});if("bottom"===i)return p(p({},{x:a+u/2,y:o+c+f,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(o+c),0),width:u}:{});if("left"===i){var b={x:a-v,y:o+c/2,textAnchor:g,verticalAnchor:"middle"};return p(p({},b),r?{width:Math.max(b.x-r.x,0),height:c}:{})}if("right"===i){var w={x:a+u+v,y:o+c/2,textAnchor:m,verticalAnchor:"middle"};return p(p({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:c}:{})}var x=r?{width:u,height:c}:{};return"insideLeft"===i?p({x:a+v,y:o+c/2,textAnchor:m,verticalAnchor:"middle"},x):"insideRight"===i?p({x:a+u-v,y:o+c/2,textAnchor:g,verticalAnchor:"middle"},x):"insideTop"===i?p({x:a+u/2,y:o+f,textAnchor:"middle",verticalAnchor:d},x):"insideBottom"===i?p({x:a+u/2,y:o+c-f,textAnchor:"middle",verticalAnchor:h},x):"insideTopLeft"===i?p({x:a+v,y:o+f,textAnchor:m,verticalAnchor:d},x):"insideTopRight"===i?p({x:a+u-v,y:o+f,textAnchor:g,verticalAnchor:d},x):"insideBottomLeft"===i?p({x:a+v,y:o+c-f,textAnchor:m,verticalAnchor:h},x):"insideBottomRight"===i?p({x:a+u-v,y:o+c-f,textAnchor:g,verticalAnchor:h},x):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?p({x:a+(0,l.F4)(i.x,u),y:o+(0,l.F4)(i.y,c),textAnchor:"end",verticalAnchor:"end"},x):p({x:a+u/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},x)})(d,j);return n.createElement(a.E,y({ref:P,className:(0,i.$)("recharts-label",x)},_,k,{breakAll:O}),t)}g.displayName="Label";var m=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:u,innerRadius:c,outerRadius:s,x:f,y:h,top:d,left:p,width:y,height:v,clockWise:g,labelViewBox:m}=e;if(m)return m;if((0,l.Et)(y)&&(0,l.Et)(v)){if((0,l.Et)(f)&&(0,l.Et)(h))return{x:f,y:h,width:y,height:v};if((0,l.Et)(d)&&(0,l.Et)(p))return{x:d,y:p,width:y,height:v}}return(0,l.Et)(f)&&(0,l.Et)(h)?{x:f,y:h,width:0,height:0}:(0,l.Et)(t)&&(0,l.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:s||u||o||0,clockWise:g}:e.viewBox?e.viewBox:void 0};g.parseViewBox=m,g.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,u=m(e),c=(0,o.aS)(i,g).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||u,key:"label-".concat(r)}));return r?[((e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(g,y({key:"label-implicit"},i)):(0,l.vh)(e)?n.createElement(g,y({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===g?(0,n.cloneElement)(e,p({key:"label-implicit"},i)):n.createElement(g,y({key:"label-implicit",content:e},i)):v(e)?n.createElement(g,y({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(g,y({},e,{key:"label-implicit"},i)):null})(e.label,t||u,a),...c]:c}},8047:(e,t,r)=>{"use strict";r.d(t,{W:()=>b});var n=r(2115),i=r(3834),a=r(6142),o=r(193),l=r(3472),u=r(2775),c=r(5540),s=r(1037),f=["children"],h=["dangerouslySetInnerHTML","ticks"];function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function v(e){var t=(0,o.j)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return y(e,f)},[e]),i=(0,o.G)(e=>(0,u.Rl)(e,r.id)),a=r===i;return((0,n.useEffect)(()=>(t((0,l.Vi)(r)),()=>{t((0,l.MC)(r))}),[r,t]),a)?e.children:null}var g=e=>{var{xAxisId:t,className:r}=e,l=(0,o.G)(c.c2),f=(0,s.r)(),d="xAxis",v=(0,o.G)(e=>(0,u.iV)(e,d,t,f)),g=(0,o.G)(e=>(0,u.Zi)(e,d,t,f)),m=(0,o.G)(e=>(0,u.Lw)(e,t)),b=(0,o.G)(e=>(0,u.L$)(e,t));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:w,ticks:x}=e,O=y(e,h);return n.createElement(a.u,p({},O,{scale:v,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.$)("recharts-".concat(d," ").concat(d),r),viewBox:l,ticks:g}))},m=e=>{var t,r,i,a,o;return n.createElement(v,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter},n.createElement(g,e))};class b extends n.Component{render(){return n.createElement(m,this.props)}}d(b,"displayName","XAxis"),d(b,"defaultProps",{allowDataOverflow:u.PU.allowDataOverflow,allowDecimals:u.PU.allowDecimals,allowDuplicatedCategory:u.PU.allowDuplicatedCategory,height:u.PU.height,hide:!1,mirror:u.PU.mirror,orientation:u.PU.orientation,padding:u.PU.padding,reversed:u.PU.reversed,scale:u.PU.scale,tickCount:u.PU.tickCount,type:u.PU.type,xAxisId:0})},8052:(e,t,r)=>{"use strict";e.exports=r(4048)},8101:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},8136:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7831);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},8138:(e,t,r)=>{e.exports=r(5642).isEqual},8180:(e,t,r)=>{"use strict";r.d(t,{oM:()=>d,ZI:()=>f,gi:()=>h});var n=r(2775),i=r(193),a=r(1037),o=r(4106),l=r(5540),u=(0,o.Mz)([l.HZ],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),c=r(63),s=(0,o.Mz)([u,c.Lp,c.A$],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),f=e=>{var t=(0,a.r)();return(0,i.G)(r=>(0,n.Gx)(r,"xAxis",e,t))},h=e=>{var t=(0,a.r)();return(0,i.G)(r=>(0,n.Gx)(r,"yAxis",e,t))},d=()=>(0,i.G)(s)},8273:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>a,hq:()=>i});var n=(0,r(7681).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,l=n.reducer},8292:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},8369:(e,t,r)=>{"use strict";r.d(t,{E:()=>g});var n=r(2115),i=r(8603),a=r(12),o=r(6442),l=r(6653),u=r(7003),c=r(7891),s=r(5888),f=r(6790),h=["width","height"];function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var p={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y=(0,n.forwardRef)(function(e,t){var r,i=(0,s.e)(e.categoricalChartProps,p),{width:y,height:v}=i,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,h);if(!(0,f.F)(y)||!(0,f.F)(v))return null;var{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,categoricalChartProps:O}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,eventEmitter:void 0}},reduxStoreName:null!=(r=O.id)?r:m},n.createElement(o.TK,{chartData:O.data}),n.createElement(l.s,{width:y,height:v,layout:i.layout,margin:i.margin}),n.createElement(u.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(c.L,d({},g,{width:y,height:v,ref:t})))}),v=["axis","item"],g=(0,n.forwardRef)((e,t)=>n.createElement(y,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:v,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},8503:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(4620).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8603:(e,t,r)=>{"use strict";r.d(t,{dl:()=>u,lJ:()=>l,uN:()=>a});var n=r(7681),i=r(4707);function a(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var o=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:u}=o.actions},8658:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2621),i=r(4061);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},8679:(e,t,r)=>{"use strict";r.d(t,{mZ:()=>l,vE:()=>o});var n=r(7681),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},a=(0,n.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:i.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),o=a.reducer,{updateOptions:l}=a.actions},8751:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5963);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},8766:(e,t,r)=>{"use strict";r.d(t,{Be:()=>v,Cv:()=>O,D0:()=>A,Gl:()=>g,Dc:()=>P});var n=r(4106),i=r(63),a=r(5540),o=r(8983),l=r(4707),u={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},c={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},s=r(6100),f=r(6584),h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:u.reversed,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:u.type,unit:void 0},d={allowDataOverflow:c.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:c.tickCount,ticks:void 0,type:c.type,unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:c.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:c.tickCount,ticks:void 0,type:"category",unit:void 0},v=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?p:h,g=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?y:d,m=e=>e.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,a.HZ],o.lY),w=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),x=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),O=(0,n.Mz)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.Mz)([v,O],s.I);var P=(0,n.Mz)([b,w,x],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.Mz)([g,P],s.I);var A=(0,n.Mz)([f.fz,m,w,x,i.Lp,i.A$],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:u,startAngle:c,endAngle:s}=t;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(u,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}})},8844:(e,t,r)=>{"use strict";r.d(t,{y:()=>Y});var n=r(2115),i=r(749),a=r.n(i),o=r(4144),l=r(3834),u=r(5658),c=r(5888),s=r(950);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var h=(e,t,r,n,i)=>{var a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+i)+"L ".concat(e+r-a/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},d={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},p=e=>{var t=(0,c.e)(e,d),r=(0,n.useRef)(),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:o,y:p,upperWidth:y,lowerWidth:v,height:g,className:m}=t,{animationEasing:b,animationDuration:w,animationBegin:x,isUpdateAnimationActive:O}=t;if(o!==+o||p!==+p||y!==+y||v!==+v||g!==+g||0===y&&0===v||0===g)return null;var P=(0,l.$)("recharts-trapezoid",m);return O?n.createElement(s.i,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:g,x:o,y:p},to:{upperWidth:y,lowerWidth:v,height:g,x:o,y:p},duration:w,animationEasing:b,isActive:O},e=>{var{upperWidth:a,lowerWidth:o,height:l,x:c,y:d}=e;return n.createElement(s.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:w,easing:b},n.createElement("path",f({},(0,u.J9)(t,!0),{className:P,d:h(c,d,a,o,l),ref:r})))}):n.createElement("g",null,n.createElement("path",f({},(0,u.J9)(t,!0),{className:P,d:h(o,p,y,v,g)})))},y=r(4597),v=r(7670);let g=Math.cos,m=Math.sin,b=Math.sqrt,w=Math.PI,x=2*w,O={draw(e,t){let r=b(t/w);e.moveTo(r,0),e.arc(0,0,r,0,x)}},P=b(1/3),A=2*P,j=m(w/10)/m(7*w/10),E=m(x/10)*j,M=-g(x/10)*j,S=b(3),_=b(3)/2,k=1/b(12),T=(k/2+1)*3;var C=r(9344),D=r(2785);b(3),b(3);var N=r(4707),I=["type","size","sizeType"];function z(){return(z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var U={symbolCircle:O,symbolCross:{draw(e,t){let r=b(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=b(t/A),n=r*P;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=b(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=b(.8908130915292852*t),n=E*r,i=M*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=x*t/5,o=g(a),l=m(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-b(t/(3*S));e.moveTo(0,2*r),e.lineTo(-S*r,-r),e.lineTo(S*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=b(t/T),n=r/2,i=r*k,a=r*k+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-_*i,_*n+-.5*i),e.lineTo(-.5*n-_*a,_*n+-.5*a),e.lineTo(-.5*o-_*a,_*o+-.5*a),e.lineTo(-.5*n+_*i,-.5*i-_*n),e.lineTo(-.5*n+_*a,-.5*a-_*n),e.lineTo(-.5*o+_*a,-.5*a-_*o),e.closePath()}}},B=Math.PI/180,$=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,a=L(L({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,I)),{},{type:t,size:r,sizeType:i}),{className:o,cx:c,cy:s}=a,f=(0,u.J9)(a,!0);return c===+c&&s===+s&&r===+r?n.createElement("path",z({},f,{className:(0,l.$)("recharts-symbols",o),transform:"translate(".concat(c,", ").concat(s,")"),d:(()=>{var e=U["symbol".concat((0,N.Zb)(t))]||O;return(function(e,t){let r=null,n=(0,D.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,C.A)(e||O),t="function"==typeof t?t:(0,C.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,C.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,C.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*B;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(r,i,t))()})()})):null};$.registerSymbol=(e,t)=>{U["symbol".concat((0,N.Zb)(e))]=t};var F=["option","shapeType","propTransformer","activeClassName","isActive"];function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Z(e,t){return H(H({},t),e)}function G(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(o.M,r);case"trapezoid":return n.createElement(p,r);case"sector":return n.createElement(y.h,r);case"symbols":if("symbols"===t)return n.createElement($,r);break;default:return null}}function Y(e){var t,{option:r,shapeType:i,propTransformer:o=Z,activeClassName:l="recharts-active-shape",isActive:u}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,F);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,H(H({},c),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(c);else if(a()(r)&&"boolean"!=typeof r){var s=o(r,c);t=n.createElement(G,{shapeType:i,elementProps:s})}else t=n.createElement(G,{shapeType:i,elementProps:c});return u?n.createElement(v.W,{className:l},t):t}},8879:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9270);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},8895:(e,t,r)=>{"use strict";r.d(t,{Cj:()=>a,Pg:()=>o,Ub:()=>l});var n=r(193),i=r(6948),a=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.RD)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},o=e=>{var t=(0,n.j)();return(r,n)=>a=>{null==e||e(r,n,a),t((0,i.oP)())}},l=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.ML)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},8983:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{IZ:()=>o,Kg:()=>a,lY:()=>l,yy:()=>u}),r(2115);var a=Math.PI/180,o=(e,t,r,n)=>({x:e+Math.cos(-a*n)*r,y:t+Math.sin(-a*n)*r}),l=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},u=(e,t)=>{var r,{x:n,y:a}=e,{radius:o,angle:l}=((e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=((e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)})({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=Math.acos((r-i)/o);return n>a&&(l=2*Math.PI-l),{radius:o,angle:180*l/Math.PI,angleInRadian:l}})({x:n,y:a},t),{innerRadius:u,outerRadius:c}=t;if(o<u||o>c||0===o)return null;var{startAngle:s,endAngle:f}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}})(t),h=l;if(s<=f){for(;h>f;)h-=360;for(;h<s;)h+=360;r=h>=s&&h<=f}else{for(;h>s;)h-=360;for(;h<f;)h+=360;r=h>=f&&h<=s}return r?i(i({},t),{},{radius:o,angle:((e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))})(h,t)}):null}},9064:(e,t,r)=>{"use strict";r.d(t,{CU:()=>s,Lx:()=>u,u3:()=>c});var n=r(7681),i=r(1978),a=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:u,removeLegendPayload:c}=a.actions,s=a.reducer},9145:(e,t,r)=>{"use strict";r(9719)},9199:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7583),i=r(1102);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},9270:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},9344:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},9471:(e,t,r)=>{"use strict";r.d(t,{P:()=>c});var n=r(593);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},u="recharts_measurement_span",c=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(t=a({},r)).forEach(e=>{t[e]||delete t[e]}),t),c=JSON.stringify({text:e,copyStyle:i});if(o.widthCache[c])return o.widthCache[c];try{var s=document.getElementById(u);s||((s=document.createElement("span")).setAttribute("id",u),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},l),i);Object.assign(s.style,f),s.textContent="".concat(e);var h=s.getBoundingClientRect(),d={width:h.width,height:h.height};return o.widthCache[c]=d,++o.cacheCount>2e3&&(o.cacheCount=0,o.widthCache={}),d}catch(e){return{width:0,height:0}}}},9578:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},9719:(e,t,r)=>{"use strict";var n=r(2115);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},9819:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(4620).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},9945:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4227),i=r(4352),a=r(870),o=r(187);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},9969:(e,t,r)=>{"use strict";r.d(t,{h:()=>b});var n=r(2115),i=r(3834),a=r(6142),o=r(3472),l=r(193),u=r(2775),c=r(5540),s=r(1037),f=r(7949),h=["dangerouslySetInnerHTML","ticks"];function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e){var t=(0,l.j)();return(0,n.useEffect)(()=>(t((0,o.cU)(e)),()=>{t((0,o.fR)(e))}),[e,t]),null}var v=e=>{var t,{yAxisId:r,className:d,width:y,label:v}=e,g=(0,n.useRef)(null),m=(0,n.useRef)(null),b=(0,l.G)(c.c2),w=(0,s.r)(),x=(0,l.j)(),O="yAxis",P=(0,l.G)(e=>(0,u.iV)(e,O,r,w)),A=(0,l.G)(e=>(0,u.wP)(e,r)),j=(0,l.G)(e=>(0,u.KR)(e,r)),E=(0,l.G)(e=>(0,u.Zi)(e,O,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==y||!A||(0,f.Z)(v)||(0,n.isValidElement)(v))){var e,t=g.current,i=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:a,tickMargin:l}=t.props,u=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0})({ticks:i,label:m.current,labelGapWithTick:5,tickSize:a,tickMargin:l});Math.round(A.width)!==Math.round(u)&&x((0,o.QG)({id:r,width:u}))}},[g,null==g||null==(t=g.current)||null==(t=t.tickRefs)?void 0:t.current,null==A?void 0:A.width,A,x,v,r,y]),null==A||null==j)return null;var{dangerouslySetInnerHTML:M,ticks:S}=e,_=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,h);return n.createElement(a.u,p({},_,{ref:g,labelRef:m,scale:P,x:j.x,y:j.y,width:A.width,height:A.height,className:(0,i.$)("recharts-".concat(O," ").concat(O),d),viewBox:b,ticks:E}))},g=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(y,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),n.createElement(v,e))},m={allowDataOverflow:u.cd.allowDataOverflow,allowDecimals:u.cd.allowDecimals,allowDuplicatedCategory:u.cd.allowDuplicatedCategory,hide:!1,mirror:u.cd.mirror,orientation:u.cd.orientation,padding:u.cd.padding,reversed:u.cd.reversed,scale:u.cd.scale,tickCount:u.cd.tickCount,type:u.cd.type,width:u.cd.width,yAxisId:0};class b extends n.Component{render(){return n.createElement(g,this.props)}}d(b,"displayName","YAxis"),d(b,"defaultProps",m)}}]);