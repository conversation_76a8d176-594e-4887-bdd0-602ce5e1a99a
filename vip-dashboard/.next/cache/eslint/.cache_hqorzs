[{"/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/api/vip/route.ts": "1", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/layout.tsx": "2", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/page.tsx": "3", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/stats/page.tsx": "4", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/vip/[id]/page.tsx": "5", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/api/vip/route.ts": "6", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/layout.tsx": "7", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/page.tsx": "8", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/stats/page.tsx": "9", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/vip/[id]/page.tsx": "10", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/components/VIPStatsCard.tsx": "11", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/components/VIPUserCard.tsx": "12", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/lib/excel-parser.ts": "13", "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/types/vip.ts": "14"}, {"size": 3644, "mtime": 1753896479717, "results": "15", "hashOfConfig": "16"}, {"size": 689, "mtime": 1753894377341, "results": "17", "hashOfConfig": "16"}, {"size": 5321, "mtime": 1753896122718, "results": "18", "hashOfConfig": "16"}, {"size": 11409, "mtime": 1753896446767, "results": "19", "hashOfConfig": "16"}, {"size": 8507, "mtime": 1753896367756, "results": "20", "hashOfConfig": "16"}, {"size": 3644, "mtime": 1753896479717, "results": "21", "hashOfConfig": "16"}, {"size": 689, "mtime": 1753894377341, "results": "22", "hashOfConfig": "16"}, {"size": 5321, "mtime": 1753896122718, "results": "23", "hashOfConfig": "16"}, {"size": 11409, "mtime": 1753896446767, "results": "24", "hashOfConfig": "16"}, {"size": 8507, "mtime": 1753896367756, "results": "25", "hashOfConfig": "16"}, {"size": 2083, "mtime": 1753895239868, "results": "26", "hashOfConfig": "16"}, {"size": 3749, "mtime": 1753896095901, "results": "27", "hashOfConfig": "16"}, {"size": 5214, "mtime": 1753896502190, "results": "28", "hashOfConfig": "16"}, {"size": 907, "mtime": 1753895085936, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wuzwxs", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/api/vip/route.ts", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/layout.tsx", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/page.tsx", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/stats/page.tsx", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/vip/[id]/page.tsx", [], ["72"], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/api/vip/route.ts", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/layout.tsx", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/page.tsx", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/stats/page.tsx", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/vip/[id]/page.tsx", [], ["73"], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/components/VIPStatsCard.tsx", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/components/VIPUserCard.tsx", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/lib/excel-parser.ts", [], [], "/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/types/vip.ts", [], [], {"ruleId": "74", "severity": 1, "message": "75", "line": 17, "column": 6, "nodeType": "76", "endLine": 17, "endColumn": 17, "suggestions": "77", "suppressions": "78"}, {"ruleId": "74", "severity": 1, "message": "75", "line": 17, "column": 6, "nodeType": "76", "endLine": 17, "endColumn": 17, "suggestions": "79", "suppressions": "80"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchVIPUser'. Either include it or remove the dependency array.", "ArrayExpression", ["81"], ["82"], ["83"], ["84"], {"desc": "85", "fix": "86"}, {"kind": "87", "justification": "88"}, {"desc": "85", "fix": "89"}, {"kind": "87", "justification": "88"}, "Update the dependencies array to be: [fetchVIPUser, params.id]", {"range": "90", "text": "91"}, "directive", "", {"range": "92", "text": "91"}, [562, 573], "[fetchVIPUser, params.id]", [562, 573]]