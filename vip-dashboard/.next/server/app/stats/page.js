(()=>{var a={};a.id=266,a.ids=[266],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},525:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(54050),e=c(81569),f=c(26604),g=c(29694);b.isIterateeCall=function(a,b,c){return!!f.isObject(c)&&(!!("number"==typeof b&&e.isArrayLike(c)&&d.isIndex(b))&&b<c.length||"string"==typeof b&&b in c)&&g.eq(c[b],a)}},2654:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(68947),e=c(80737),f=c(23210);b.orderBy=function(a,b,c,g){if(null==a)return[];c=g?void 0:c,Array.isArray(a)||(a=Object.values(a)),Array.isArray(b)||(b=null==b?[null]:[b]),0===b.length&&(b=[null]),Array.isArray(c)||(c=null==c?[]:[c]),c=c.map(a=>String(a));let h=(a,b)=>{let c=a;for(let a=0;a<b.length&&null!=c;++a)c=c[b[a]];return c},i=b.map(a=>(Array.isArray(a)&&1===a.length&&(a=a[0]),null==a||"function"==typeof a||Array.isArray(a)||e.isKey(a))?a:{key:a,path:f.toPath(a)});return a.map(a=>({original:a,criteria:i.map(b=>{var c,d;return c=b,null==(d=a)||null==c?d:"object"==typeof c&&"key"in c?Object.hasOwn(d,c.key)?d[c.key]:h(d,c.path):"function"==typeof c?c(d):Array.isArray(c)?h(d,c):"object"==typeof d?d[c]:d})})).slice().sort((a,b)=>{for(let e=0;e<i.length;e++){let f=d.compareValues(a.criteria[e],b.criteria[e],c[e]);if(0!==f)return f}return 0}).map(a=>a.original)}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3458:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(6262),e=c(90679);b.matches=function(a){return a=e.cloneDeep(a),b=>d.isMatch(b,a)}},6262:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(89274);b.isMatch=function(a,b){return d.isMatchWith(a,b,()=>void 0)}},7544:(a,b,c)=>{"use strict";var d=c(43210),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},9549:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(525),e=c(92426);b.range=function(a,b,c){c&&"number"!=typeof c&&d.isIterateeCall(a,b,c)&&(b=c=void 0),a=e.toFinite(a),void 0===b?(b=a,a=0):b=e.toFinite(b),c=void 0===c?a<b?1:-1:e.toFinite(c);let f=Math.max(Math.ceil((b-a)/(c||1)),0),g=Array(f);for(let b=0;b<f;b++)g[b]=a,a+=c;return g}},9773:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.identity=function(a){return a}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11728:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if("object"!=typeof a||null==a)return!1;if(null===Object.getPrototypeOf(a))return!0;if("[object Object]"!==Object.prototype.toString.call(a)){let b=a[Symbol.toStringTag];return null!=b&&!!Object.getOwnPropertyDescriptor(a,Symbol.toStringTag)?.writable&&a.toString()===`[object ${b}]`}let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}},13976:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>rS});var d={};c.r(d),c.d(d,{scaleBand:()=>c8,scaleDiverging:()=>function a(){var b=en(gC()(d5));return b.copy=function(){return gz(b,a())},c2.apply(b,arguments)},scaleDivergingLog:()=>function a(){var b=ev(gC()).domain([.1,1,10]);return b.copy=function(){return gz(b,a()).base(b.base())},c2.apply(b,arguments)},scaleDivergingPow:()=>gD,scaleDivergingSqrt:()=>gE,scaleDivergingSymlog:()=>function a(){var b=ey(gC());return b.copy=function(){return gz(b,a()).constant(b.constant())},c2.apply(b,arguments)},scaleIdentity:()=>function a(b){var c;function d(a){return null==a||isNaN(a*=1)?c:a}return d.invert=d,d.domain=d.range=function(a){return arguments.length?(b=Array.from(a,d3),d):b.slice()},d.unknown=function(a){return arguments.length?(c=a,d):c},d.copy=function(){return a(b).unknown(c)},b=arguments.length?Array.from(b,d3):[0,1],en(d)},scaleImplicit:()=>c6,scaleLinear:()=>function a(){var b=eb();return b.copy=function(){return d9(b,a())},c1.apply(b,arguments),en(b)},scaleLog:()=>function a(){let b=ev(ea()).domain([1,10]);return b.copy=()=>d9(b,a()).base(b.base()),c1.apply(b,arguments),b},scaleOrdinal:()=>c7,scalePoint:()=>c9,scalePow:()=>eD,scaleQuantile:()=>function a(){var b,c=[],d=[],e=[];function f(){var a=0,b=Math.max(1,d.length);for(e=Array(b-1);++a<b;)e[a-1]=function(a,b,c=dl){if(!(!(d=a.length)||isNaN(b*=1))){if(b<=0||d<2)return+c(a[0],0,a);if(b>=1)return+c(a[d-1],d-1,a);var d,e=(d-1)*b,f=Math.floor(e),g=+c(a[f],f,a);return g+(c(a[f+1],f+1,a)-g)*(e-f)}}(c,a/b);return g}function g(a){return null==a||isNaN(a*=1)?b:d[dn(e,a)]}return g.invertExtent=function(a){var b=d.indexOf(a);return b<0?[NaN,NaN]:[b>0?e[b-1]:c[0],b<e.length?e[b]:c[c.length-1]]},g.domain=function(a){if(!arguments.length)return c.slice();for(let b of(c=[],a))null==b||isNaN(b*=1)||c.push(b);return c.sort(dh),f()},g.range=function(a){return arguments.length?(d=Array.from(a),f()):d.slice()},g.unknown=function(a){return arguments.length?(b=a,g):b},g.quantiles=function(){return e.slice()},g.copy=function(){return a().domain(c).range(d).unknown(b)},c1.apply(g,arguments)},scaleQuantize:()=>function a(){var b,c=0,d=1,e=1,f=[.5],g=[0,1];function h(a){return null!=a&&a<=a?g[dn(f,a,0,e)]:b}function i(){var a=-1;for(f=Array(e);++a<e;)f[a]=((a+1)*d-(a-e)*c)/(e+1);return h}return h.domain=function(a){return arguments.length?([c,d]=a,c*=1,d*=1,i()):[c,d]},h.range=function(a){return arguments.length?(e=(g=Array.from(a)).length-1,i()):g.slice()},h.invertExtent=function(a){var b=g.indexOf(a);return b<0?[NaN,NaN]:b<1?[c,f[0]]:b>=e?[f[e-1],d]:[f[b-1],f[b]]},h.unknown=function(a){return arguments.length&&(b=a),h},h.thresholds=function(){return f.slice()},h.copy=function(){return a().domain([c,d]).range(g).unknown(b)},c1.apply(en(h),arguments)},scaleRadial:()=>function a(){var b,c=eb(),d=[0,1],e=!1;function f(a){var d,f=Math.sign(d=c(a))*Math.sqrt(Math.abs(d));return isNaN(f)?b:e?Math.round(f):f}return f.invert=function(a){return c.invert(eF(a))},f.domain=function(a){return arguments.length?(c.domain(a),f):c.domain()},f.range=function(a){return arguments.length?(c.range((d=Array.from(a,d3)).map(eF)),f):d.slice()},f.rangeRound=function(a){return f.range(a).round(!0)},f.round=function(a){return arguments.length?(e=!!a,f):e},f.clamp=function(a){return arguments.length?(c.clamp(a),f):c.clamp()},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a(c.domain(),d).round(e).clamp(c.clamp()).unknown(b)},c1.apply(f,arguments),en(f)},scaleSequential:()=>function a(){var b=en(gy()(d5));return b.copy=function(){return gz(b,a())},c2.apply(b,arguments)},scaleSequentialLog:()=>function a(){var b=ev(gy()).domain([1,10]);return b.copy=function(){return gz(b,a()).base(b.base())},c2.apply(b,arguments)},scaleSequentialPow:()=>gA,scaleSequentialQuantile:()=>function a(){var b=[],c=d5;function d(a){if(null!=a&&!isNaN(a*=1))return c((dn(b,a,1)-1)/(b.length-1))}return d.domain=function(a){if(!arguments.length)return b.slice();for(let c of(b=[],a))null==c||isNaN(c*=1)||b.push(c);return b.sort(dh),d},d.interpolator=function(a){return arguments.length?(c=a,d):c},d.range=function(){return b.map((a,d)=>c(d/(b.length-1)))},d.quantiles=function(a){return Array.from({length:a+1},(c,d)=>(function(a,b,c){if(!(!(d=(a=Float64Array.from(function*(a,b){if(void 0===b)for(let b of a)null!=b&&(b*=1)>=b&&(yield b);else{let c=-1;for(let d of a)null!=(d=b(d,++c,a))&&(d*=1)>=d&&(yield d)}}(a,void 0))).length)||isNaN(b*=1))){if(b<=0||d<2)return eH(a);if(b>=1)return eG(a);var d,e=(d-1)*b,f=Math.floor(e),g=eG((function a(b,c,d=0,e=1/0,f){if(c=Math.floor(c),d=Math.floor(Math.max(0,d)),e=Math.floor(Math.min(b.length-1,e)),!(d<=c&&c<=e))return b;for(f=void 0===f?eI:function(a=dh){if(a===dh)return eI;if("function"!=typeof a)throw TypeError("compare is not a function");return(b,c)=>{let d=a(b,c);return d||0===d?d:(0===a(c,c))-(0===a(b,b))}}(f);e>d;){if(e-d>600){let g=e-d+1,h=c-d+1,i=Math.log(g),j=.5*Math.exp(2*i/3),k=.5*Math.sqrt(i*j*(g-j)/g)*(h-g/2<0?-1:1),l=Math.max(d,Math.floor(c-h*j/g+k)),m=Math.min(e,Math.floor(c+(g-h)*j/g+k));a(b,c,l,m,f)}let g=b[c],h=d,i=e;for(eJ(b,d,c),f(b[e],g)>0&&eJ(b,d,e);h<i;){for(eJ(b,h,i),++h,--i;0>f(b[h],g);)++h;for(;f(b[i],g)>0;)--i}0===f(b[d],g)?eJ(b,d,i):eJ(b,++i,e),i<=c&&(d=i+1),c<=i&&(e=i-1)}return b})(a,f).subarray(0,f+1));return g+(eH(a.subarray(f+1))-g)*(e-f)}})(b,d/a))},d.copy=function(){return a(c).domain(b)},c2.apply(d,arguments)},scaleSequentialSqrt:()=>gB,scaleSequentialSymlog:()=>function a(){var b=ey(gy());return b.copy=function(){return gz(b,a()).constant(b.constant())},c2.apply(b,arguments)},scaleSqrt:()=>eE,scaleSymlog:()=>function a(){var b=ey(ea());return b.copy=function(){return d9(b,a()).constant(b.constant())},c1.apply(b,arguments)},scaleThreshold:()=>function a(){var b,c=[.5],d=[0,1],e=1;function f(a){return null!=a&&a<=a?d[dn(c,a,0,e)]:b}return f.domain=function(a){return arguments.length?(e=Math.min((c=Array.from(a)).length,d.length-1),f):c.slice()},f.range=function(a){return arguments.length?(d=Array.from(a),e=Math.min(c.length,d.length-1),f):d.slice()},f.invertExtent=function(a){var b=d.indexOf(a);return[c[b-1],c[b]]},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a().domain(c).range(d).unknown(b)},c1.apply(f,arguments)},scaleTime:()=>gw,scaleUtc:()=>gx,tickFormat:()=>em});var e=c(60687),f=c(43210);function g(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}var h=c(64698),i=c.n(h),j=c(86980),k=c.n(j),l=a=>0===a?0:a>0?1:-1,m=a=>"number"==typeof a&&a!=+a,n=a=>"string"==typeof a&&a.indexOf("%")===a.length-1,o=a=>("number"==typeof a||a instanceof Number)&&!m(a),p=a=>o(a)||"string"==typeof a,q=0,r=a=>{var b=++q;return"".concat(a||"").concat(b)},s=function(a,b){var c,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,e=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!o(a)&&"string"!=typeof a)return d;if(n(a)){if(null==b)return d;var f=a.indexOf("%");c=b*parseFloat(a.slice(0,f))/100}else c=+a;return m(c)&&(c=d),e&&null!=b&&c>b&&(c=b),c},t=a=>{if(!Array.isArray(a))return!1;for(var b=a.length,c={},d=0;d<b;d++)if(c[a[d]])return!0;else c[a[d]]=!0;return!1},u=(a,b)=>o(a)&&o(b)?c=>a+c*(b-a):()=>b,v=a=>null==a?a:"".concat(a.charAt(0).toUpperCase()).concat(a.slice(1)),w=function(a,b){for(var c=arguments.length,d=Array(c>2?c-2:0),e=2;e<c;e++)d[e-2]=arguments[e]};function x(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function y(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?x(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):x(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var z=(0,f.forwardRef)((a,b)=>{var{aspect:c,initialDimension:d={width:-1,height:-1},width:e="100%",height:h="100%",minWidth:j=0,minHeight:k,maxHeight:l,children:m,debounce:o=0,id:p,className:q,onResize:r,style:s={}}=a,t=(0,f.useRef)(null),u=(0,f.useRef)();u.current=r,(0,f.useImperativeHandle)(b,()=>t.current);var[v,x]=(0,f.useState)({containerWidth:d.width,containerHeight:d.height}),z=(0,f.useCallback)((a,b)=>{x(c=>{var d=Math.round(a),e=Math.round(b);return c.containerWidth===d&&c.containerHeight===e?c:{containerWidth:d,containerHeight:e}})},[]);(0,f.useEffect)(()=>{var a=a=>{var b,{width:c,height:d}=a[0].contentRect;z(c,d),null==(b=u.current)||b.call(u,c,d)};o>0&&(a=i()(a,o,{trailing:!0,leading:!1}));var b=new ResizeObserver(a),{width:c,height:d}=t.current.getBoundingClientRect();return z(c,d),b.observe(t.current),()=>{b.disconnect()}},[z,o]);var A=(0,f.useMemo)(()=>{var{containerWidth:a,containerHeight:b}=v;if(a<0||b<0)return null;w(n(e)||n(h),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",e,h),w(!c||c>0,"The aspect(%s) must be greater than zero.",c);var d=n(e)?a:e,g=n(h)?b:h;return c&&c>0&&(d?g=d/c:g&&(d=g*c),l&&g>l&&(g=l)),w(d>0||g>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",d,g,e,h,j,k,c),f.Children.map(m,a=>(0,f.cloneElement)(a,{width:d,height:g,style:y({width:d,height:g},a.props.style)}))},[c,m,h,l,k,j,v,e]);return f.createElement("div",{id:p?"".concat(p):void 0,className:g("recharts-responsive-container",q),style:y(y({},s),{},{width:e,height:h,minWidth:j,minHeight:k,maxHeight:l}),ref:t},f.createElement("div",{style:{width:0,height:0,overflow:"visible"}},A))});function A(a){return`Minified Redux error #${a}; visit https://redux.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}var B="function"==typeof Symbol&&Symbol.observable||"@@observable",C=()=>Math.random().toString(36).substring(7).split("").join("."),D={INIT:`@@redux/INIT${C()}`,REPLACE:`@@redux/REPLACE${C()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${C()}`};function E(a){if("object"!=typeof a||null===a)return!1;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b||null===Object.getPrototypeOf(a)}function F(a){let b,c=Object.keys(a),d={};for(let b=0;b<c.length;b++){let e=c[b];"function"==typeof a[e]&&(d[e]=a[e])}let e=Object.keys(d);try{Object.keys(d).forEach(a=>{let b=d[a];if(void 0===b(void 0,{type:D.INIT}))throw Error(A(12));if(void 0===b(void 0,{type:D.PROBE_UNKNOWN_ACTION()}))throw Error(A(13))})}catch(a){b=a}return function(a={},c){if(b)throw b;let f=!1,g={};for(let b=0;b<e.length;b++){let h=e[b],i=d[h],j=a[h],k=i(j,c);if(void 0===k)throw c&&c.type,Error(A(14));g[h]=k,f=f||k!==j}return(f=f||e.length!==Object.keys(a).length)?g:a}}function G(...a){return 0===a.length?a=>a:1===a.length?a[0]:a.reduce((a,b)=>(...c)=>a(b(...c)))}function H(a){return E(a)&&"type"in a&&"string"==typeof a.type}function I(a){return({dispatch:b,getState:c})=>d=>e=>"function"==typeof e?e(b,c,a):d(e)}var J=I(),K=Symbol.for("immer-nothing"),L=Symbol.for("immer-draftable"),M=Symbol.for("immer-state");function N(a){throw Error(`[Immer] minified error nr: ${a}. Full error at: https://bit.ly/3cXEKWf`)}var O=Object.getPrototypeOf;function P(a){return!!a&&!!a[M]}function Q(a){return!!a&&(S(a)||Array.isArray(a)||!!a[L]||!!a.constructor?.[L]||X(a)||Y(a))}var R=Object.prototype.constructor.toString();function S(a){if(!a||"object"!=typeof a)return!1;let b=O(a);if(null===b)return!0;let c=Object.hasOwnProperty.call(b,"constructor")&&b.constructor;return c===Object||"function"==typeof c&&Function.toString.call(c)===R}function T(a,b){0===U(a)?Reflect.ownKeys(a).forEach(c=>{b(c,a[c],a)}):a.forEach((c,d)=>b(d,c,a))}function U(a){let b=a[M];return b?b.type_:Array.isArray(a)?1:X(a)?2:3*!!Y(a)}function V(a,b){return 2===U(a)?a.has(b):Object.prototype.hasOwnProperty.call(a,b)}function W(a,b,c){let d=U(a);2===d?a.set(b,c):3===d?a.add(c):a[b]=c}function X(a){return a instanceof Map}function Y(a){return a instanceof Set}function Z(a){return a.copy_||a.base_}function $(a,b){if(X(a))return new Map(a);if(Y(a))return new Set(a);if(Array.isArray(a))return Array.prototype.slice.call(a);let c=S(a);if(!0!==b&&("class_only"!==b||c)){let b=O(a);return null!==b&&c?{...a}:Object.assign(Object.create(b),a)}{let b=Object.getOwnPropertyDescriptors(a);delete b[M];let c=Reflect.ownKeys(b);for(let d=0;d<c.length;d++){let e=c[d],f=b[e];!1===f.writable&&(f.writable=!0,f.configurable=!0),(f.get||f.set)&&(b[e]={configurable:!0,writable:!0,enumerable:f.enumerable,value:a[e]})}return Object.create(O(a),b)}}function _(a,b=!1){return ab(a)||P(a)||!Q(a)||(U(a)>1&&(a.set=a.add=a.clear=a.delete=aa),Object.freeze(a),b&&Object.entries(a).forEach(([a,b])=>_(b,!0))),a}function aa(){N(2)}function ab(a){return Object.isFrozen(a)}var ac={};function ad(a){let b=ac[a];return b||N(0,a),b}function ae(a,b){b&&(ad("Patches"),a.patches_=[],a.inversePatches_=[],a.patchListener_=b)}function af(a){ag(a),a.drafts_.forEach(ai),a.drafts_=null}function ag(a){a===gM&&(gM=a.parent_)}function ah(a){return gM={drafts_:[],parent_:gM,immer_:a,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function ai(a){let b=a[M];0===b.type_||1===b.type_?b.revoke_():b.revoked_=!0}function aj(a,b){b.unfinalizedDrafts_=b.drafts_.length;let c=b.drafts_[0];return void 0!==a&&a!==c?(c[M].modified_&&(af(b),N(4)),Q(a)&&(a=ak(b,a),b.parent_||am(b,a)),b.patches_&&ad("Patches").generateReplacementPatches_(c[M].base_,a,b.patches_,b.inversePatches_)):a=ak(b,c,[]),af(b),b.patches_&&b.patchListener_(b.patches_,b.inversePatches_),a!==K?a:void 0}function ak(a,b,c){if(ab(b))return b;let d=b[M];if(!d)return T(b,(e,f)=>al(a,d,b,e,f,c)),b;if(d.scope_!==a)return b;if(!d.modified_)return am(a,d.base_,!0),d.base_;if(!d.finalized_){d.finalized_=!0,d.scope_.unfinalizedDrafts_--;let b=d.copy_,e=b,f=!1;3===d.type_&&(e=new Set(b),b.clear(),f=!0),T(e,(e,g)=>al(a,d,b,e,g,c,f)),am(a,b,!1),c&&a.patches_&&ad("Patches").generatePatches_(d,c,a.patches_,a.inversePatches_)}return d.copy_}function al(a,b,c,d,e,f,g){if(P(e)){let g=ak(a,e,f&&b&&3!==b.type_&&!V(b.assigned_,d)?f.concat(d):void 0);if(W(c,d,g),!P(g))return;a.canAutoFreeze_=!1}else g&&c.add(e);if(Q(e)&&!ab(e)){if(!a.immer_.autoFreeze_&&a.unfinalizedDrafts_<1)return;ak(a,e),(!b||!b.scope_.parent_)&&"symbol"!=typeof d&&Object.prototype.propertyIsEnumerable.call(c,d)&&am(a,e)}}function am(a,b,c=!1){!a.parent_&&a.immer_.autoFreeze_&&a.canAutoFreeze_&&_(b,c)}var an={get(a,b){if(b===M)return a;let c=Z(a);if(!V(c,b)){var d=a,e=c,f=b;let g=aq(e,f);return g?"value"in g?g.value:g.get?.call(d.draft_):void 0}let g=c[b];return a.finalized_||!Q(g)?g:g===ap(a.base_,b)?(as(a),a.copy_[b]=at(g,a)):g},has:(a,b)=>b in Z(a),ownKeys:a=>Reflect.ownKeys(Z(a)),set(a,b,c){let d=aq(Z(a),b);if(d?.set)return d.set.call(a.draft_,c),!0;if(!a.modified_){let d=ap(Z(a),b),e=d?.[M];if(e&&e.base_===c)return a.copy_[b]=c,a.assigned_[b]=!1,!0;if((c===d?0!==c||1/c==1/d:c!=c&&d!=d)&&(void 0!==c||V(a.base_,b)))return!0;as(a),ar(a)}return!!(a.copy_[b]===c&&(void 0!==c||b in a.copy_)||Number.isNaN(c)&&Number.isNaN(a.copy_[b]))||(a.copy_[b]=c,a.assigned_[b]=!0,!0)},deleteProperty:(a,b)=>(void 0!==ap(a.base_,b)||b in a.base_?(a.assigned_[b]=!1,as(a),ar(a)):delete a.assigned_[b],a.copy_&&delete a.copy_[b],!0),getOwnPropertyDescriptor(a,b){let c=Z(a),d=Reflect.getOwnPropertyDescriptor(c,b);return d?{writable:!0,configurable:1!==a.type_||"length"!==b,enumerable:d.enumerable,value:c[b]}:d},defineProperty(){N(11)},getPrototypeOf:a=>O(a.base_),setPrototypeOf(){N(12)}},ao={};function ap(a,b){let c=a[M];return(c?Z(c):a)[b]}function aq(a,b){if(!(b in a))return;let c=O(a);for(;c;){let a=Object.getOwnPropertyDescriptor(c,b);if(a)return a;c=O(c)}}function ar(a){!a.modified_&&(a.modified_=!0,a.parent_&&ar(a.parent_))}function as(a){a.copy_||(a.copy_=$(a.base_,a.scope_.immer_.useStrictShallowCopy_))}function at(a,b){let c=X(a)?ad("MapSet").proxyMap_(a,b):Y(a)?ad("MapSet").proxySet_(a,b):function(a,b){let c=Array.isArray(a),d={type_:+!!c,scope_:b?b.scope_:gM,modified_:!1,finalized_:!1,assigned_:{},parent_:b,base_:a,draft_:null,copy_:null,revoke_:null,isManual_:!1},e=d,f=an;c&&(e=[d],f=ao);let{revoke:g,proxy:h}=Proxy.revocable(e,f);return d.draft_=h,d.revoke_=g,h}(a,b);return(b?b.scope_:gM).drafts_.push(c),c}function au(a){return P(a)||N(10,a),function a(b){let c;if(!Q(b)||ab(b))return b;let d=b[M];if(d){if(!d.modified_)return d.base_;d.finalized_=!0,c=$(b,d.scope_.immer_.useStrictShallowCopy_)}else c=$(b,!0);return T(c,(b,d)=>{W(c,b,a(d))}),d&&(d.finalized_=!1),c}(a)}T(an,(a,b)=>{ao[a]=function(){return arguments[0]=arguments[0][0],b.apply(this,arguments)}}),ao.deleteProperty=function(a,b){return ao.set.call(this,a,b,void 0)},ao.set=function(a,b,c){return an.set.call(this,a[0],b,c,a[0])};var av=new class{constructor(a){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(a,b,c)=>{let d;if("function"==typeof a&&"function"!=typeof b){let c=b;b=a;let d=this;return function(a=c,...e){return d.produce(a,a=>b.call(this,a,...e))}}if("function"!=typeof b&&N(6),void 0!==c&&"function"!=typeof c&&N(7),Q(a)){let e=ah(this),f=at(a,void 0),g=!0;try{d=b(f),g=!1}finally{g?af(e):ag(e)}return ae(e,c),aj(d,e)}if(a&&"object"==typeof a)N(1,a);else{if(void 0===(d=b(a))&&(d=a),d===K&&(d=void 0),this.autoFreeze_&&_(d,!0),c){let b=[],e=[];ad("Patches").generateReplacementPatches_(a,d,b,e),c(b,e)}return d}},this.produceWithPatches=(a,b)=>{let c,d;return"function"==typeof a?(b,...c)=>this.produceWithPatches(b,b=>a(b,...c)):[this.produce(a,b,(a,b)=>{c=a,d=b}),c,d]},"boolean"==typeof a?.autoFreeze&&this.setAutoFreeze(a.autoFreeze),"boolean"==typeof a?.useStrictShallowCopy&&this.setUseStrictShallowCopy(a.useStrictShallowCopy)}createDraft(a){Q(a)||N(8),P(a)&&(a=au(a));let b=ah(this),c=at(a,void 0);return c[M].isManual_=!0,ag(b),c}finishDraft(a,b){let c=a&&a[M];c&&c.isManual_||N(9);let{scope_:d}=c;return ae(d,b),aj(void 0,d)}setAutoFreeze(a){this.autoFreeze_=a}setUseStrictShallowCopy(a){this.useStrictShallowCopy_=a}applyPatches(a,b){let c;for(c=b.length-1;c>=0;c--){let d=b[c];if(0===d.path.length&&"replace"===d.op){a=d.value;break}}c>-1&&(b=b.slice(c+1));let d=ad("Patches").applyPatches_;return P(a)?d(a,b):this.produce(a,a=>d(a,b))}},aw=av.produce;av.produceWithPatches.bind(av),av.setAutoFreeze.bind(av),av.setUseStrictShallowCopy.bind(av),av.applyPatches.bind(av),av.createDraft.bind(av),av.finishDraft.bind(av);var ax="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?G:G.apply(null,arguments)};function ay(a,b){function c(...d){if(b){let c=b(...d);if(!c)throw Error(bb(0));return{type:a,payload:c.payload,..."meta"in c&&{meta:c.meta},..."error"in c&&{error:c.error}}}return{type:a,payload:d[0]}}return c.toString=()=>`${a}`,c.type=a,c.match=b=>H(b)&&b.type===a,c}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var az=class a extends Array{constructor(...b){super(...b),Object.setPrototypeOf(this,a.prototype)}static get[Symbol.species](){return a}concat(...a){return super.concat.apply(this,a)}prepend(...b){return 1===b.length&&Array.isArray(b[0])?new a(...b[0].concat(this)):new a(...b.concat(this))}};function aA(a){return Q(a)?aw(a,()=>{}):a}function aB(a,b,c){return a.has(b)?a.get(b):a.set(b,c(b)).get(b)}var aC=a=>b=>{setTimeout(b,a)};function aD(a){let b,c={},d=[],e={addCase(a,b){let d="string"==typeof a?a:a.type;if(!d)throw Error(bb(28));if(d in c)throw Error(bb(29));return c[d]=b,e},addMatcher:(a,b)=>(d.push({matcher:a,reducer:b}),e),addDefaultCase:a=>(b=a,e)};return a(e),[c,d,b]}var aE=Symbol.for("rtk-slice-createasyncthunk"),aF=(a=>(a.reducer="reducer",a.reducerWithPrepare="reducerWithPrepare",a.asyncThunk="asyncThunk",a))(aF||{}),aG=function({creators:a}={}){let b=a?.asyncThunk?.[aE];return function(a){let c,{name:d,reducerPath:e=d}=a;if(!d)throw Error(bb(11));let f=("function"==typeof a.reducers?a.reducers(function(){function a(a,b){return{_reducerDefinitionType:"asyncThunk",payloadCreator:a,...b}}return a.withTypes=()=>a,{reducer:a=>Object.assign({[a.name]:(...b)=>a(...b)}[a.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(a,b)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:a,reducer:b}),asyncThunk:a}}()):a.reducers)||{},g=Object.keys(f),h={},i={},j={},k=[],l={addCase(a,b){let c="string"==typeof a?a:a.type;if(!c)throw Error(bb(12));if(c in i)throw Error(bb(13));return i[c]=b,l},addMatcher:(a,b)=>(k.push({matcher:a,reducer:b}),l),exposeAction:(a,b)=>(j[a]=b,l),exposeCaseReducer:(a,b)=>(h[a]=b,l)};function m(){let[b={},c=[],d]="function"==typeof a.extraReducers?aD(a.extraReducers):[a.extraReducers],e={...b,...i};return function(a,b){let c,[d,e,f]=aD(b);if("function"==typeof a)c=()=>aA(a());else{let b=aA(a);c=()=>b}function g(a=c(),b){let h=[d[b.type],...e.filter(({matcher:a})=>a(b)).map(({reducer:a})=>a)];return 0===h.filter(a=>!!a).length&&(h=[f]),h.reduce((a,c)=>{if(c)if(P(a)){let d=c(a,b);return void 0===d?a:d}else{if(Q(a))return aw(a,a=>c(a,b));let d=c(a,b);if(void 0===d){if(null===a)return a;throw Error("A case reducer on a non-draftable value must not return undefined")}return d}return a},a)}return g.getInitialState=c,g}(a.initialState,a=>{for(let b in e)a.addCase(b,e[b]);for(let b of k)a.addMatcher(b.matcher,b.reducer);for(let b of c)a.addMatcher(b.matcher,b.reducer);d&&a.addDefaultCase(d)})}g.forEach(c=>{let e=f[c],g={reducerName:c,type:`${d}/${c}`,createNotation:"function"==typeof a.reducers};"asyncThunk"===e._reducerDefinitionType?function({type:a,reducerName:b},c,d,e){if(!e)throw Error(bb(18));let{payloadCreator:f,fulfilled:g,pending:h,rejected:i,settled:j,options:k}=c,l=e(a,f,k);d.exposeAction(b,l),g&&d.addCase(l.fulfilled,g),h&&d.addCase(l.pending,h),i&&d.addCase(l.rejected,i),j&&d.addMatcher(l.settled,j),d.exposeCaseReducer(b,{fulfilled:g||aH,pending:h||aH,rejected:i||aH,settled:j||aH})}(g,e,l,b):function({type:a,reducerName:b,createNotation:c},d,e){let f,g;if("reducer"in d){if(c&&"reducerWithPrepare"!==d._reducerDefinitionType)throw Error(bb(17));f=d.reducer,g=d.prepare}else f=d;e.addCase(a,f).exposeCaseReducer(b,f).exposeAction(b,g?ay(a,g):ay(a))}(g,e,l)});let n=a=>a,o=new Map,p=new WeakMap;function q(a,b){return c||(c=m()),c(a,b)}function r(){return c||(c=m()),c.getInitialState()}function s(b,c=!1){function d(a){let e=a[b];return void 0===e&&c&&(e=aB(p,d,r)),e}function e(b=n){let d=aB(o,c,()=>new WeakMap);return aB(d,b,()=>{let d={};for(let[e,f]of Object.entries(a.selectors??{}))d[e]=function(a,b,c,d){function e(f,...g){let h=b(f);return void 0===h&&d&&(h=c()),a(h,...g)}return e.unwrapped=a,e}(f,b,()=>aB(p,b,r),c);return d})}return{reducerPath:b,getSelectors:e,get selectors(){return e(d)},selectSlice:d}}let t={name:d,reducer:q,actions:j,caseReducers:h,getInitialState:r,...s(e),injectInto(a,{reducerPath:b,...c}={}){let d=b??e;return a.inject({reducerPath:d,reducer:q},c),{...t,...s(d,!0)}}};return t}}();function aH(){}var aI="listener",aJ="completed",aK="cancelled",aL=`task-${aK}`,aM=`task-${aJ}`,aN=`${aI}-${aK}`,aO=`${aI}-${aJ}`,aP=class{constructor(a){this.code=a,this.message=`task ${aK} (reason: ${a})`}name="TaskAbortError";message},aQ=(a,b)=>{if("function"!=typeof a)throw TypeError(bb(32))},aR=()=>{},aS=(a,b=aR)=>(a.catch(b),a),aT=(a,b)=>(a.addEventListener("abort",b,{once:!0}),()=>a.removeEventListener("abort",b)),aU=(a,b)=>{let c=a.signal;c.aborted||("reason"in c||Object.defineProperty(c,"reason",{enumerable:!0,value:b,configurable:!0,writable:!0}),a.abort(b))},aV=a=>{if(a.aborted){let{reason:b}=a;throw new aP(b)}};function aW(a,b){let c=aR;return new Promise((d,e)=>{let f=()=>e(new aP(a.reason));if(a.aborted)return void f();c=aT(a,f),b.finally(()=>c()).then(d,e)}).finally(()=>{c=aR})}var aX=async(a,b)=>{try{await Promise.resolve();let b=await a();return{status:"ok",value:b}}catch(a){return{status:a instanceof aP?"cancelled":"rejected",error:a}}finally{b?.()}},aY=a=>b=>aS(aW(a,b).then(b=>(aV(a),b))),aZ=a=>{let b=aY(a);return a=>b(new Promise(b=>setTimeout(b,a)))},{assign:a$}=Object,a_={},a0="listenerMiddleware",a1=a=>{let{type:b,actionCreator:c,matcher:d,predicate:e,effect:f}=a;if(b)e=ay(b).match;else if(c)b=c.type,e=c.match;else if(d)e=d;else if(e);else throw Error(bb(21));return aQ(f,"options.listener"),{predicate:e,type:b,effect:f}},a2=a$(a=>{let{type:b,predicate:c,effect:d}=a1(a);return{id:((a=21)=>{let b="",c=a;for(;c--;)b+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return b})(),effect:d,type:b,predicate:c,pending:new Set,unsubscribe:()=>{throw Error(bb(22))}}},{withTypes:()=>a2}),a3=(a,b)=>{let{type:c,effect:d,predicate:e}=a1(b);return Array.from(a.values()).find(a=>("string"==typeof c?a.type===c:a.predicate===e)&&a.effect===d)},a4=a=>{a.pending.forEach(a=>{aU(a,aN)})},a5=(a,b,c)=>{try{a(b,c)}catch(a){setTimeout(()=>{throw a},0)}},a6=a$(ay(`${a0}/add`),{withTypes:()=>a6}),a7=ay(`${a0}/removeAll`),a8=a$(ay(`${a0}/remove`),{withTypes:()=>a8}),a9=(...a)=>{console.error(`${a0}/error`,...a)},ba=(a={})=>{let b=new Map,{extra:c,onError:d=a9}=a;aQ(d,"onError");let e=a=>(a=>(a.unsubscribe=()=>b.delete(a.id),b.set(a.id,a),b=>{a.unsubscribe(),b?.cancelActive&&a4(a)}))(a3(b,a)??a2(a));a$(e,{withTypes:()=>e});let f=a=>{let c=a3(b,a);return c&&(c.unsubscribe(),a.cancelActive&&a4(c)),!!c};a$(f,{withTypes:()=>f});let g=async(a,f,g,h)=>{let i=new AbortController,j=((a,b)=>{let c=async(c,d)=>{aV(b);let e=()=>{},f=[new Promise((b,d)=>{let f=a({predicate:c,effect:(a,c)=>{c.unsubscribe(),b([a,c.getState(),c.getOriginalState()])}});e=()=>{f(),d()}})];null!=d&&f.push(new Promise(a=>setTimeout(a,d,null)));try{let a=await aW(b,Promise.race(f));return aV(b),a}finally{e()}};return(a,b)=>aS(c(a,b))})(e,i.signal),k=[];try{a.pending.add(i),await Promise.resolve(a.effect(f,a$({},g,{getOriginalState:h,condition:(a,b)=>j(a,b).then(Boolean),take:j,delay:aZ(i.signal),pause:aY(i.signal),extra:c,signal:i.signal,fork:((a,b)=>(c,d)=>{aQ(c,"taskExecutor");let e=new AbortController;aT(a,()=>aU(e,a.reason));let f=aX(async()=>{aV(a),aV(e.signal);let b=await c({pause:aY(e.signal),delay:aZ(e.signal),signal:e.signal});return aV(e.signal),b},()=>aU(e,aM));return d?.autoJoin&&b.push(f.catch(aR)),{result:aY(a)(f),cancel(){aU(e,aL)}}})(i.signal,k),unsubscribe:a.unsubscribe,subscribe:()=>{b.set(a.id,a)},cancelActiveListeners:()=>{a.pending.forEach((a,b,c)=>{a!==i&&(aU(a,aN),c.delete(a))})},cancel:()=>{aU(i,aN),a.pending.delete(i)},throwIfCancelled:()=>{aV(i.signal)}})))}catch(a){a instanceof aP||a5(d,a,{raisedBy:"effect"})}finally{await Promise.all(k),aU(i,aO),a.pending.delete(i)}},h=(a=>()=>{a.forEach(a4),a.clear()})(b);return{middleware:a=>c=>i=>{let j;if(!H(i))return c(i);if(a6.match(i))return e(i.payload);if(a7.match(i))return void h();if(a8.match(i))return f(i.payload);let k=a.getState(),l=()=>{if(k===a_)throw Error(bb(23));return k};try{if(j=c(i),b.size>0){let c=a.getState();for(let e of Array.from(b.values())){let b=!1;try{b=e.predicate(i,c,k)}catch(a){b=!1,a5(d,a,{raisedBy:"predicate"})}b&&g(e,i,a,l)}}}finally{k=a_}return j},startListening:e,stopListening:f,clearListeners:h}};function bb(a){return`Minified Redux Toolkit error #${a}; visit https://redux-toolkit.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}function bc(a,b){if(b){var c=Number.parseInt(b,10);if(!m(c))return null==a?void 0:a[c]}}Symbol.for("rtk-state-proxy-original");var bd=aG({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:a=>{null==a.eventEmitter&&(a.eventEmitter=Symbol("rechartsEventEmitter"))}}}),be=bd.reducer,{createEventEmitter:bf}=bd.actions;c(24595);var bg={notify(){},get:()=>[]},bh="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,bi="undefined"!=typeof navigator&&"ReactNative"===navigator.product,bj=bh||bi?f.useLayoutEffect:f.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var bk=Symbol.for("react-redux-context"),bl="undefined"!=typeof globalThis?globalThis:{},bm=function(){if(!f.createContext)return{};let a=bl[bk]??=new Map,b=a.get(f.createContext);return b||(b=f.createContext(null),a.set(f.createContext,b)),b}(),bn=function(a){let{children:b,context:c,serverState:d,store:e}=a,g=f.useMemo(()=>{let a=function(a,b){let c,d=bg,e=0,f=!1;function g(){j.onStateChange&&j.onStateChange()}function h(){if(e++,!c){let b,e;c=a.subscribe(g),b=null,e=null,d={clear(){b=null,e=null},notify(){let a=b;for(;a;)a.callback(),a=a.next},get(){let a=[],c=b;for(;c;)a.push(c),c=c.next;return a},subscribe(a){let c=!0,d=e={callback:a,next:null,prev:e};return d.prev?d.prev.next=d:b=d,function(){c&&null!==b&&(c=!1,d.next?d.next.prev=d.prev:e=d.prev,d.prev?d.prev.next=d.next:b=d.next)}}}}}function i(){e--,c&&0===e&&(c(),c=void 0,d.clear(),d=bg)}let j={addNestedSub:function(a){h();let b=d.subscribe(a),c=!1;return()=>{c||(c=!0,b(),i())}},notifyNestedSubs:function(){d.notify()},handleChangeWrapper:g,isSubscribed:function(){return f},trySubscribe:function(){f||(f=!0,h())},tryUnsubscribe:function(){f&&(f=!1,i())},getListeners:()=>d};return j}(e);return{store:e,subscription:a,getServerState:d?()=>d:void 0}},[e,d]),h=f.useMemo(()=>e.getState(),[e]);return bj(()=>{let{subscription:a}=g;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),h!==e.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[g,h]),f.createElement((c||bm).Provider,{value:g},b)},bo={active:!1,index:null,dataKey:void 0,coordinate:void 0},bp=aG({name:"tooltip",initialState:{itemInteraction:{click:bo,hover:bo},axisInteraction:{click:bo,hover:bo},keyboardInteraction:bo,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(a,b){a.tooltipItemPayloads.push(b.payload)},removeTooltipEntrySettings(a,b){var c=au(a).tooltipItemPayloads.indexOf(b.payload);c>-1&&a.tooltipItemPayloads.splice(c,1)},setTooltipSettingsState(a,b){a.settings=b.payload},setActiveMouseOverItemIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.itemInteraction.hover.active=!0,a.itemInteraction.hover.index=b.payload.activeIndex,a.itemInteraction.hover.dataKey=b.payload.activeDataKey,a.itemInteraction.hover.coordinate=b.payload.activeCoordinate},mouseLeaveChart(a){a.itemInteraction.hover.active=!1,a.axisInteraction.hover.active=!1},mouseLeaveItem(a){a.itemInteraction.hover.active=!1},setActiveClickItemIndex(a,b){a.syncInteraction.active=!1,a.itemInteraction.click.active=!0,a.keyboardInteraction.active=!1,a.itemInteraction.click.index=b.payload.activeIndex,a.itemInteraction.click.dataKey=b.payload.activeDataKey,a.itemInteraction.click.coordinate=b.payload.activeCoordinate},setMouseOverAxisIndex(a,b){a.syncInteraction.active=!1,a.axisInteraction.hover.active=!0,a.keyboardInteraction.active=!1,a.axisInteraction.hover.index=b.payload.activeIndex,a.axisInteraction.hover.dataKey=b.payload.activeDataKey,a.axisInteraction.hover.coordinate=b.payload.activeCoordinate},setMouseClickAxisIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.axisInteraction.click.active=!0,a.axisInteraction.click.index=b.payload.activeIndex,a.axisInteraction.click.dataKey=b.payload.activeDataKey,a.axisInteraction.click.coordinate=b.payload.activeCoordinate},setSyncInteraction(a,b){a.syncInteraction=b.payload},setKeyboardInteraction(a,b){a.keyboardInteraction.active=b.payload.active,a.keyboardInteraction.index=b.payload.activeIndex,a.keyboardInteraction.coordinate=b.payload.activeCoordinate,a.keyboardInteraction.dataKey=b.payload.activeDataKey}}}),{addTooltipEntrySettings:bq,removeTooltipEntrySettings:br,setTooltipSettingsState:bs,setActiveMouseOverItemIndex:bt,mouseLeaveItem:bu,mouseLeaveChart:bv,setActiveClickItemIndex:bw,setMouseOverAxisIndex:bx,setMouseClickAxisIndex:by,setSyncInteraction:bz,setKeyboardInteraction:bA}=bp.actions,bB=bp.reducer,bC=aG({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(a,b){if(a.chartData=b.payload,null==b.payload){a.dataStartIndex=0,a.dataEndIndex=0;return}b.payload.length>0&&a.dataEndIndex!==b.payload.length-1&&(a.dataEndIndex=b.payload.length-1)},setComputedData(a,b){a.computedData=b.payload},setDataStartEndIndexes(a,b){var{startIndex:c,endIndex:d}=b.payload;null!=c&&(a.dataStartIndex=c),null!=d&&(a.dataEndIndex=d)}}}),{setChartData:bD,setDataStartEndIndexes:bE,setComputedData:bF}=bC.actions,bG=bC.reducer,bH=aG({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(a,b){a.layoutType=b.payload},setChartSize(a,b){a.width=b.payload.width,a.height=b.payload.height},setMargin(a,b){a.margin.top=b.payload.top,a.margin.right=b.payload.right,a.margin.bottom=b.payload.bottom,a.margin.left=b.payload.left},setScale(a,b){a.scale=b.payload}}}),{setMargin:bI,setLayout:bJ,setChartSize:bK,setScale:bL}=bH.actions,bM=bH.reducer,bN=a=>Array.isArray(a)?a:[a],bO=0,bP=class{revision=bO;_value;_lastValue;_isEqual=bQ;constructor(a,b=bQ){this._value=this._lastValue=a,this._isEqual=b}get value(){return this._value}set value(a){this.value!==a&&(this._value=a,this.revision=++bO)}};function bQ(a,b){return a===b}function bR(a){return a instanceof bP||console.warn("Not a valid cell! ",a),a.value}var bS=(a,b)=>!1;function bT(){return function(a,b=bQ){return new bP(null,b)}(0,bS)}var bU=a=>{let b=a.collectionTag;null===b&&(b=a.collectionTag=bT()),bR(b)};Symbol();var bV=0,bW=Object.getPrototypeOf({}),bX=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy(this,bY);tag=bT();tags={};children={};collectionTag=null;id=bV++},bY={get:(a,b)=>(function(){let{value:c}=a,d=Reflect.get(c,b);if("symbol"==typeof b||b in bW)return d;if("object"==typeof d&&null!==d){var e;let c=a.children[b];return void 0===c&&(c=a.children[b]=Array.isArray(e=d)?new bZ(e):new bX(e)),c.tag&&bR(c.tag),c.proxy}{let c=a.tags[b];return void 0===c&&((c=a.tags[b]=bT()).value=d),bR(c),d}})(),ownKeys:a=>(bU(a),Reflect.ownKeys(a.value)),getOwnPropertyDescriptor:(a,b)=>Reflect.getOwnPropertyDescriptor(a.value,b),has:(a,b)=>Reflect.has(a.value,b)},bZ=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy([this],b$);tag=bT();tags={};children={};collectionTag=null;id=bV++},b$={get:([a],b)=>("length"===b&&bU(a),bY.get(a,b)),ownKeys:([a])=>bY.ownKeys(a),getOwnPropertyDescriptor:([a],b)=>bY.getOwnPropertyDescriptor(a,b),has:([a],b)=>bY.has(a,b)},b_="undefined"!=typeof WeakRef?WeakRef:class{constructor(a){this.value=a}deref(){return this.value}};function b0(){return{s:0,v:void 0,o:null,p:null}}function b1(a,b={}){let c,d=b0(),{resultEqualityCheck:e}=b,f=0;function g(){let b,g=d,{length:h}=arguments;for(let a=0;a<h;a++){let b=arguments[a];if("function"==typeof b||"object"==typeof b&&null!==b){let a=g.o;null===a&&(g.o=a=new WeakMap);let c=a.get(b);void 0===c?(g=b0(),a.set(b,g)):g=c}else{let a=g.p;null===a&&(g.p=a=new Map);let c=a.get(b);void 0===c?(g=b0(),a.set(b,g)):g=c}}let i=g;if(1===g.s)b=g.v;else if(b=a.apply(null,arguments),f++,e){let a=c?.deref?.()??c;null!=a&&e(a,b)&&(b=a,0!==f&&f--),c="object"==typeof b&&null!==b||"function"==typeof b?new b_(b):b}return i.s=1,i.v=b,b}return g.clearCache=()=>{d=b0(),g.resetResultsCount()},g.resultsCount=()=>f,g.resetResultsCount=()=>{f=0},g}var b2=function(a,...b){let c="function"==typeof a?{memoize:a,memoizeOptions:b}:a,d=(...a)=>{let b,d=0,e=0,f={},g=a.pop();"object"==typeof g&&(f=g,g=a.pop()),function(a,b=`expected a function, instead received ${typeof a}`){if("function"!=typeof a)throw TypeError(b)}(g,`createSelector expects an output function after the inputs, but received: [${typeof g}]`);let{memoize:h,memoizeOptions:i=[],argsMemoize:j=b1,argsMemoizeOptions:k=[],devModeChecks:l={}}={...c,...f},m=bN(i),n=bN(k),o=function(a){let b=Array.isArray(a[0])?a[0]:a;return!function(a,b="expected all items to be functions, instead received the following types: "){if(!a.every(a=>"function"==typeof a)){let c=a.map(a=>"function"==typeof a?`function ${a.name||"unnamed"}()`:typeof a).join(", ");throw TypeError(`${b}[${c}]`)}}(b,"createSelector expects all input-selectors to be functions, but received the following types: "),b}(a),p=h(function(){return d++,g.apply(null,arguments)},...m);return Object.assign(j(function(){e++;let a=function(a,b){let c=[],{length:d}=a;for(let e=0;e<d;e++)c.push(a[e].apply(null,b));return c}(o,arguments);return b=p.apply(null,a)},...n),{resultFunc:g,memoizedResultFunc:p,dependencies:o,dependencyRecomputations:()=>e,resetDependencyRecomputations:()=>{e=0},lastResult:()=>b,recomputations:()=>d,resetRecomputations:()=>{d=0},memoize:h,argsMemoize:j})};return Object.assign(d,{withTypes:()=>d}),d}(b1),b3=Object.assign((a,b=b2)=>{!function(a,b=`expected an object, instead received ${typeof a}`){if("object"!=typeof a)throw TypeError(b)}(a,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof a}`);let c=Object.keys(a);return b(c.map(b=>a[b]),(...a)=>a.reduce((a,b,d)=>(a[c[d]]=b,a),{}))},{withTypes:()=>b3}),b4=c(95153),b5=(0,f.createContext)(null),b6=a=>a,b7=()=>{var a=(0,f.useContext)(b5);return a?a.store.dispatch:b6},b8=()=>{},b9=()=>b8,ca=(a,b)=>a===b;function cb(a){var b=(0,f.useContext)(b5);return(0,b4.useSyncExternalStoreWithSelector)(b?b.subscription.addNestedSub:b9,b?b.store.getState:b8,b?b.store.getState:b8,b?a:b8,ca)}var cc=c(98323),cd=c.n(cc),ce=a=>a.legend.settings;function cf(a,b){if((e=a.length)>1)for(var c,d,e,f=1,g=a[b[0]],h=g.length;f<e;++f)for(d=g,g=a[b[f]],c=0;c<h;++c)g[c][1]+=g[c][0]=isNaN(d[c][1])?d[c][0]:d[c][1]}function cg(a){return"object"==typeof a&&"length"in a?a:Array.from(a)}function ch(a){return function(){return a}}function ci(a){for(var b=a.length,c=Array(b);--b>=0;)c[b]=b;return c}function cj(a,b){return a[b]}function ck(a){let b=[];return b.key=a,b}function cl(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cm(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cl(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cl(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}b2([a=>a.legend.payload,ce],(a,b)=>{var{itemSorter:c}=b,d=a.flat(1);return c?cd()(d,c):d}),Array.prototype.slice;var cn=Math.PI/180,co=(a,b,c,d)=>({x:a+Math.cos(-cn*d)*c,y:b+Math.sin(-cn*d)*c}),cp=function(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(a-(c.left||0)-(c.right||0)),Math.abs(b-(c.top||0)-(c.bottom||0)))/2};function cq(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cr(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cq(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cq(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cs(a,b,c){return null==a||null==b?c:p(b)?k()(a,b,c):"function"==typeof b?b(a):c}var ct=(a,b)=>"horizontal"===a&&"xAxis"===b||"vertical"===a&&"yAxis"===b||"centric"===a&&"angleAxis"===b||"radial"===a&&"radiusAxis"===b,cu=(a,b,c,d)=>{if(d)return a.map(a=>a.coordinate);var e,f,g=a.map(a=>(a.coordinate===b&&(e=!0),a.coordinate===c&&(f=!0),a.coordinate));return e||g.push(b),f||g.push(c),g},cv=(a,b,c)=>{if(!a)return null;var{duplicateDomain:d,type:e,range:f,scale:g,realScaleType:h,isCategorical:i,categoricalDomain:j,tickCount:k,ticks:n,niceTicks:o,axisType:p}=a;if(!g)return null;var q="scaleBand"===h&&g.bandwidth?g.bandwidth()/2:2,r=(b||c)&&"category"===e&&g.bandwidth?g.bandwidth()/q:0;return(r="angleAxis"===p&&f&&f.length>=2?2*l(f[0]-f[1])*r:r,b&&(n||o))?(n||o||[]).map((a,b)=>({coordinate:g(d?d.indexOf(a):a)+r,value:a,offset:r,index:b})).filter(a=>!m(a.coordinate)):i&&j?j.map((a,b)=>({coordinate:g(a)+r,value:a,index:b,offset:r})):g.ticks&&!c&&null!=k?g.ticks(k).map((a,b)=>({coordinate:g(a)+r,value:a,offset:r,index:b})):g.domain().map((a,b)=>({coordinate:g(a)+r,value:d?d[a]:a,index:b,offset:r}))},cw={sign:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0,g=0;g<b;++g){var h=m(a[g][c][1])?a[g][c][0]:a[g][c][1];h>=0?(a[g][c][0]=e,a[g][c][1]=e+h,e=a[g][c][1]):(a[g][c][0]=f,a[g][c][1]=f+h,f=a[g][c][1])}},expand:function(a,b){if((d=a.length)>0){for(var c,d,e,f=0,g=a[0].length;f<g;++f){for(e=c=0;c<d;++c)e+=a[c][f][1]||0;if(e)for(c=0;c<d;++c)a[c][f][1]/=e}cf(a,b)}},none:cf,silhouette:function(a,b){if((c=a.length)>0){for(var c,d=0,e=a[b[0]],f=e.length;d<f;++d){for(var g=0,h=0;g<c;++g)h+=a[g][d][1]||0;e[d][1]+=e[d][0]=-h/2}cf(a,b)}},wiggle:function(a,b){if((e=a.length)>0&&(d=(c=a[b[0]]).length)>0){for(var c,d,e,f=0,g=1;g<d;++g){for(var h=0,i=0,j=0;h<e;++h){for(var k=a[b[h]],l=k[g][1]||0,m=(l-(k[g-1][1]||0))/2,n=0;n<h;++n){var o=a[b[n]];m+=(o[g][1]||0)-(o[g-1][1]||0)}i+=l,j+=m*l}c[g-1][1]+=c[g-1][0]=f,i&&(f-=j/i)}c[g-1][1]+=c[g-1][0]=f,cf(a,b)}},positive:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0;f<b;++f){var g=m(a[f][c][1])?a[f][c][0]:a[f][c][1];g>=0?(a[f][c][0]=e,a[f][c][1]=e+g,e=a[f][c][1]):(a[f][c][0]=0,a[f][c][1]=0)}}},cx=a=>{var{axis:b,ticks:c,offset:d,bandSize:e,entry:f,index:g}=a;if("category"===b.type)return c[g]?c[g].coordinate+d:null;var h=cs(f,b.dataKey,b.scale.domain()[g]);return null==h?null:b.scale(h)-e/2+d},cy=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cz=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cA=(a,b,c)=>{if(a&&a.scale&&a.scale.bandwidth){var d=a.scale.bandwidth();if(!c||d>0)return d}if(a&&b&&b.length>=2){for(var e=cd()(b,a=>a.coordinate),f=1/0,g=1,h=e.length;g<h;g++){var i=e[g],j=e[g-1];f=Math.min((i.coordinate||0)-(j.coordinate||0),f)}return f===1/0?0:f}return c?void 0:0};function cB(a){var{tooltipEntrySettings:b,dataKey:c,payload:d,value:e,name:f}=a;return cr(cr({},b),{},{dataKey:c,payload:d,value:e,name:f})}function cC(a,b){return a?String(a):"string"==typeof b?b:void 0}var cD=a=>a.layout.width,cE=a=>a.layout.height,cF=a=>a.layout.scale,cG=a=>a.layout.margin,cH=b2(a=>a.cartesianAxis.xAxis,a=>Object.values(a)),cI=b2(a=>a.cartesianAxis.yAxis,a=>Object.values(a)),cJ="data-recharts-item-index",cK="data-recharts-item-data-key";function cL(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cM(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cL(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cL(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var cN=b2([cD,cE,cG,a=>a.brush.height,cH,cI,ce,a=>a.legend.size],(a,b,c,d,e,f,g,h)=>{var i=f.reduce((a,b)=>{var{orientation:c}=b;if(!b.mirror&&!b.hide){var d="number"==typeof b.width?b.width:60;return cM(cM({},a),{},{[c]:a[c]+d})}return a},{left:c.left||0,right:c.right||0}),j=e.reduce((a,b)=>{var{orientation:c}=b;return b.mirror||b.hide?a:cM(cM({},a),{},{[c]:k()(a,"".concat(c))+b.height})},{top:c.top||0,bottom:c.bottom||0}),l=cM(cM({},j),i),m=l.bottom;l.bottom+=d;var n=a-(l=((a,b,c)=>{if(b&&c){var{width:d,height:e}=c,{align:f,verticalAlign:g,layout:h}=b;if(("vertical"===h||"horizontal"===h&&"middle"===g)&&"center"!==f&&o(a[f]))return cr(cr({},a),{},{[f]:a[f]+(d||0)});if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==g&&o(a[g]))return cr(cr({},a),{},{[g]:a[g]+(e||0)})}return a})(l,g,h)).left-l.right,p=b-l.top-l.bottom;return cM(cM({brushBottom:m},l),{},{width:Math.max(n,0),height:Math.max(p,0)})}),cO=b2(cN,a=>({x:a.left,y:a.top,width:a.width,height:a.height})),cP=b2(cD,cE,(a,b)=>({x:0,y:0,width:a,height:b})),cQ=(0,f.createContext)(null),cR=()=>null!=(0,f.useContext)(cQ),cS=a=>a.brush,cT=b2([cS,cN,cG],(a,b,c)=>({height:a.height,x:o(a.x)?a.x:b.left,y:o(a.y)?a.y:b.top+b.height+b.brushBottom-((null==c?void 0:c.bottom)||0),width:o(a.width)?a.width:b.width})),cU=()=>{var a,b=cR(),c=cb(cO),d=cb(cT),e=null==(a=cb(cS))?void 0:a.padding;return b&&d&&e?{width:d.width-e.left-e.right,height:d.height-e.top-e.bottom,x:e.left,y:e.top}:c},cV={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},cW=()=>{var a;return null!=(a=cb(cN))?a:cV},cX=()=>cb(cD),cY=()=>cb(cE),cZ=a=>a.layout.layoutType,c$=()=>cb(cZ),c_=c(41421),c0=c.n(c_);function c1(a,b){switch(arguments.length){case 0:break;case 1:this.range(a);break;default:this.range(b).domain(a)}return this}function c2(a,b){switch(arguments.length){case 0:break;case 1:"function"==typeof a?this.interpolator(a):this.range(a);break;default:this.domain(a),"function"==typeof b?this.interpolator(b):this.range(b)}return this}class c3 extends Map{constructor(a,b=c5){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:b}}),null!=a)for(let[b,c]of a)this.set(b,c)}get(a){return super.get(c4(this,a))}has(a){return super.has(c4(this,a))}set(a,b){return super.set(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):(a.set(d,c),c)}(this,a),b)}delete(a){return super.delete(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)&&(c=a.get(d),a.delete(d)),c}(this,a))}}function c4({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):c}function c5(a){return null!==a&&"object"==typeof a?a.valueOf():a}let c6=Symbol("implicit");function c7(){var a=new c3,b=[],c=[],d=c6;function e(e){let f=a.get(e);if(void 0===f){if(d!==c6)return d;a.set(e,f=b.push(e)-1)}return c[f%c.length]}return e.domain=function(c){if(!arguments.length)return b.slice();for(let d of(b=[],a=new c3,c))a.has(d)||a.set(d,b.push(d)-1);return e},e.range=function(a){return arguments.length?(c=Array.from(a),e):c.slice()},e.unknown=function(a){return arguments.length?(d=a,e):d},e.copy=function(){return c7(b,c).unknown(d)},c1.apply(e,arguments),e}function c8(){var a,b,c=c7().unknown(void 0),d=c.domain,e=c.range,f=0,g=1,h=!1,i=0,j=0,k=.5;function l(){var c=d().length,l=g<f,m=l?g:f,n=l?f:g;a=(n-m)/Math.max(1,c-i+2*j),h&&(a=Math.floor(a)),m+=(n-m-a*(c-i))*k,b=a*(1-i),h&&(m=Math.round(m),b=Math.round(b));var o=(function(a,b,c){a*=1,b*=1,c=(e=arguments.length)<2?(b=a,a=0,1):e<3?1:+c;for(var d=-1,e=0|Math.max(0,Math.ceil((b-a)/c)),f=Array(e);++d<e;)f[d]=a+d*c;return f})(c).map(function(b){return m+a*b});return e(l?o.reverse():o)}return delete c.unknown,c.domain=function(a){return arguments.length?(d(a),l()):d()},c.range=function(a){return arguments.length?([f,g]=a,f*=1,g*=1,l()):[f,g]},c.rangeRound=function(a){return[f,g]=a,f*=1,g*=1,h=!0,l()},c.bandwidth=function(){return b},c.step=function(){return a},c.round=function(a){return arguments.length?(h=!!a,l()):h},c.padding=function(a){return arguments.length?(i=Math.min(1,j=+a),l()):i},c.paddingInner=function(a){return arguments.length?(i=Math.min(1,a),l()):i},c.paddingOuter=function(a){return arguments.length?(j=+a,l()):j},c.align=function(a){return arguments.length?(k=Math.max(0,Math.min(1,a)),l()):k},c.copy=function(){return c8(d(),[f,g]).round(h).paddingInner(i).paddingOuter(j).align(k)},c1.apply(l(),arguments)}function c9(){return function a(b){var c=b.copy;return b.padding=b.paddingOuter,delete b.paddingInner,delete b.paddingOuter,b.copy=function(){return a(c())},b}(c8.apply(null,arguments).paddingInner(1))}let da=Math.sqrt(50),db=Math.sqrt(10),dc=Math.sqrt(2);function dd(a,b,c){let d,e,f,g=(b-a)/Math.max(0,c),h=Math.floor(Math.log10(g)),i=g/Math.pow(10,h),j=i>=da?10:i>=db?5:i>=dc?2:1;return(h<0?(d=Math.round(a*(f=Math.pow(10,-h)/j)),e=Math.round(b*f),d/f<a&&++d,e/f>b&&--e,f=-f):(d=Math.round(a/(f=Math.pow(10,h)*j)),e=Math.round(b/f),d*f<a&&++d,e*f>b&&--e),e<d&&.5<=c&&c<2)?dd(a,b,2*c):[d,e,f]}function de(a,b,c){if(b*=1,a*=1,!((c*=1)>0))return[];if(a===b)return[a];let d=b<a,[e,f,g]=d?dd(b,a,c):dd(a,b,c);if(!(f>=e))return[];let h=f-e+1,i=Array(h);if(d)if(g<0)for(let a=0;a<h;++a)i[a]=-((f-a)/g);else for(let a=0;a<h;++a)i[a]=(f-a)*g;else if(g<0)for(let a=0;a<h;++a)i[a]=-((e+a)/g);else for(let a=0;a<h;++a)i[a]=(e+a)*g;return i}function df(a,b,c){return dd(a*=1,b*=1,c*=1)[2]}function dg(a,b,c){b*=1,a*=1,c*=1;let d=b<a,e=d?df(b,a,c):df(a,b,c);return(d?-1:1)*(e<0?-(1/e):e)}function dh(a,b){return null==a||null==b?NaN:a<b?-1:a>b?1:a>=b?0:NaN}function di(a,b){return null==a||null==b?NaN:b<a?-1:b>a?1:b>=a?0:NaN}function dj(a){let b,c,d;function e(a,d,f=0,g=a.length){if(f<g){if(0!==b(d,d))return g;do{let b=f+g>>>1;0>c(a[b],d)?f=b+1:g=b}while(f<g)}return f}return 2!==a.length?(b=dh,c=(b,c)=>dh(a(b),c),d=(b,c)=>a(b)-c):(b=a===dh||a===di?a:dk,c=a,d=a),{left:e,center:function(a,b,c=0,f=a.length){let g=e(a,b,c,f-1);return g>c&&d(a[g-1],b)>-d(a[g],b)?g-1:g},right:function(a,d,e=0,f=a.length){if(e<f){if(0!==b(d,d))return f;do{let b=e+f>>>1;0>=c(a[b],d)?e=b+1:f=b}while(e<f)}return e}}}function dk(){return 0}function dl(a){return null===a?NaN:+a}let dm=dj(dh),dn=dm.right;function dp(a,b,c){a.prototype=b.prototype=c,c.constructor=a}function dq(a,b){var c=Object.create(a.prototype);for(var d in b)c[d]=b[d];return c}function dr(){}dm.left,dj(dl).center;var ds="\\s*([+-]?\\d+)\\s*",dt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",du="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",dv=/^#([0-9a-f]{3,8})$/,dw=RegExp(`^rgb\\(${ds},${ds},${ds}\\)$`),dx=RegExp(`^rgb\\(${du},${du},${du}\\)$`),dy=RegExp(`^rgba\\(${ds},${ds},${ds},${dt}\\)$`),dz=RegExp(`^rgba\\(${du},${du},${du},${dt}\\)$`),dA=RegExp(`^hsl\\(${dt},${du},${du}\\)$`),dB=RegExp(`^hsla\\(${dt},${du},${du},${dt}\\)$`),dC={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function dD(){return this.rgb().formatHex()}function dE(){return this.rgb().formatRgb()}function dF(a){var b,c;return a=(a+"").trim().toLowerCase(),(b=dv.exec(a))?(c=b[1].length,b=parseInt(b[1],16),6===c?dG(b):3===c?new dJ(b>>8&15|b>>4&240,b>>4&15|240&b,(15&b)<<4|15&b,1):8===c?dH(b>>24&255,b>>16&255,b>>8&255,(255&b)/255):4===c?dH(b>>12&15|b>>8&240,b>>8&15|b>>4&240,b>>4&15|240&b,((15&b)<<4|15&b)/255):null):(b=dw.exec(a))?new dJ(b[1],b[2],b[3],1):(b=dx.exec(a))?new dJ(255*b[1]/100,255*b[2]/100,255*b[3]/100,1):(b=dy.exec(a))?dH(b[1],b[2],b[3],b[4]):(b=dz.exec(a))?dH(255*b[1]/100,255*b[2]/100,255*b[3]/100,b[4]):(b=dA.exec(a))?dP(b[1],b[2]/100,b[3]/100,1):(b=dB.exec(a))?dP(b[1],b[2]/100,b[3]/100,b[4]):dC.hasOwnProperty(a)?dG(dC[a]):"transparent"===a?new dJ(NaN,NaN,NaN,0):null}function dG(a){return new dJ(a>>16&255,a>>8&255,255&a,1)}function dH(a,b,c,d){return d<=0&&(a=b=c=NaN),new dJ(a,b,c,d)}function dI(a,b,c,d){var e;return 1==arguments.length?((e=a)instanceof dr||(e=dF(e)),e)?new dJ((e=e.rgb()).r,e.g,e.b,e.opacity):new dJ:new dJ(a,b,c,null==d?1:d)}function dJ(a,b,c,d){this.r=+a,this.g=+b,this.b=+c,this.opacity=+d}function dK(){return`#${dO(this.r)}${dO(this.g)}${dO(this.b)}`}function dL(){let a=dM(this.opacity);return`${1===a?"rgb(":"rgba("}${dN(this.r)}, ${dN(this.g)}, ${dN(this.b)}${1===a?")":`, ${a})`}`}function dM(a){return isNaN(a)?1:Math.max(0,Math.min(1,a))}function dN(a){return Math.max(0,Math.min(255,Math.round(a)||0))}function dO(a){return((a=dN(a))<16?"0":"")+a.toString(16)}function dP(a,b,c,d){return d<=0?a=b=c=NaN:c<=0||c>=1?a=b=NaN:b<=0&&(a=NaN),new dR(a,b,c,d)}function dQ(a){if(a instanceof dR)return new dR(a.h,a.s,a.l,a.opacity);if(a instanceof dr||(a=dF(a)),!a)return new dR;if(a instanceof dR)return a;var b=(a=a.rgb()).r/255,c=a.g/255,d=a.b/255,e=Math.min(b,c,d),f=Math.max(b,c,d),g=NaN,h=f-e,i=(f+e)/2;return h?(g=b===f?(c-d)/h+(c<d)*6:c===f?(d-b)/h+2:(b-c)/h+4,h/=i<.5?f+e:2-f-e,g*=60):h=i>0&&i<1?0:g,new dR(g,h,i,a.opacity)}function dR(a,b,c,d){this.h=+a,this.s=+b,this.l=+c,this.opacity=+d}function dS(a){return(a=(a||0)%360)<0?a+360:a}function dT(a){return Math.max(0,Math.min(1,a||0))}function dU(a,b,c){return(a<60?b+(c-b)*a/60:a<180?c:a<240?b+(c-b)*(240-a)/60:b)*255}function dV(a,b,c,d,e){var f=a*a,g=f*a;return((1-3*a+3*f-g)*b+(4-6*f+3*g)*c+(1+3*a+3*f-3*g)*d+g*e)/6}dp(dr,dF,{copy(a){return Object.assign(new this.constructor,this,a)},displayable(){return this.rgb().displayable()},hex:dD,formatHex:dD,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return dQ(this).formatHsl()},formatRgb:dE,toString:dE}),dp(dJ,dI,dq(dr,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new dJ(this.r*a,this.g*a,this.b*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new dJ(this.r*a,this.g*a,this.b*a,this.opacity)},rgb(){return this},clamp(){return new dJ(dN(this.r),dN(this.g),dN(this.b),dM(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:dK,formatHex:dK,formatHex8:function(){return`#${dO(this.r)}${dO(this.g)}${dO(this.b)}${dO((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:dL,toString:dL})),dp(dR,function(a,b,c,d){return 1==arguments.length?dQ(a):new dR(a,b,c,null==d?1:d)},dq(dr,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new dR(this.h,this.s,this.l*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new dR(this.h,this.s,this.l*a,this.opacity)},rgb(){var a=this.h%360+(this.h<0)*360,b=isNaN(a)||isNaN(this.s)?0:this.s,c=this.l,d=c+(c<.5?c:1-c)*b,e=2*c-d;return new dJ(dU(a>=240?a-240:a+120,e,d),dU(a,e,d),dU(a<120?a+240:a-120,e,d),this.opacity)},clamp(){return new dR(dS(this.h),dT(this.s),dT(this.l),dM(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let a=dM(this.opacity);return`${1===a?"hsl(":"hsla("}${dS(this.h)}, ${100*dT(this.s)}%, ${100*dT(this.l)}%${1===a?")":`, ${a})`}`}}));let dW=a=>()=>a;function dX(a,b){var c=b-a;return c?function(b){return a+b*c}:dW(isNaN(a)?b:a)}let dY=function a(b){var c,d=1==(c=+b)?dX:function(a,b){var d,e,f;return b-a?(d=a,e=b,d=Math.pow(d,f=c),e=Math.pow(e,f)-d,f=1/f,function(a){return Math.pow(d+a*e,f)}):dW(isNaN(a)?b:a)};function e(a,b){var c=d((a=dI(a)).r,(b=dI(b)).r),e=d(a.g,b.g),f=d(a.b,b.b),g=dX(a.opacity,b.opacity);return function(b){return a.r=c(b),a.g=e(b),a.b=f(b),a.opacity=g(b),a+""}}return e.gamma=a,e}(1);function dZ(a){return function(b){var c,d,e=b.length,f=Array(e),g=Array(e),h=Array(e);for(c=0;c<e;++c)d=dI(b[c]),f[c]=d.r||0,g[c]=d.g||0,h[c]=d.b||0;return f=a(f),g=a(g),h=a(h),d.opacity=1,function(a){return d.r=f(a),d.g=g(a),d.b=h(a),d+""}}}function d$(a,b){return a*=1,b*=1,function(c){return a*(1-c)+b*c}}dZ(function(a){var b=a.length-1;return function(c){var d=c<=0?c=0:c>=1?(c=1,b-1):Math.floor(c*b),e=a[d],f=a[d+1],g=d>0?a[d-1]:2*e-f,h=d<b-1?a[d+2]:2*f-e;return dV((c-d/b)*b,g,e,f,h)}}),dZ(function(a){var b=a.length;return function(c){var d=Math.floor(((c%=1)<0?++c:c)*b),e=a[(d+b-1)%b],f=a[d%b],g=a[(d+1)%b],h=a[(d+2)%b];return dV((c-d/b)*b,e,f,g,h)}});var d_=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,d0=RegExp(d_.source,"g");function d1(a,b){var c,d,e=typeof b;return null==b||"boolean"===e?dW(b):("number"===e?d$:"string"===e?(d=dF(b))?(b=d,dY):function(a,b){var c,d,e,f,g,h=d_.lastIndex=d0.lastIndex=0,i=-1,j=[],k=[];for(a+="",b+="";(e=d_.exec(a))&&(f=d0.exec(b));)(g=f.index)>h&&(g=b.slice(h,g),j[i]?j[i]+=g:j[++i]=g),(e=e[0])===(f=f[0])?j[i]?j[i]+=f:j[++i]=f:(j[++i]=null,k.push({i:i,x:d$(e,f)})),h=d0.lastIndex;return h<b.length&&(g=b.slice(h),j[i]?j[i]+=g:j[++i]=g),j.length<2?k[0]?(c=k[0].x,function(a){return c(a)+""}):(d=b,function(){return d}):(b=k.length,function(a){for(var c,d=0;d<b;++d)j[(c=k[d]).i]=c.x(a);return j.join("")})}:b instanceof dF?dY:b instanceof Date?function(a,b){var c=new Date;return a*=1,b*=1,function(d){return c.setTime(a*(1-d)+b*d),c}}:!ArrayBuffer.isView(c=b)||c instanceof DataView?Array.isArray(b)?function(a,b){var c,d=b?b.length:0,e=a?Math.min(d,a.length):0,f=Array(e),g=Array(d);for(c=0;c<e;++c)f[c]=d1(a[c],b[c]);for(;c<d;++c)g[c]=b[c];return function(a){for(c=0;c<e;++c)g[c]=f[c](a);return g}}:"function"!=typeof b.valueOf&&"function"!=typeof b.toString||isNaN(b)?function(a,b){var c,d={},e={};for(c in(null===a||"object"!=typeof a)&&(a={}),(null===b||"object"!=typeof b)&&(b={}),b)c in a?d[c]=d1(a[c],b[c]):e[c]=b[c];return function(a){for(c in d)e[c]=d[c](a);return e}}:d$:function(a,b){b||(b=[]);var c,d=a?Math.min(b.length,a.length):0,e=b.slice();return function(f){for(c=0;c<d;++c)e[c]=a[c]*(1-f)+b[c]*f;return e}})(a,b)}function d2(a,b){return a*=1,b*=1,function(c){return Math.round(a*(1-c)+b*c)}}function d3(a){return+a}var d4=[0,1];function d5(a){return a}function d6(a,b){var c;return(b-=a*=1)?function(c){return(c-a)/b}:(c=isNaN(b)?NaN:.5,function(){return c})}function d7(a,b,c){var d=a[0],e=a[1],f=b[0],g=b[1];return e<d?(d=d6(e,d),f=c(g,f)):(d=d6(d,e),f=c(f,g)),function(a){return f(d(a))}}function d8(a,b,c){var d=Math.min(a.length,b.length)-1,e=Array(d),f=Array(d),g=-1;for(a[d]<a[0]&&(a=a.slice().reverse(),b=b.slice().reverse());++g<d;)e[g]=d6(a[g],a[g+1]),f[g]=c(b[g],b[g+1]);return function(b){var c=dn(a,b,1,d)-1;return f[c](e[c](b))}}function d9(a,b){return b.domain(a.domain()).range(a.range()).interpolate(a.interpolate()).clamp(a.clamp()).unknown(a.unknown())}function ea(){var a,b,c,d,e,f,g=d4,h=d4,i=d1,j=d5;function k(){var a,b,c,i=Math.min(g.length,h.length);return j!==d5&&(a=g[0],b=g[i-1],a>b&&(c=a,a=b,b=c),j=function(c){return Math.max(a,Math.min(b,c))}),d=i>2?d8:d7,e=f=null,l}function l(b){return null==b||isNaN(b*=1)?c:(e||(e=d(g.map(a),h,i)))(a(j(b)))}return l.invert=function(c){return j(b((f||(f=d(h,g.map(a),d$)))(c)))},l.domain=function(a){return arguments.length?(g=Array.from(a,d3),k()):g.slice()},l.range=function(a){return arguments.length?(h=Array.from(a),k()):h.slice()},l.rangeRound=function(a){return h=Array.from(a),i=d2,k()},l.clamp=function(a){return arguments.length?(j=!!a||d5,k()):j!==d5},l.interpolate=function(a){return arguments.length?(i=a,k()):i},l.unknown=function(a){return arguments.length?(c=a,l):c},function(c,d){return a=c,b=d,k()}}function eb(){return ea()(d5,d5)}var ec=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ed(a){var b;if(!(b=ec.exec(a)))throw Error("invalid format: "+a);return new ee({fill:b[1],align:b[2],sign:b[3],symbol:b[4],zero:b[5],width:b[6],comma:b[7],precision:b[8]&&b[8].slice(1),trim:b[9],type:b[10]})}function ee(a){this.fill=void 0===a.fill?" ":a.fill+"",this.align=void 0===a.align?">":a.align+"",this.sign=void 0===a.sign?"-":a.sign+"",this.symbol=void 0===a.symbol?"":a.symbol+"",this.zero=!!a.zero,this.width=void 0===a.width?void 0:+a.width,this.comma=!!a.comma,this.precision=void 0===a.precision?void 0:+a.precision,this.trim=!!a.trim,this.type=void 0===a.type?"":a.type+""}function ef(a,b){if((c=(a=b?a.toExponential(b-1):a.toExponential()).indexOf("e"))<0)return null;var c,d=a.slice(0,c);return[d.length>1?d[0]+d.slice(2):d,+a.slice(c+1)]}function eg(a){return(a=ef(Math.abs(a)))?a[1]:NaN}function eh(a,b){var c=ef(a,b);if(!c)return a+"";var d=c[0],e=c[1];return e<0?"0."+Array(-e).join("0")+d:d.length>e+1?d.slice(0,e+1)+"."+d.slice(e+1):d+Array(e-d.length+2).join("0")}ed.prototype=ee.prototype,ee.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ei={"%":(a,b)=>(100*a).toFixed(b),b:a=>Math.round(a).toString(2),c:a=>a+"",d:function(a){return Math.abs(a=Math.round(a))>=1e21?a.toLocaleString("en").replace(/,/g,""):a.toString(10)},e:(a,b)=>a.toExponential(b),f:(a,b)=>a.toFixed(b),g:(a,b)=>a.toPrecision(b),o:a=>Math.round(a).toString(8),p:(a,b)=>eh(100*a,b),r:eh,s:function(a,b){var c=ef(a,b);if(!c)return a+"";var d=c[0],e=c[1],f=e-(gN=3*Math.max(-8,Math.min(8,Math.floor(e/3))))+1,g=d.length;return f===g?d:f>g?d+Array(f-g+1).join("0"):f>0?d.slice(0,f)+"."+d.slice(f):"0."+Array(1-f).join("0")+ef(a,Math.max(0,b+f-1))[0]},X:a=>Math.round(a).toString(16).toUpperCase(),x:a=>Math.round(a).toString(16)};function ej(a){return a}var ek=Array.prototype.map,el=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function em(a,b,c,d){var e,f,g=dg(a,b,c);switch((d=ed(null==d?",f":d)).type){case"s":var h=Math.max(Math.abs(a),Math.abs(b));return null!=d.precision||isNaN(f=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eg(h)/3)))-eg(Math.abs(g))))||(d.precision=f),gQ(d,h);case"":case"e":case"g":case"p":case"r":null!=d.precision||isNaN(f=Math.max(0,eg(Math.abs(Math.max(Math.abs(a),Math.abs(b)))-(e=Math.abs(e=g)))-eg(e))+1)||(d.precision=f-("e"===d.type));break;case"f":case"%":null!=d.precision||isNaN(f=Math.max(0,-eg(Math.abs(g))))||(d.precision=f-("%"===d.type)*2)}return gP(d)}function en(a){var b=a.domain;return a.ticks=function(a){var c=b();return de(c[0],c[c.length-1],null==a?10:a)},a.tickFormat=function(a,c){var d=b();return em(d[0],d[d.length-1],null==a?10:a,c)},a.nice=function(c){null==c&&(c=10);var d,e,f=b(),g=0,h=f.length-1,i=f[g],j=f[h],k=10;for(j<i&&(e=i,i=j,j=e,e=g,g=h,h=e);k-- >0;){if((e=df(i,j,c))===d)return f[g]=i,f[h]=j,b(f);if(e>0)i=Math.floor(i/e)*e,j=Math.ceil(j/e)*e;else if(e<0)i=Math.ceil(i*e)/e,j=Math.floor(j*e)/e;else break;d=e}return a},a}function eo(a,b){a=a.slice();var c,d=0,e=a.length-1,f=a[d],g=a[e];return g<f&&(c=d,d=e,e=c,c=f,f=g,g=c),a[d]=b.floor(f),a[e]=b.ceil(g),a}function ep(a){return Math.log(a)}function eq(a){return Math.exp(a)}function er(a){return-Math.log(-a)}function es(a){return-Math.exp(-a)}function et(a){return isFinite(a)?+("1e"+a):a<0?0:a}function eu(a){return(b,c)=>-a(-b,c)}function ev(a){let b,c,d=a(ep,eq),e=d.domain,f=10;function g(){var g,h;return b=(g=f)===Math.E?Math.log:10===g&&Math.log10||2===g&&Math.log2||(g=Math.log(g),a=>Math.log(a)/g),c=10===(h=f)?et:h===Math.E?Math.exp:a=>Math.pow(h,a),e()[0]<0?(b=eu(b),c=eu(c),a(er,es)):a(ep,eq),d}return d.base=function(a){return arguments.length?(f=+a,g()):f},d.domain=function(a){return arguments.length?(e(a),g()):e()},d.ticks=a=>{let d,g,h=e(),i=h[0],j=h[h.length-1],k=j<i;k&&([i,j]=[j,i]);let l=b(i),m=b(j),n=null==a?10:+a,o=[];if(!(f%1)&&m-l<n){if(l=Math.floor(l),m=Math.ceil(m),i>0){for(;l<=m;++l)for(d=1;d<f;++d)if(!((g=l<0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}}else for(;l<=m;++l)for(d=f-1;d>=1;--d)if(!((g=l>0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}2*o.length<n&&(o=de(i,j,n))}else o=de(l,m,Math.min(m-l,n)).map(c);return k?o.reverse():o},d.tickFormat=(a,e)=>{if(null==a&&(a=10),null==e&&(e=10===f?"s":","),"function"!=typeof e&&(f%1||null!=(e=ed(e)).precision||(e.trim=!0),e=gP(e)),a===1/0)return e;let g=Math.max(1,f*a/d.ticks().length);return a=>{let d=a/c(Math.round(b(a)));return d*f<f-.5&&(d*=f),d<=g?e(a):""}},d.nice=()=>e(eo(e(),{floor:a=>c(Math.floor(b(a))),ceil:a=>c(Math.ceil(b(a)))})),d}function ew(a){return function(b){return Math.sign(b)*Math.log1p(Math.abs(b/a))}}function ex(a){return function(b){return Math.sign(b)*Math.expm1(Math.abs(b))*a}}function ey(a){var b=1,c=a(ew(1),ex(b));return c.constant=function(c){return arguments.length?a(ew(b=+c),ex(b)):b},en(c)}function ez(a){return function(b){return b<0?-Math.pow(-b,a):Math.pow(b,a)}}function eA(a){return a<0?-Math.sqrt(-a):Math.sqrt(a)}function eB(a){return a<0?-a*a:a*a}function eC(a){var b=a(d5,d5),c=1;return b.exponent=function(b){return arguments.length?1==(c=+b)?a(d5,d5):.5===c?a(eA,eB):a(ez(c),ez(1/c)):c},en(b)}function eD(){var a=eC(ea());return a.copy=function(){return d9(a,eD()).exponent(a.exponent())},c1.apply(a,arguments),a}function eE(){return eD.apply(null,arguments).exponent(.5)}function eF(a){return Math.sign(a)*a*a}function eG(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c<b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c<e||void 0===c&&e>=e)&&(c=e)}return c}function eH(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c>b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c>e||void 0===c&&e>=e)&&(c=e)}return c}function eI(a,b){return(null==a||!(a>=a))-(null==b||!(b>=b))||(a<b?-1:+(a>b))}function eJ(a,b,c){let d=a[b];a[b]=a[c],a[c]=d}gP=(gO=function(a){var b,c,d,e=void 0===a.grouping||void 0===a.thousands?ej:(b=ek.call(a.grouping,Number),c=a.thousands+"",function(a,d){for(var e=a.length,f=[],g=0,h=b[0],i=0;e>0&&h>0&&(i+h+1>d&&(h=Math.max(1,d-i)),f.push(a.substring(e-=h,e+h)),!((i+=h+1)>d));)h=b[g=(g+1)%b.length];return f.reverse().join(c)}),f=void 0===a.currency?"":a.currency[0]+"",g=void 0===a.currency?"":a.currency[1]+"",h=void 0===a.decimal?".":a.decimal+"",i=void 0===a.numerals?ej:(d=ek.call(a.numerals,String),function(a){return a.replace(/[0-9]/g,function(a){return d[+a]})}),j=void 0===a.percent?"%":a.percent+"",k=void 0===a.minus?"−":a.minus+"",l=void 0===a.nan?"NaN":a.nan+"";function m(a){var b=(a=ed(a)).fill,c=a.align,d=a.sign,m=a.symbol,n=a.zero,o=a.width,p=a.comma,q=a.precision,r=a.trim,s=a.type;"n"===s?(p=!0,s="g"):ei[s]||(void 0===q&&(q=12),r=!0,s="g"),(n||"0"===b&&"="===c)&&(n=!0,b="0",c="=");var t="$"===m?f:"#"===m&&/[boxX]/.test(s)?"0"+s.toLowerCase():"",u="$"===m?g:/[%p]/.test(s)?j:"",v=ei[s],w=/[defgprs%]/.test(s);function x(a){var f,g,j,m=t,x=u;if("c"===s)x=v(a)+x,a="";else{var y=(a*=1)<0||1/a<0;if(a=isNaN(a)?l:v(Math.abs(a),q),r&&(a=function(a){a:for(var b,c=a.length,d=1,e=-1;d<c;++d)switch(a[d]){case".":e=b=d;break;case"0":0===e&&(e=d),b=d;break;default:if(!+a[d])break a;e>0&&(e=0)}return e>0?a.slice(0,e)+a.slice(b+1):a}(a)),y&&0==+a&&"+"!==d&&(y=!1),m=(y?"("===d?d:k:"-"===d||"("===d?"":d)+m,x=("s"===s?el[8+gN/3]:"")+x+(y&&"("===d?")":""),w){for(f=-1,g=a.length;++f<g;)if(48>(j=a.charCodeAt(f))||j>57){x=(46===j?h+a.slice(f+1):a.slice(f))+x,a=a.slice(0,f);break}}}p&&!n&&(a=e(a,1/0));var z=m.length+a.length+x.length,A=z<o?Array(o-z+1).join(b):"";switch(p&&n&&(a=e(A+a,A.length?o-x.length:1/0),A=""),c){case"<":a=m+a+x+A;break;case"=":a=m+A+a+x;break;case"^":a=A.slice(0,z=A.length>>1)+m+a+x+A.slice(z);break;default:a=A+m+a+x}return i(a)}return q=void 0===q?6:/[gprs]/.test(s)?Math.max(1,Math.min(21,q)):Math.max(0,Math.min(20,q)),x.toString=function(){return a+""},x}return{format:m,formatPrefix:function(a,b){var c=m(((a=ed(a)).type="f",a)),d=3*Math.max(-8,Math.min(8,Math.floor(eg(b)/3))),e=Math.pow(10,-d),f=el[8+d/3];return function(a){return c(e*a)+f}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,gQ=gO.formatPrefix;let eK=new Date,eL=new Date;function eM(a,b,c,d){function e(b){return a(b=0==arguments.length?new Date:new Date(+b)),b}return e.floor=b=>(a(b=new Date(+b)),b),e.ceil=c=>(a(c=new Date(c-1)),b(c,1),a(c),c),e.round=a=>{let b=e(a),c=e.ceil(a);return a-b<c-a?b:c},e.offset=(a,c)=>(b(a=new Date(+a),null==c?1:Math.floor(c)),a),e.range=(c,d,f)=>{let g,h=[];if(c=e.ceil(c),f=null==f?1:Math.floor(f),!(c<d)||!(f>0))return h;do h.push(g=new Date(+c)),b(c,f),a(c);while(g<c&&c<d);return h},e.filter=c=>eM(b=>{if(b>=b)for(;a(b),!c(b);)b.setTime(b-1)},(a,d)=>{if(a>=a)if(d<0)for(;++d<=0;)for(;b(a,-1),!c(a););else for(;--d>=0;)for(;b(a,1),!c(a););}),c&&(e.count=(b,d)=>(eK.setTime(+b),eL.setTime(+d),a(eK),a(eL),Math.floor(c(eK,eL))),e.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?e.filter(d?b=>d(b)%a==0:b=>e.count(0,b)%a==0):e:null),e}let eN=eM(()=>{},(a,b)=>{a.setTime(+a+b)},(a,b)=>b-a);eN.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?eM(b=>{b.setTime(Math.floor(b/a)*a)},(b,c)=>{b.setTime(+b+c*a)},(b,c)=>(c-b)/a):eN:null,eN.range;let eO=eM(a=>{a.setTime(a-a.getMilliseconds())},(a,b)=>{a.setTime(+a+1e3*b)},(a,b)=>(b-a)/1e3,a=>a.getUTCSeconds());eO.range;let eP=eM(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds())},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getMinutes());eP.range;let eQ=eM(a=>{a.setUTCSeconds(0,0)},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getUTCMinutes());eQ.range;let eR=eM(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds()-6e4*a.getMinutes())},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getHours());eR.range;let eS=eM(a=>{a.setUTCMinutes(0,0,0)},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getUTCHours());eS.range;let eT=eM(a=>a.setHours(0,0,0,0),(a,b)=>a.setDate(a.getDate()+b),(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/864e5,a=>a.getDate()-1);eT.range;let eU=eM(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>a.getUTCDate()-1);eU.range;let eV=eM(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>Math.floor(a/864e5));function eW(a){return eM(b=>{b.setDate(b.getDate()-(b.getDay()+7-a)%7),b.setHours(0,0,0,0)},(a,b)=>{a.setDate(a.getDate()+7*b)},(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/6048e5)}eV.range;let eX=eW(0),eY=eW(1),eZ=eW(2),e$=eW(3),e_=eW(4),e0=eW(5),e1=eW(6);function e2(a){return eM(b=>{b.setUTCDate(b.getUTCDate()-(b.getUTCDay()+7-a)%7),b.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+7*b)},(a,b)=>(b-a)/6048e5)}eX.range,eY.range,eZ.range,e$.range,e_.range,e0.range,e1.range;let e3=e2(0),e4=e2(1),e5=e2(2),e6=e2(3),e7=e2(4),e8=e2(5),e9=e2(6);e3.range,e4.range,e5.range,e6.range,e7.range,e8.range,e9.range;let fa=eM(a=>{a.setDate(1),a.setHours(0,0,0,0)},(a,b)=>{a.setMonth(a.getMonth()+b)},(a,b)=>b.getMonth()-a.getMonth()+(b.getFullYear()-a.getFullYear())*12,a=>a.getMonth());fa.range;let fb=eM(a=>{a.setUTCDate(1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCMonth(a.getUTCMonth()+b)},(a,b)=>b.getUTCMonth()-a.getUTCMonth()+(b.getUTCFullYear()-a.getUTCFullYear())*12,a=>a.getUTCMonth());fb.range;let fc=eM(a=>{a.setMonth(0,1),a.setHours(0,0,0,0)},(a,b)=>{a.setFullYear(a.getFullYear()+b)},(a,b)=>b.getFullYear()-a.getFullYear(),a=>a.getFullYear());fc.every=a=>isFinite(a=Math.floor(a))&&a>0?eM(b=>{b.setFullYear(Math.floor(b.getFullYear()/a)*a),b.setMonth(0,1),b.setHours(0,0,0,0)},(b,c)=>{b.setFullYear(b.getFullYear()+c*a)}):null,fc.range;let fd=eM(a=>{a.setUTCMonth(0,1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCFullYear(a.getUTCFullYear()+b)},(a,b)=>b.getUTCFullYear()-a.getUTCFullYear(),a=>a.getUTCFullYear());function fe(a,b,c,d,e,f){let g=[[eO,1,1e3],[eO,5,5e3],[eO,15,15e3],[eO,30,3e4],[f,1,6e4],[f,5,3e5],[f,15,9e5],[f,30,18e5],[e,1,36e5],[e,3,108e5],[e,6,216e5],[e,12,432e5],[d,1,864e5],[d,2,1728e5],[c,1,6048e5],[b,1,2592e6],[b,3,7776e6],[a,1,31536e6]];function h(b,c,d){let e=Math.abs(c-b)/d,f=dj(([,,a])=>a).right(g,e);if(f===g.length)return a.every(dg(b/31536e6,c/31536e6,d));if(0===f)return eN.every(Math.max(dg(b,c,d),1));let[h,i]=g[e/g[f-1][2]<g[f][2]/e?f-1:f];return h.every(i)}return[function(a,b,c){let d=b<a;d&&([a,b]=[b,a]);let e=c&&"function"==typeof c.range?c:h(a,b,c),f=e?e.range(a,+b+1):[];return d?f.reverse():f},h]}fd.every=a=>isFinite(a=Math.floor(a))&&a>0?eM(b=>{b.setUTCFullYear(Math.floor(b.getUTCFullYear()/a)*a),b.setUTCMonth(0,1),b.setUTCHours(0,0,0,0)},(b,c)=>{b.setUTCFullYear(b.getUTCFullYear()+c*a)}):null,fd.range;let[ff,fg]=fe(fd,fb,e3,eV,eS,eQ),[fh,fi]=fe(fc,fa,eX,eT,eR,eP);function fj(a){if(0<=a.y&&a.y<100){var b=new Date(-1,a.m,a.d,a.H,a.M,a.S,a.L);return b.setFullYear(a.y),b}return new Date(a.y,a.m,a.d,a.H,a.M,a.S,a.L)}function fk(a){if(0<=a.y&&a.y<100){var b=new Date(Date.UTC(-1,a.m,a.d,a.H,a.M,a.S,a.L));return b.setUTCFullYear(a.y),b}return new Date(Date.UTC(a.y,a.m,a.d,a.H,a.M,a.S,a.L))}function fl(a,b,c){return{y:a,m:b,d:c,H:0,M:0,S:0,L:0}}var fm={"-":"",_:" ",0:"0"},fn=/^\s*\d+/,fo=/^%/,fp=/[\\^$*+?|[\]().{}]/g;function fq(a,b,c){var d=a<0?"-":"",e=(d?-a:a)+"",f=e.length;return d+(f<c?Array(c-f+1).join(b)+e:e)}function fr(a){return a.replace(fp,"\\$&")}function fs(a){return RegExp("^(?:"+a.map(fr).join("|")+")","i")}function ft(a){return new Map(a.map((a,b)=>[a.toLowerCase(),b]))}function fu(a,b,c){var d=fn.exec(b.slice(c,c+1));return d?(a.w=+d[0],c+d[0].length):-1}function fv(a,b,c){var d=fn.exec(b.slice(c,c+1));return d?(a.u=+d[0],c+d[0].length):-1}function fw(a,b,c){var d=fn.exec(b.slice(c,c+2));return d?(a.U=+d[0],c+d[0].length):-1}function fx(a,b,c){var d=fn.exec(b.slice(c,c+2));return d?(a.V=+d[0],c+d[0].length):-1}function fy(a,b,c){var d=fn.exec(b.slice(c,c+2));return d?(a.W=+d[0],c+d[0].length):-1}function fz(a,b,c){var d=fn.exec(b.slice(c,c+4));return d?(a.y=+d[0],c+d[0].length):-1}function fA(a,b,c){var d=fn.exec(b.slice(c,c+2));return d?(a.y=+d[0]+(+d[0]>68?1900:2e3),c+d[0].length):-1}function fB(a,b,c){var d=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(b.slice(c,c+6));return d?(a.Z=d[1]?0:-(d[2]+(d[3]||"00")),c+d[0].length):-1}function fC(a,b,c){var d=fn.exec(b.slice(c,c+1));return d?(a.q=3*d[0]-3,c+d[0].length):-1}function fD(a,b,c){var d=fn.exec(b.slice(c,c+2));return d?(a.m=d[0]-1,c+d[0].length):-1}function fE(a,b,c){var d=fn.exec(b.slice(c,c+2));return d?(a.d=+d[0],c+d[0].length):-1}function fF(a,b,c){var d=fn.exec(b.slice(c,c+3));return d?(a.m=0,a.d=+d[0],c+d[0].length):-1}function fG(a,b,c){var d=fn.exec(b.slice(c,c+2));return d?(a.H=+d[0],c+d[0].length):-1}function fH(a,b,c){var d=fn.exec(b.slice(c,c+2));return d?(a.M=+d[0],c+d[0].length):-1}function fI(a,b,c){var d=fn.exec(b.slice(c,c+2));return d?(a.S=+d[0],c+d[0].length):-1}function fJ(a,b,c){var d=fn.exec(b.slice(c,c+3));return d?(a.L=+d[0],c+d[0].length):-1}function fK(a,b,c){var d=fn.exec(b.slice(c,c+6));return d?(a.L=Math.floor(d[0]/1e3),c+d[0].length):-1}function fL(a,b,c){var d=fo.exec(b.slice(c,c+1));return d?c+d[0].length:-1}function fM(a,b,c){var d=fn.exec(b.slice(c));return d?(a.Q=+d[0],c+d[0].length):-1}function fN(a,b,c){var d=fn.exec(b.slice(c));return d?(a.s=+d[0],c+d[0].length):-1}function fO(a,b){return fq(a.getDate(),b,2)}function fP(a,b){return fq(a.getHours(),b,2)}function fQ(a,b){return fq(a.getHours()%12||12,b,2)}function fR(a,b){return fq(1+eT.count(fc(a),a),b,3)}function fS(a,b){return fq(a.getMilliseconds(),b,3)}function fT(a,b){return fS(a,b)+"000"}function fU(a,b){return fq(a.getMonth()+1,b,2)}function fV(a,b){return fq(a.getMinutes(),b,2)}function fW(a,b){return fq(a.getSeconds(),b,2)}function fX(a){var b=a.getDay();return 0===b?7:b}function fY(a,b){return fq(eX.count(fc(a)-1,a),b,2)}function fZ(a){var b=a.getDay();return b>=4||0===b?e_(a):e_.ceil(a)}function f$(a,b){return a=fZ(a),fq(e_.count(fc(a),a)+(4===fc(a).getDay()),b,2)}function f_(a){return a.getDay()}function f0(a,b){return fq(eY.count(fc(a)-1,a),b,2)}function f1(a,b){return fq(a.getFullYear()%100,b,2)}function f2(a,b){return fq((a=fZ(a)).getFullYear()%100,b,2)}function f3(a,b){return fq(a.getFullYear()%1e4,b,4)}function f4(a,b){var c=a.getDay();return fq((a=c>=4||0===c?e_(a):e_.ceil(a)).getFullYear()%1e4,b,4)}function f5(a){var b=a.getTimezoneOffset();return(b>0?"-":(b*=-1,"+"))+fq(b/60|0,"0",2)+fq(b%60,"0",2)}function f6(a,b){return fq(a.getUTCDate(),b,2)}function f7(a,b){return fq(a.getUTCHours(),b,2)}function f8(a,b){return fq(a.getUTCHours()%12||12,b,2)}function f9(a,b){return fq(1+eU.count(fd(a),a),b,3)}function ga(a,b){return fq(a.getUTCMilliseconds(),b,3)}function gb(a,b){return ga(a,b)+"000"}function gc(a,b){return fq(a.getUTCMonth()+1,b,2)}function gd(a,b){return fq(a.getUTCMinutes(),b,2)}function ge(a,b){return fq(a.getUTCSeconds(),b,2)}function gf(a){var b=a.getUTCDay();return 0===b?7:b}function gg(a,b){return fq(e3.count(fd(a)-1,a),b,2)}function gh(a){var b=a.getUTCDay();return b>=4||0===b?e7(a):e7.ceil(a)}function gi(a,b){return a=gh(a),fq(e7.count(fd(a),a)+(4===fd(a).getUTCDay()),b,2)}function gj(a){return a.getUTCDay()}function gk(a,b){return fq(e4.count(fd(a)-1,a),b,2)}function gl(a,b){return fq(a.getUTCFullYear()%100,b,2)}function gm(a,b){return fq((a=gh(a)).getUTCFullYear()%100,b,2)}function gn(a,b){return fq(a.getUTCFullYear()%1e4,b,4)}function go(a,b){var c=a.getUTCDay();return fq((a=c>=4||0===c?e7(a):e7.ceil(a)).getUTCFullYear()%1e4,b,4)}function gp(){return"+0000"}function gq(){return"%"}function gr(a){return+a}function gs(a){return Math.floor(a/1e3)}function gt(a){return new Date(a)}function gu(a){return a instanceof Date?+a:+new Date(+a)}function gv(a,b,c,d,e,f,g,h,i,j){var k=eb(),l=k.invert,m=k.domain,n=j(".%L"),o=j(":%S"),p=j("%I:%M"),q=j("%I %p"),r=j("%a %d"),s=j("%b %d"),t=j("%B"),u=j("%Y");function v(a){return(i(a)<a?n:h(a)<a?o:g(a)<a?p:f(a)<a?q:d(a)<a?e(a)<a?r:s:c(a)<a?t:u)(a)}return k.invert=function(a){return new Date(l(a))},k.domain=function(a){return arguments.length?m(Array.from(a,gu)):m().map(gt)},k.ticks=function(b){var c=m();return a(c[0],c[c.length-1],null==b?10:b)},k.tickFormat=function(a,b){return null==b?v:j(b)},k.nice=function(a){var c=m();return a&&"function"==typeof a.range||(a=b(c[0],c[c.length-1],null==a?10:a)),a?m(eo(c,a)):k},k.copy=function(){return d9(k,gv(a,b,c,d,e,f,g,h,i,j))},k}function gw(){return c1.apply(gv(fh,fi,fc,fa,eX,eT,eR,eP,eO,gS).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function gx(){return c1.apply(gv(ff,fg,fd,fb,e3,eU,eS,eQ,eO,gT).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function gy(){var a,b,c,d,e,f=0,g=1,h=d5,i=!1;function j(b){return null==b||isNaN(b*=1)?e:h(0===c?.5:(b=(d(b)-a)*c,i?Math.max(0,Math.min(1,b)):b))}function k(a){return function(b){var c,d;return arguments.length?([c,d]=b,h=a(c,d),j):[h(0),h(1)]}}return j.domain=function(e){return arguments.length?([f,g]=e,a=d(f*=1),b=d(g*=1),c=a===b?0:1/(b-a),j):[f,g]},j.clamp=function(a){return arguments.length?(i=!!a,j):i},j.interpolator=function(a){return arguments.length?(h=a,j):h},j.range=k(d1),j.rangeRound=k(d2),j.unknown=function(a){return arguments.length?(e=a,j):e},function(e){return d=e,a=e(f),b=e(g),c=a===b?0:1/(b-a),j}}function gz(a,b){return b.domain(a.domain()).interpolator(a.interpolator()).clamp(a.clamp()).unknown(a.unknown())}function gA(){var a=eC(gy());return a.copy=function(){return gz(a,gA()).exponent(a.exponent())},c2.apply(a,arguments)}function gB(){return gA.apply(null,arguments).exponent(.5)}function gC(){var a,b,c,d,e,f,g,h=0,i=.5,j=1,k=1,l=d5,m=!1;function n(a){return isNaN(a*=1)?g:(a=.5+((a=+f(a))-b)*(k*a<k*b?d:e),l(m?Math.max(0,Math.min(1,a)):a))}function o(a){return function(b){var c,d,e;return arguments.length?([c,d,e]=b,l=function(a,b){void 0===b&&(b=a,a=d1);for(var c=0,d=b.length-1,e=b[0],f=Array(d<0?0:d);c<d;)f[c]=a(e,e=b[++c]);return function(a){var b=Math.max(0,Math.min(d-1,Math.floor(a*=d)));return f[b](a-b)}}(a,[c,d,e]),n):[l(0),l(.5),l(1)]}}return n.domain=function(g){return arguments.length?([h,i,j]=g,a=f(h*=1),b=f(i*=1),c=f(j*=1),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n):[h,i,j]},n.clamp=function(a){return arguments.length?(m=!!a,n):m},n.interpolator=function(a){return arguments.length?(l=a,n):l},n.range=o(d1),n.rangeRound=o(d2),n.unknown=function(a){return arguments.length?(g=a,n):g},function(g){return f=g,a=g(h),b=g(i),c=g(j),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n}}function gD(){var a=eC(gC());return a.copy=function(){return gz(a,gD()).exponent(a.exponent())},c2.apply(a,arguments)}function gE(){return gD.apply(null,arguments).exponent(.5)}gS=(gR=function(a){var b=a.dateTime,c=a.date,d=a.time,e=a.periods,f=a.days,g=a.shortDays,h=a.months,i=a.shortMonths,j=fs(e),k=ft(e),l=fs(f),m=ft(f),n=fs(g),o=ft(g),p=fs(h),q=ft(h),r=fs(i),s=ft(i),t={a:function(a){return g[a.getDay()]},A:function(a){return f[a.getDay()]},b:function(a){return i[a.getMonth()]},B:function(a){return h[a.getMonth()]},c:null,d:fO,e:fO,f:fT,g:f2,G:f4,H:fP,I:fQ,j:fR,L:fS,m:fU,M:fV,p:function(a){return e[+(a.getHours()>=12)]},q:function(a){return 1+~~(a.getMonth()/3)},Q:gr,s:gs,S:fW,u:fX,U:fY,V:f$,w:f_,W:f0,x:null,X:null,y:f1,Y:f3,Z:f5,"%":gq},u={a:function(a){return g[a.getUTCDay()]},A:function(a){return f[a.getUTCDay()]},b:function(a){return i[a.getUTCMonth()]},B:function(a){return h[a.getUTCMonth()]},c:null,d:f6,e:f6,f:gb,g:gm,G:go,H:f7,I:f8,j:f9,L:ga,m:gc,M:gd,p:function(a){return e[+(a.getUTCHours()>=12)]},q:function(a){return 1+~~(a.getUTCMonth()/3)},Q:gr,s:gs,S:ge,u:gf,U:gg,V:gi,w:gj,W:gk,x:null,X:null,y:gl,Y:gn,Z:gp,"%":gq},v={a:function(a,b,c){var d=n.exec(b.slice(c));return d?(a.w=o.get(d[0].toLowerCase()),c+d[0].length):-1},A:function(a,b,c){var d=l.exec(b.slice(c));return d?(a.w=m.get(d[0].toLowerCase()),c+d[0].length):-1},b:function(a,b,c){var d=r.exec(b.slice(c));return d?(a.m=s.get(d[0].toLowerCase()),c+d[0].length):-1},B:function(a,b,c){var d=p.exec(b.slice(c));return d?(a.m=q.get(d[0].toLowerCase()),c+d[0].length):-1},c:function(a,c,d){return y(a,b,c,d)},d:fE,e:fE,f:fK,g:fA,G:fz,H:fG,I:fG,j:fF,L:fJ,m:fD,M:fH,p:function(a,b,c){var d=j.exec(b.slice(c));return d?(a.p=k.get(d[0].toLowerCase()),c+d[0].length):-1},q:fC,Q:fM,s:fN,S:fI,u:fv,U:fw,V:fx,w:fu,W:fy,x:function(a,b,d){return y(a,c,b,d)},X:function(a,b,c){return y(a,d,b,c)},y:fA,Y:fz,Z:fB,"%":fL};function w(a,b){return function(c){var d,e,f,g=[],h=-1,i=0,j=a.length;for(c instanceof Date||(c=new Date(+c));++h<j;)37===a.charCodeAt(h)&&(g.push(a.slice(i,h)),null!=(e=fm[d=a.charAt(++h)])?d=a.charAt(++h):e="e"===d?" ":"0",(f=b[d])&&(d=f(c,e)),g.push(d),i=h+1);return g.push(a.slice(i,h)),g.join("")}}function x(a,b){return function(c){var d,e,f=fl(1900,void 0,1);if(y(f,a,c+="",0)!=c.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(1e3*f.s+("L"in f?f.L:0));if(!b||"Z"in f||(f.Z=0),"p"in f&&(f.H=f.H%12+12*f.p),void 0===f.m&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(d=(e=(d=fk(fl(f.y,0,1))).getUTCDay())>4||0===e?e4.ceil(d):e4(d),d=eU.offset(d,(f.V-1)*7),f.y=d.getUTCFullYear(),f.m=d.getUTCMonth(),f.d=d.getUTCDate()+(f.w+6)%7):(d=(e=(d=fj(fl(f.y,0,1))).getDay())>4||0===e?eY.ceil(d):eY(d),d=eT.offset(d,(f.V-1)*7),f.y=d.getFullYear(),f.m=d.getMonth(),f.d=d.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:+("W"in f)),e="Z"in f?fk(fl(f.y,0,1)).getUTCDay():fj(fl(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+7*f.W-(e+5)%7:f.w+7*f.U-(e+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,fk(f)):fj(f)}}function y(a,b,c,d){for(var e,f,g=0,h=b.length,i=c.length;g<h;){if(d>=i)return -1;if(37===(e=b.charCodeAt(g++))){if(!(f=v[(e=b.charAt(g++))in fm?b.charAt(g++):e])||(d=f(a,c,d))<0)return -1}else if(e!=c.charCodeAt(d++))return -1}return d}return t.x=w(c,t),t.X=w(d,t),t.c=w(b,t),u.x=w(c,u),u.X=w(d,u),u.c=w(b,u),{format:function(a){var b=w(a+="",t);return b.toString=function(){return a},b},parse:function(a){var b=x(a+="",!1);return b.toString=function(){return a},b},utcFormat:function(a){var b=w(a+="",u);return b.toString=function(){return a},b},utcParse:function(a){var b=x(a+="",!0);return b.toString=function(){return a},b}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,gR.parse,gT=gR.utcFormat,gR.utcParse;var gF=a=>a.chartData,gG=b2([gF],a=>{var b=null!=a.chartData?a.chartData.length-1:0;return{chartData:a.chartData,computedData:a.computedData,dataEndIndex:b,dataStartIndex:0}}),gH=(a,b,c,d)=>d?gG(a):gF(a);function gI(a){return Number.isFinite(a)}function gJ(a){return"number"==typeof a&&a>0&&Number.isFinite(a)}function gK(a){if(Array.isArray(a)&&2===a.length){var[b,c]=a;if(gI(b)&&gI(c))return!0}return!1}function gL(a,b,c){return c?a:[Math.min(a[0],b[0]),Math.max(a[1],b[1])]}var gM,gN,gO,gP,gQ,gR,gS,gT,gU,gV,gW=!0,gX="[DecimalError] ",gY=gX+"Invalid argument: ",gZ=gX+"Exponent out of range: ",g$=Math.floor,g_=Math.pow,g0=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,g1=g$(1286742750677284.5),g2={};function g3(a,b){var c,d,e,f,g,h,i,j,k=a.constructor,l=k.precision;if(!a.s||!b.s)return b.s||(b=new k(a)),gW?hd(b,l):b;if(i=a.d,j=b.d,g=a.e,e=b.e,i=i.slice(),f=g-e){for(f<0?(d=i,f=-f,h=j.length):(d=j,e=g,h=i.length),f>(h=(g=Math.ceil(l/7))>h?g+1:h+1)&&(f=h,d.length=1),d.reverse();f--;)d.push(0);d.reverse()}for((h=i.length)-(f=j.length)<0&&(f=h,d=j,j=i,i=d),c=0;f;)c=(i[--f]=i[f]+j[f]+c)/1e7|0,i[f]%=1e7;for(c&&(i.unshift(c),++e),h=i.length;0==i[--h];)i.pop();return b.d=i,b.e=e,gW?hd(b,l):b}function g4(a,b,c){if(a!==~~a||a<b||a>c)throw Error(gY+a)}function g5(a){var b,c,d,e=a.length-1,f="",g=a[0];if(e>0){for(f+=g,b=1;b<e;b++)(c=7-(d=a[b]+"").length)&&(f+=ha(c)),f+=d;(c=7-(d=(g=a[b])+"").length)&&(f+=ha(c))}else if(0===g)return"0";for(;g%10==0;)g/=10;return f+g}g2.absoluteValue=g2.abs=function(){var a=new this.constructor(this);return a.s&&(a.s=1),a},g2.comparedTo=g2.cmp=function(a){var b,c,d,e;if(a=new this.constructor(a),this.s!==a.s)return this.s||-a.s;if(this.e!==a.e)return this.e>a.e^this.s<0?1:-1;for(b=0,c=(d=this.d.length)<(e=a.d.length)?d:e;b<c;++b)if(this.d[b]!==a.d[b])return this.d[b]>a.d[b]^this.s<0?1:-1;return d===e?0:d>e^this.s<0?1:-1},g2.decimalPlaces=g2.dp=function(){var a=this.d.length-1,b=(a-this.e)*7;if(a=this.d[a])for(;a%10==0;a/=10)b--;return b<0?0:b},g2.dividedBy=g2.div=function(a){return g6(this,new this.constructor(a))},g2.dividedToIntegerBy=g2.idiv=function(a){var b=this.constructor;return hd(g6(this,new b(a),0,1),b.precision)},g2.equals=g2.eq=function(a){return!this.cmp(a)},g2.exponent=function(){return g8(this)},g2.greaterThan=g2.gt=function(a){return this.cmp(a)>0},g2.greaterThanOrEqualTo=g2.gte=function(a){return this.cmp(a)>=0},g2.isInteger=g2.isint=function(){return this.e>this.d.length-2},g2.isNegative=g2.isneg=function(){return this.s<0},g2.isPositive=g2.ispos=function(){return this.s>0},g2.isZero=function(){return 0===this.s},g2.lessThan=g2.lt=function(a){return 0>this.cmp(a)},g2.lessThanOrEqualTo=g2.lte=function(a){return 1>this.cmp(a)},g2.logarithm=g2.log=function(a){var b,c=this.constructor,d=c.precision,e=d+5;if(void 0===a)a=new c(10);else if((a=new c(a)).s<1||a.eq(gV))throw Error(gX+"NaN");if(this.s<1)throw Error(gX+(this.s?"NaN":"-Infinity"));return this.eq(gV)?new c(0):(gW=!1,b=g6(hb(this,e),hb(a,e),e),gW=!0,hd(b,d))},g2.minus=g2.sub=function(a){return a=new this.constructor(a),this.s==a.s?he(this,a):g3(this,(a.s=-a.s,a))},g2.modulo=g2.mod=function(a){var b,c=this.constructor,d=c.precision;if(!(a=new c(a)).s)throw Error(gX+"NaN");return this.s?(gW=!1,b=g6(this,a,0,1).times(a),gW=!0,this.minus(b)):hd(new c(this),d)},g2.naturalExponential=g2.exp=function(){return g7(this)},g2.naturalLogarithm=g2.ln=function(){return hb(this)},g2.negated=g2.neg=function(){var a=new this.constructor(this);return a.s=-a.s||0,a},g2.plus=g2.add=function(a){return a=new this.constructor(a),this.s==a.s?g3(this,a):he(this,(a.s=-a.s,a))},g2.precision=g2.sd=function(a){var b,c,d;if(void 0!==a&&!!a!==a&&1!==a&&0!==a)throw Error(gY+a);if(b=g8(this)+1,c=7*(d=this.d.length-1)+1,d=this.d[d]){for(;d%10==0;d/=10)c--;for(d=this.d[0];d>=10;d/=10)c++}return a&&b>c?b:c},g2.squareRoot=g2.sqrt=function(){var a,b,c,d,e,f,g,h=this.constructor;if(this.s<1){if(!this.s)return new h(0);throw Error(gX+"NaN")}for(a=g8(this),gW=!1,0==(e=Math.sqrt(+this))||e==1/0?(((b=g5(this.d)).length+a)%2==0&&(b+="0"),e=Math.sqrt(b),a=g$((a+1)/2)-(a<0||a%2),d=new h(b=e==1/0?"5e"+a:(b=e.toExponential()).slice(0,b.indexOf("e")+1)+a)):d=new h(e.toString()),e=g=(c=h.precision)+3;;)if(d=(f=d).plus(g6(this,f,g+2)).times(.5),g5(f.d).slice(0,g)===(b=g5(d.d)).slice(0,g)){if(b=b.slice(g-3,g+1),e==g&&"4999"==b){if(hd(f,c+1,0),f.times(f).eq(this)){d=f;break}}else if("9999"!=b)break;g+=4}return gW=!0,hd(d,c)},g2.times=g2.mul=function(a){var b,c,d,e,f,g,h,i,j,k=this.constructor,l=this.d,m=(a=new k(a)).d;if(!this.s||!a.s)return new k(0);for(a.s*=this.s,c=this.e+a.e,(i=l.length)<(j=m.length)&&(f=l,l=m,m=f,g=i,i=j,j=g),f=[],d=g=i+j;d--;)f.push(0);for(d=j;--d>=0;){for(b=0,e=i+d;e>d;)h=f[e]+m[d]*l[e-d-1]+b,f[e--]=h%1e7|0,b=h/1e7|0;f[e]=(f[e]+b)%1e7|0}for(;!f[--g];)f.pop();return b?++c:f.shift(),a.d=f,a.e=c,gW?hd(a,k.precision):a},g2.toDecimalPlaces=g2.todp=function(a,b){var c=this,d=c.constructor;return(c=new d(c),void 0===a)?c:(g4(a,0,1e9),void 0===b?b=d.rounding:g4(b,0,8),hd(c,a+g8(c)+1,b))},g2.toExponential=function(a,b){var c,d=this,e=d.constructor;return void 0===a?c=hf(d,!0):(g4(a,0,1e9),void 0===b?b=e.rounding:g4(b,0,8),c=hf(d=hd(new e(d),a+1,b),!0,a+1)),c},g2.toFixed=function(a,b){var c,d,e=this.constructor;return void 0===a?hf(this):(g4(a,0,1e9),void 0===b?b=e.rounding:g4(b,0,8),c=hf((d=hd(new e(this),a+g8(this)+1,b)).abs(),!1,a+g8(d)+1),this.isneg()&&!this.isZero()?"-"+c:c)},g2.toInteger=g2.toint=function(){var a=this.constructor;return hd(new a(this),g8(this)+1,a.rounding)},g2.toNumber=function(){return+this},g2.toPower=g2.pow=function(a){var b,c,d,e,f,g,h=this,i=h.constructor,j=+(a=new i(a));if(!a.s)return new i(gV);if(!(h=new i(h)).s){if(a.s<1)throw Error(gX+"Infinity");return h}if(h.eq(gV))return h;if(d=i.precision,a.eq(gV))return hd(h,d);if(g=(b=a.e)>=(c=a.d.length-1),f=h.s,g){if((c=j<0?-j:j)<=0x1fffffffffffff){for(e=new i(gV),b=Math.ceil(d/7+4),gW=!1;c%2&&hg((e=e.times(h)).d,b),0!==(c=g$(c/2));)hg((h=h.times(h)).d,b);return gW=!0,a.s<0?new i(gV).div(e):hd(e,d)}}else if(f<0)throw Error(gX+"NaN");return f=f<0&&1&a.d[Math.max(b,c)]?-1:1,h.s=1,gW=!1,e=a.times(hb(h,d+12)),gW=!0,(e=g7(e)).s=f,e},g2.toPrecision=function(a,b){var c,d,e=this,f=e.constructor;return void 0===a?(c=g8(e),d=hf(e,c<=f.toExpNeg||c>=f.toExpPos)):(g4(a,1,1e9),void 0===b?b=f.rounding:g4(b,0,8),c=g8(e=hd(new f(e),a,b)),d=hf(e,a<=c||c<=f.toExpNeg,a)),d},g2.toSignificantDigits=g2.tosd=function(a,b){var c=this.constructor;return void 0===a?(a=c.precision,b=c.rounding):(g4(a,1,1e9),void 0===b?b=c.rounding:g4(b,0,8)),hd(new c(this),a,b)},g2.toString=g2.valueOf=g2.val=g2.toJSON=g2[Symbol.for("nodejs.util.inspect.custom")]=function(){var a=g8(this),b=this.constructor;return hf(this,a<=b.toExpNeg||a>=b.toExpPos)};var g6=function(){function a(a,b){var c,d=0,e=a.length;for(a=a.slice();e--;)c=a[e]*b+d,a[e]=c%1e7|0,d=c/1e7|0;return d&&a.unshift(d),a}function b(a,b,c,d){var e,f;if(c!=d)f=c>d?1:-1;else for(e=f=0;e<c;e++)if(a[e]!=b[e]){f=a[e]>b[e]?1:-1;break}return f}function c(a,b,c){for(var d=0;c--;)a[c]-=d,d=+(a[c]<b[c]),a[c]=1e7*d+a[c]-b[c];for(;!a[0]&&a.length>1;)a.shift()}return function(d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=d.constructor,A=d.s==e.s?1:-1,B=d.d,C=e.d;if(!d.s)return new z(d);if(!e.s)throw Error(gX+"Division by zero");for(j=0,i=d.e-e.e,x=C.length,v=B.length,o=(n=new z(A)).d=[];C[j]==(B[j]||0);)++j;if(C[j]>(B[j]||0)&&--i,(s=null==f?f=z.precision:g?f+(g8(d)-g8(e))+1:f)<0)return new z(0);if(s=s/7+2|0,j=0,1==x)for(k=0,C=C[0],s++;(j<v||k)&&s--;j++)t=1e7*k+(B[j]||0),o[j]=t/C|0,k=t%C|0;else{for((k=1e7/(C[0]+1)|0)>1&&(C=a(C,k),B=a(B,k),x=C.length,v=B.length),u=x,q=(p=B.slice(0,x)).length;q<x;)p[q++]=0;(y=C.slice()).unshift(0),w=C[0],C[1]>=1e7/2&&++w;do k=0,(h=b(C,p,x,q))<0?(r=p[0],x!=q&&(r=1e7*r+(p[1]||0)),(k=r/w|0)>1?(k>=1e7&&(k=1e7-1),m=(l=a(C,k)).length,q=p.length,1==(h=b(l,p,m,q))&&(k--,c(l,x<m?y:C,m))):(0==k&&(h=k=1),l=C.slice()),(m=l.length)<q&&l.unshift(0),c(p,l,q),-1==h&&(q=p.length,(h=b(C,p,x,q))<1&&(k++,c(p,x<q?y:C,q))),q=p.length):0===h&&(k++,p=[0]),o[j++]=k,h&&p[0]?p[q++]=B[u]||0:(p=[B[u]],q=1);while((u++<v||void 0!==p[0])&&s--)}return o[0]||o.shift(),n.e=i,hd(n,g?f+g8(n)+1:f)}}();function g7(a,b){var c,d,e,f,g,h=0,i=0,j=a.constructor,k=j.precision;if(g8(a)>16)throw Error(gZ+g8(a));if(!a.s)return new j(gV);for(null==b?(gW=!1,g=k):g=b,f=new j(.03125);a.abs().gte(.1);)a=a.times(f),i+=5;for(g+=Math.log(g_(2,i))/Math.LN10*2+5|0,c=d=e=new j(gV),j.precision=g;;){if(d=hd(d.times(a),g),c=c.times(++h),g5((f=e.plus(g6(d,c,g))).d).slice(0,g)===g5(e.d).slice(0,g)){for(;i--;)e=hd(e.times(e),g);return j.precision=k,null==b?(gW=!0,hd(e,k)):e}e=f}}function g8(a){for(var b=7*a.e,c=a.d[0];c>=10;c/=10)b++;return b}function g9(a,b,c){if(b>a.LN10.sd())throw gW=!0,c&&(a.precision=c),Error(gX+"LN10 precision limit exceeded");return hd(new a(a.LN10),b)}function ha(a){for(var b="";a--;)b+="0";return b}function hb(a,b){var c,d,e,f,g,h,i,j,k,l=1,m=a,n=m.d,o=m.constructor,p=o.precision;if(m.s<1)throw Error(gX+(m.s?"NaN":"-Infinity"));if(m.eq(gV))return new o(0);if(null==b?(gW=!1,j=p):j=b,m.eq(10))return null==b&&(gW=!0),g9(o,j);if(o.precision=j+=10,d=(c=g5(n)).charAt(0),!(15e14>Math.abs(f=g8(m))))return i=g9(o,j+2,p).times(f+""),m=hb(new o(d+"."+c.slice(1)),j-10).plus(i),o.precision=p,null==b?(gW=!0,hd(m,p)):m;for(;d<7&&1!=d||1==d&&c.charAt(1)>3;)d=(c=g5((m=m.times(a)).d)).charAt(0),l++;for(f=g8(m),d>1?(m=new o("0."+c),f++):m=new o(d+"."+c.slice(1)),h=g=m=g6(m.minus(gV),m.plus(gV),j),k=hd(m.times(m),j),e=3;;){if(g=hd(g.times(k),j),g5((i=h.plus(g6(g,new o(e),j))).d).slice(0,j)===g5(h.d).slice(0,j))return h=h.times(2),0!==f&&(h=h.plus(g9(o,j+2,p).times(f+""))),h=g6(h,new o(l),j),o.precision=p,null==b?(gW=!0,hd(h,p)):h;h=i,e+=2}}function hc(a,b){var c,d,e;for((c=b.indexOf("."))>-1&&(b=b.replace(".","")),(d=b.search(/e/i))>0?(c<0&&(c=d),c+=+b.slice(d+1),b=b.substring(0,d)):c<0&&(c=b.length),d=0;48===b.charCodeAt(d);)++d;for(e=b.length;48===b.charCodeAt(e-1);)--e;if(b=b.slice(d,e)){if(e-=d,a.e=g$((c=c-d-1)/7),a.d=[],d=(c+1)%7,c<0&&(d+=7),d<e){for(d&&a.d.push(+b.slice(0,d)),e-=7;d<e;)a.d.push(+b.slice(d,d+=7));d=7-(b=b.slice(d)).length}else d-=e;for(;d--;)b+="0";if(a.d.push(+b),gW&&(a.e>g1||a.e<-g1))throw Error(gZ+c)}else a.s=0,a.e=0,a.d=[0];return a}function hd(a,b,c){var d,e,f,g,h,i,j,k,l=a.d;for(g=1,f=l[0];f>=10;f/=10)g++;if((d=b-g)<0)d+=7,e=b,j=l[k=0];else{if((k=Math.ceil((d+1)/7))>=(f=l.length))return a;for(g=1,j=f=l[k];f>=10;f/=10)g++;d%=7,e=d-7+g}if(void 0!==c&&(h=j/(f=g_(10,g-e-1))%10|0,i=b<0||void 0!==l[k+1]||j%f,i=c<4?(h||i)&&(0==c||c==(a.s<0?3:2)):h>5||5==h&&(4==c||i||6==c&&(d>0?e>0?j/g_(10,g-e):0:l[k-1])%10&1||c==(a.s<0?8:7))),b<1||!l[0])return i?(f=g8(a),l.length=1,b=b-f-1,l[0]=g_(10,(7-b%7)%7),a.e=g$(-b/7)||0):(l.length=1,l[0]=a.e=a.s=0),a;if(0==d?(l.length=k,f=1,k--):(l.length=k+1,f=g_(10,7-d),l[k]=e>0?(j/g_(10,g-e)%g_(10,e)|0)*f:0),i)for(;;)if(0==k){1e7==(l[0]+=f)&&(l[0]=1,++a.e);break}else{if(l[k]+=f,1e7!=l[k])break;l[k--]=0,f=1}for(d=l.length;0===l[--d];)l.pop();if(gW&&(a.e>g1||a.e<-g1))throw Error(gZ+g8(a));return a}function he(a,b){var c,d,e,f,g,h,i,j,k,l,m=a.constructor,n=m.precision;if(!a.s||!b.s)return b.s?b.s=-b.s:b=new m(a),gW?hd(b,n):b;if(i=a.d,l=b.d,d=b.e,j=a.e,i=i.slice(),g=j-d){for((k=g<0)?(c=i,g=-g,h=l.length):(c=l,d=j,h=i.length),g>(e=Math.max(Math.ceil(n/7),h)+2)&&(g=e,c.length=1),c.reverse(),e=g;e--;)c.push(0);c.reverse()}else{for((k=(e=i.length)<(h=l.length))&&(h=e),e=0;e<h;e++)if(i[e]!=l[e]){k=i[e]<l[e];break}g=0}for(k&&(c=i,i=l,l=c,b.s=-b.s),h=i.length,e=l.length-h;e>0;--e)i[h++]=0;for(e=l.length;e>g;){if(i[--e]<l[e]){for(f=e;f&&0===i[--f];)i[f]=1e7-1;--i[f],i[e]+=1e7}i[e]-=l[e]}for(;0===i[--h];)i.pop();for(;0===i[0];i.shift())--d;return i[0]?(b.d=i,b.e=d,gW?hd(b,n):b):new m(0)}function hf(a,b,c){var d,e=g8(a),f=g5(a.d),g=f.length;return b?(c&&(d=c-g)>0?f=f.charAt(0)+"."+f.slice(1)+ha(d):g>1&&(f=f.charAt(0)+"."+f.slice(1)),f=f+(e<0?"e":"e+")+e):e<0?(f="0."+ha(-e-1)+f,c&&(d=c-g)>0&&(f+=ha(d))):e>=g?(f+=ha(e+1-g),c&&(d=c-e-1)>0&&(f=f+"."+ha(d))):((d=e+1)<g&&(f=f.slice(0,d)+"."+f.slice(d)),c&&(d=c-g)>0&&(e+1===g&&(f+="."),f+=ha(d))),a.s<0?"-"+f:f}function hg(a,b){if(a.length>b)return a.length=b,!0}function hh(a){if(!a||"object"!=typeof a)throw Error(gX+"Object expected");var b,c,d,e=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(b=0;b<e.length;b+=3)if(void 0!==(d=a[c=e[b]]))if(g$(d)===d&&d>=e[b+1]&&d<=e[b+2])this[c]=d;else throw Error(gY+c+": "+d);if(void 0!==(d=a[c="LN10"]))if(d==Math.LN10)this[c]=new this(d);else throw Error(gY+c+": "+d);return this}var gU=function a(b){var c,d,e;function f(a){if(!(this instanceof f))return new f(a);if(this.constructor=f,a instanceof f){this.s=a.s,this.e=a.e,this.d=(a=a.d)?a.slice():a;return}if("number"==typeof a){if(0*a!=0)throw Error(gY+a);if(a>0)this.s=1;else if(a<0)a=-a,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(a===~~a&&a<1e7){this.e=0,this.d=[a];return}return hc(this,a.toString())}if("string"!=typeof a)throw Error(gY+a);if(45===a.charCodeAt(0)?(a=a.slice(1),this.s=-1):this.s=1,g0.test(a))hc(this,a);else throw Error(gY+a)}if(f.prototype=g2,f.ROUND_UP=0,f.ROUND_DOWN=1,f.ROUND_CEIL=2,f.ROUND_FLOOR=3,f.ROUND_HALF_UP=4,f.ROUND_HALF_DOWN=5,f.ROUND_HALF_EVEN=6,f.ROUND_HALF_CEIL=7,f.ROUND_HALF_FLOOR=8,f.clone=a,f.config=f.set=hh,void 0===b&&(b={}),b)for(c=0,e=["precision","rounding","toExpNeg","toExpPos","LN10"];c<e.length;)b.hasOwnProperty(d=e[c++])||(b[d]=this[d]);return f.config(b),f}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});gV=new gU(1);let hi=gU;var hj=a=>a,hk={},hl=a=>function b(){let c;return 0==arguments.length||1==arguments.length&&(c=arguments.length<=0?void 0:arguments[0],c===hk)?b:a(...arguments)},hm=(a,b)=>1===a?b:hl(function(){for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];var f=d.filter(a=>a!==hk).length;return f>=a?b(...d):hm(a-f,hl(function(){for(var a=arguments.length,c=Array(a),e=0;e<a;e++)c[e]=arguments[e];return b(...d.map(a=>a===hk?c.shift():a),...c)}))}),hn=a=>hm(a.length,a),ho=(a,b)=>{for(var c=[],d=a;d<b;++d)c[d-a]=d;return c},hp=hn((a,b)=>Array.isArray(b)?b.map(a):Object.keys(b).map(a=>b[a]).map(a)),hq=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];if(!b.length)return hj;var d=b.reverse(),e=d[0],f=d.slice(1);return function(){return f.reduce((a,b)=>b(a),e(...arguments))}},hr=a=>Array.isArray(a)?a.reverse():a.split("").reverse().join(""),hs=a=>{var b=null,c=null;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return b&&e.every((a,c)=>{var d;return a===(null==(d=b)?void 0:d[c])})?c:(b=e,c=a(...e))}};function ht(a){return 0===a?1:Math.floor(new hi(a).abs().log(10).toNumber())+1}function hu(a,b,c){for(var d=new hi(a),e=0,f=[];d.lt(b)&&e<1e5;)f.push(d.toNumber()),d=d.add(c),e++;return f}hn((a,b,c)=>{var d=+a;return d+c*(b-d)}),hn((a,b,c)=>{var d=b-a;return(c-a)/(d=d||1/0)}),hn((a,b,c)=>{var d=b-a;return Math.max(0,Math.min(1,(c-a)/(d=d||1/0)))});var hv=a=>{var[b,c]=a,[d,e]=[b,c];return b>c&&([d,e]=[c,b]),[d,e]},hw=(a,b,c)=>{if(a.lte(0))return new hi(0);var d=ht(a.toNumber()),e=new hi(10).pow(d),f=a.div(e),g=1!==d?.05:.1,h=new hi(Math.ceil(f.div(g).toNumber())).add(c).mul(g).mul(e);return new hi(b?h.toNumber():Math.ceil(h.toNumber()))},hx=function(a,b,c,d){var e,f=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((b-a)/(c-1)))return{step:new hi(0),tickMin:new hi(0),tickMax:new hi(0)};var g=hw(new hi(b).sub(a).div(c-1),d,f),h=Math.ceil((e=a<=0&&b>=0?new hi(0):(e=new hi(a).add(b).div(2)).sub(new hi(e).mod(g))).sub(a).div(g).toNumber()),i=Math.ceil(new hi(b).sub(e).div(g).toNumber()),j=h+i+1;return j>c?hx(a,b,c,d,f+1):(j<c&&(i=b>0?i+(c-j):i,h=b>0?h:h+(c-j)),{step:g,tickMin:e.sub(new hi(h).mul(g)),tickMax:e.add(new hi(i).mul(g))})},hy=hs(function(a){var[b,c]=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],f=Math.max(d,2),[g,h]=hv([b,c]);if(g===-1/0||h===1/0){var i=h===1/0?[g,...ho(0,d-1).map(()=>1/0)]:[...ho(0,d-1).map(()=>-1/0),h];return b>c?hr(i):i}if(g===h){var j=new hi(1),k=new hi(g);if(!k.isint()&&e){var l=Math.abs(g);l<1?(j=new hi(10).pow(ht(g)-1),k=new hi(Math.floor(k.div(j).toNumber())).mul(j)):l>1&&(k=new hi(Math.floor(g)))}else 0===g?k=new hi(Math.floor((d-1)/2)):e||(k=new hi(Math.floor(g)));var m=Math.floor((d-1)/2);return hq(hp(a=>k.add(new hi(a-m).mul(j)).toNumber()),ho)(0,d)}var{step:n,tickMin:o,tickMax:p}=hx(g,h,f,e,0),q=hu(o,p.add(new hi(.1).mul(n)),n);return b>c?hr(q):q}),hz=hs(function(a,b){var[c,d]=a,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[f,g]=hv([c,d]);if(f===-1/0||g===1/0)return[c,d];if(f===g)return[f];var h=Math.max(b,2),i=hw(new hi(g).sub(f).div(h-1),e,0),j=[...hu(new hi(f),new hi(g),i),g];return!1===e&&(j=j.map(a=>Math.round(a))),c>d?hr(j):j}),hA=a=>a.rootProps.maxBarSize,hB=a=>a.rootProps.barCategoryGap,hC=a=>a.rootProps.stackOffset,hD=a=>a.options.chartName,hE=a=>a.rootProps.syncId,hF=a=>a.rootProps.syncMethod,hG=a=>a.options.eventEmitter,hH={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},hI={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},hJ=(a,b)=>{if(a&&b)return null!=a&&a.reversed?[b[1],b[0]]:b},hK={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:hH.angleAxisId,includeHidden:!1,name:void 0,reversed:hH.reversed,scale:hH.scale,tick:hH.tick,tickCount:void 0,ticks:void 0,type:hH.type,unit:void 0},hL={allowDataOverflow:hI.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hI.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hI.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hI.scale,tick:hI.tick,tickCount:hI.tickCount,ticks:void 0,type:hI.type,unit:void 0},hM={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:hH.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hH.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hH.scale,tick:hH.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},hN={allowDataOverflow:hI.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hI.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hI.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hI.scale,tick:hI.tick,tickCount:hI.tickCount,ticks:void 0,type:"category",unit:void 0},hO=(a,b)=>null!=a.polarAxis.angleAxis[b]?a.polarAxis.angleAxis[b]:"radial"===a.layout.layoutType?hM:hK,hP=(a,b)=>null!=a.polarAxis.radiusAxis[b]?a.polarAxis.radiusAxis[b]:"radial"===a.layout.layoutType?hN:hL,hQ=a=>a.polarOptions,hR=b2([cD,cE,cN],cp),hS=b2([hQ,hR],(a,b)=>{if(null!=a)return s(a.innerRadius,b,0)}),hT=b2([hQ,hR],(a,b)=>{if(null!=a)return s(a.outerRadius,b,.8*b)}),hU=b2([hQ],a=>{if(null==a)return[0,0];var{startAngle:b,endAngle:c}=a;return[b,c]});b2([hO,hU],hJ);var hV=b2([hR,hS,hT],(a,b,c)=>{if(null!=a&&null!=b&&null!=c)return[b,c]});b2([hP,hV],hJ);var hW=b2([cZ,hQ,hS,hT,cD,cE],(a,b,c,d,e,f)=>{if(("centric"===a||"radial"===a)&&null!=b&&null!=c&&null!=d){var{cx:g,cy:h,startAngle:i,endAngle:j}=b;return{cx:s(g,e,e/2),cy:s(h,f,f/2),innerRadius:c,outerRadius:d,startAngle:i,endAngle:j,clockWise:!1}}}),hX=(a,b)=>b,hY=(a,b,c)=>c;function hZ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function h$(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?hZ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):hZ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var h_=[0,"auto"],h0={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},h1=(a,b)=>{var c=a.cartesianAxis.xAxis[b];return null==c?h0:c},h2={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:h_,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},h3=(a,b)=>{var c=a.cartesianAxis.yAxis[b];return null==c?h2:c},h4={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},h5=(a,b)=>{var c=a.cartesianAxis.zAxis[b];return null==c?h4:c},h6=(a,b,c)=>{switch(b){case"xAxis":return h1(a,c);case"yAxis":return h3(a,c);case"zAxis":return h5(a,c);case"angleAxis":return hO(a,c);case"radiusAxis":return hP(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},h7=(a,b,c)=>{switch(b){case"xAxis":return h1(a,c);case"yAxis":return h3(a,c);case"angleAxis":return hO(a,c);case"radiusAxis":return hP(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},h8=a=>a.graphicalItems.countOfBars>0;function h9(a,b){return c=>{switch(a){case"xAxis":return"xAxisId"in c&&c.xAxisId===b;case"yAxis":return"yAxisId"in c&&c.yAxisId===b;case"zAxis":return"zAxisId"in c&&c.zAxisId===b;case"angleAxis":return"angleAxisId"in c&&c.angleAxisId===b;case"radiusAxis":return"radiusAxisId"in c&&c.radiusAxisId===b;default:return!1}}}var ia=a=>a.graphicalItems.cartesianItems,ib=b2([hX,hY],h9),ic=(a,b,c)=>a.filter(c).filter(a=>(null==b?void 0:b.includeHidden)===!0||!a.hide),id=b2([ia,h6,ib],ic),ie=a=>a.filter(a=>void 0===a.stackId),ig=b2([id],ie),ih=a=>a.map(a=>a.data).filter(Boolean).flat(1),ii=b2([id],ih),ij=(a,b)=>{var{chartData:c=[],dataStartIndex:d,dataEndIndex:e}=b;return a.length>0?a:c.slice(d,e+1)},ik=b2([ii,gH],ij),il=(a,b,c)=>(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cs(a,b.dataKey)})):c.length>0?c.map(a=>a.dataKey).flatMap(b=>a.map(a=>({value:cs(a,b)}))):a.map(a=>({value:a})),im=b2([ik,h6,id],il);function io(a,b){switch(a){case"xAxis":return"x"===b.direction;case"yAxis":return"y"===b.direction;default:return!1}}function ip(a){return a.filter(a=>p(a)||a instanceof Date).map(Number).filter(a=>!1===m(a))}var iq=(a,b,c)=>Object.fromEntries(Object.entries(b.reduce((a,b)=>(null==b.stackId||(null==a[b.stackId]&&(a[b.stackId]=[]),a[b.stackId].push(b)),a),{})).map(b=>{var[d,e]=b;return[d,{stackedData:((a,b,c)=>{var d=cw[c];return(function(){var a=ch([]),b=ci,c=cf,d=cj;function e(e){var f,g,h=Array.from(a.apply(this,arguments),ck),i=h.length,j=-1;for(let a of e)for(f=0,++j;f<i;++f)(h[f][j]=[0,+d(a,h[f].key,j,e)]).data=a;for(f=0,g=cg(b(h));f<i;++f)h[g[f]].index=f;return c(h,g),h}return e.keys=function(b){return arguments.length?(a="function"==typeof b?b:ch(Array.from(b)),e):a},e.value=function(a){return arguments.length?(d="function"==typeof a?a:ch(+a),e):d},e.order=function(a){return arguments.length?(b=null==a?ci:"function"==typeof a?a:ch(Array.from(a)),e):b},e.offset=function(a){return arguments.length?(c=null==a?cf:a,e):c},e})().keys(b).value((a,b)=>+cs(a,b,0)).order(ci).offset(d)(a)})(a,e.map(a=>a.dataKey),c),graphicalItems:e}]})),ir=b2([ik,id,hC],iq),is=(a,b,c)=>{var{dataStartIndex:d,dataEndIndex:e}=b;if("zAxis"!==c){var f=((a,b,c)=>{if(null!=a)return(a=>[a[0]===1/0?0:a[0],a[1]===-1/0?0:a[1]])(Object.keys(a).reduce((d,e)=>{var{stackedData:f}=a[e],g=f.reduce((a,d)=>{var e=(a=>{var b=a.flat(2).filter(o);return[Math.min(...b),Math.max(...b)]})(d.slice(b,c+1));return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]},[1/0,-1/0]);return[Math.min(g[0],d[0]),Math.max(g[1],d[1])]},[1/0,-1/0]))})(a,d,e);if(null==f||0!==f[0]||0!==f[1])return f}},it=b2([ir,gF,hX],is),iu=(a,b,c,d)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var e,f,g=null==(e=c.errorBars)?void 0:e.filter(a=>io(d,a)),h=cs(a,null!=(f=b.dataKey)?f:c.dataKey);return{value:h,errorDomain:function(a,b,c){return!c||"number"!=typeof b||m(b)||!c.length?[]:ip(c.flatMap(c=>{var d,e,f=cs(a,c.dataKey);if(Array.isArray(f)?[d,e]=f:d=e=f,gI(d)&&gI(e))return[b-d,b+e]}))}(a,h,g)}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cs(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]})),iv=b2(ik,h6,ig,hX,iu);function iw(a){var{value:b}=a;if(p(b)||b instanceof Date)return b}var ix=a=>{var b;if(null==a||!("domain"in a))return h_;if(null!=a.domain)return a.domain;if(null!=a.ticks){if("number"===a.type){var c=ip(a.ticks);return[Math.min(...c),Math.max(...c)]}if("category"===a.type)return a.ticks.map(String)}return null!=(b=null==a?void 0:a.domain)?b:h_},iy=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];var d=b.filter(Boolean);if(0!==d.length){var e=d.flat();return[Math.min(...e),Math.max(...e)]}},iz=a=>a.referenceElements.dots,iA=(a,b,c)=>a.filter(a=>"extendDomain"===a.ifOverflow).filter(a=>"xAxis"===b?a.xAxisId===c:a.yAxisId===c),iB=b2([iz,hX,hY],iA),iC=a=>a.referenceElements.areas,iD=b2([iC,hX,hY],iA),iE=a=>a.referenceElements.lines,iF=b2([iE,hX,hY],iA),iG=(a,b)=>{var c=ip(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iH=b2(iB,hX,iG),iI=(a,b)=>{var c=ip(a.flatMap(a=>["xAxis"===b?a.x1:a.y1,"xAxis"===b?a.x2:a.y2]));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iJ=b2([iD,hX],iI),iK=(a,b)=>{var c=ip(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iL=b2(iF,hX,iK),iM=b2(iH,iL,iJ,(a,b,c)=>iy(a,c,b)),iN=b2([h6],ix),iO=(a,b,c,d,e)=>{var f=function(a,b){if(b&&"function"!=typeof a&&Array.isArray(a)&&2===a.length){var c,d,[e,f]=a;if(gI(e))c=e;else if("function"==typeof e)return;if(gI(f))d=f;else if("function"==typeof f)return;var g=[c,d];if(gK(g))return g}}(b,a.allowDataOverflow);return null!=f?f:function(a,b,c){if(c||null!=b){if("function"==typeof a&&null!=b)try{var d=a(b,c);if(gK(d))return gL(d,b,c)}catch(a){}if(Array.isArray(a)&&2===a.length){var e,f,[g,h]=a;if("auto"===g)null!=b&&(e=Math.min(...b));else if(o(g))e=g;else if("function"==typeof g)try{null!=b&&(e=g(null==b?void 0:b[0]))}catch(a){}else if("string"==typeof g&&cy.test(g)){var i=cy.exec(g);if(null==i||null==b)e=void 0;else{var j=+i[1];e=b[0]-j}}else e=null==b?void 0:b[0];if("auto"===h)null!=b&&(f=Math.max(...b));else if(o(h))f=h;else if("function"==typeof h)try{null!=b&&(f=h(null==b?void 0:b[1]))}catch(a){}else if("string"==typeof h&&cz.test(h)){var k=cz.exec(h);if(null==k||null==b)f=void 0;else{var l=+k[1];f=b[1]+l}}else f=null==b?void 0:b[1];var m=[e,f];if(gK(m))return null==b?m:gL(m,b,c)}}}(b,iy(c,e,(a=>{var b=ip(a.flatMap(a=>[a.value,a.errorDomain]).flat(1));if(0!==b.length)return[Math.min(...b),Math.max(...b)]})(d)),a.allowDataOverflow)},iP=b2([h6,iN,it,iv,iM],iO),iQ=[0,1],iR=(a,b,c,d,e,f,g)=>{if(null!=a&&null!=c&&0!==c.length){var{dataKey:h,type:i}=a,j=ct(b,f);return j&&null==h?c0()(0,c.length):"category"===i?((a,b,c)=>{var d=a.map(iw).filter(a=>null!=a);return c&&(null==b.dataKey||b.allowDuplicatedCategory&&t(d))?c0()(0,a.length):b.allowDuplicatedCategory?d:Array.from(new Set(d))})(d,a,j):"expand"===e?iQ:g}},iS=b2([h6,cZ,ik,im,hC,hX,iP],iR),iT=(a,b,c,e,f)=>{if(null!=a){var{scale:g,type:h}=a;if("auto"===g)return"radial"===b&&"radiusAxis"===f?"band":"radial"===b&&"angleAxis"===f?"linear":"category"===h&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!c)?"point":"category"===h?"band":"linear";if("string"==typeof g){var i="scale".concat(v(g));return i in d?i:"point"}}},iU=b2([h6,cZ,h8,hD,hX],iT);function iV(a,b,c,e){if(null!=c&&null!=e){if("function"==typeof a.scale)return a.scale.copy().domain(c).range(e);var f=function(a){if(null!=a){if(a in d)return d[a]();var b="scale".concat(v(a));if(b in d)return d[b]()}}(b);if(null!=f){var g=f.domain(c).range(e);return(a=>{var b=a.domain();if(b&&!(b.length<=2)){var c=b.length,d=a.range(),e=Math.min(d[0],d[1])-1e-4,f=Math.max(d[0],d[1])+1e-4,g=a(b[0]),h=a(b[c-1]);(g<e||g>f||h<e||h>f)&&a.domain([b[0],b[c-1]])}})(g),g}}}var iW=(a,b,c)=>{var d=ix(b);if("auto"===c||"linear"===c){if(null!=b&&b.tickCount&&Array.isArray(d)&&("auto"===d[0]||"auto"===d[1])&&gK(a))return hy(a,b.tickCount,b.allowDecimals);if(null!=b&&b.tickCount&&"number"===b.type&&gK(a))return hz(a,b.tickCount,b.allowDecimals)}},iX=b2([iS,h7,iU],iW),iY=(a,b,c,d)=>"angleAxis"!==d&&(null==a?void 0:a.type)==="number"&&gK(b)&&Array.isArray(c)&&c.length>0?[Math.min(b[0],c[0]),Math.max(b[1],c[c.length-1])]:b,iZ=b2([h6,iS,iX,hX],iY),i$=b2(im,h6,(a,b)=>{if(b&&"number"===b.type){var c=1/0,d=Array.from(ip(a.map(a=>a.value))).sort((a,b)=>a-b);if(d.length<2)return 1/0;var e=d[d.length-1]-d[0];if(0===e)return 1/0;for(var f=0;f<d.length-1;f++)c=Math.min(c,d[f+1]-d[f]);return c/e}}),i_=b2(i$,cZ,hB,cN,(a,b,c,d)=>d,(a,b,c,d,e)=>{if(!gI(a))return 0;var f="vertical"===b?d.height:d.width;if("gap"===e)return a*f/2;if("no-gap"===e){var g=s(c,a*f),h=a*f/2;return h-g-(h-g)/f*g}return 0}),i0=b2(h1,(a,b)=>{var c=h1(a,b);return null==c||"string"!=typeof c.padding?0:i_(a,"xAxis",b,c.padding)},(a,b)=>{if(null==a)return{left:0,right:0};var c,d,{padding:e}=a;return"string"==typeof e?{left:b,right:b}:{left:(null!=(c=e.left)?c:0)+b,right:(null!=(d=e.right)?d:0)+b}}),i1=b2(h3,(a,b)=>{var c=h3(a,b);return null==c||"string"!=typeof c.padding?0:i_(a,"yAxis",b,c.padding)},(a,b)=>{if(null==a)return{top:0,bottom:0};var c,d,{padding:e}=a;return"string"==typeof e?{top:b,bottom:b}:{top:(null!=(c=e.top)?c:0)+b,bottom:(null!=(d=e.bottom)?d:0)+b}}),i2=b2([cN,i0,cT,cS,(a,b,c)=>c],(a,b,c,d,e)=>{var{padding:f}=d;return e?[f.left,c.width-f.right]:[a.left+b.left,a.left+a.width-b.right]}),i3=b2([cN,cZ,i1,cT,cS,(a,b,c)=>c],(a,b,c,d,e,f)=>{var{padding:g}=e;return f?[d.height-g.bottom,g.top]:"horizontal"===b?[a.top+a.height-c.bottom,a.top+c.top]:[a.top+c.top,a.top+a.height-c.bottom]}),i4=(a,b,c,d)=>{var e;switch(b){case"xAxis":return i2(a,c,d);case"yAxis":return i3(a,c,d);case"zAxis":return null==(e=h5(a,c))?void 0:e.range;case"angleAxis":return hU(a);case"radiusAxis":return hV(a,c);default:return}},i5=b2([h6,i4],hJ),i6=b2([h6,iU,iZ,i5],iV);function i7(a,b){return a.id<b.id?-1:+(a.id>b.id)}b2(id,hX,(a,b)=>a.flatMap(a=>{var b;return null!=(b=a.errorBars)?b:[]}).filter(a=>io(b,a)));var i8=(a,b)=>b,i9=(a,b,c)=>c,ja=b2(cH,i8,i9,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(i7)),jb=b2(cI,i8,i9,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(i7)),jc=(a,b)=>({width:a.width,height:b.height}),jd=b2(cN,h1,jc),je=b2(cE,cN,ja,i8,i9,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=jc(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"top":return a.top;case"bottom":return c-a.bottom;default:return 0}})(b,d,a));var i="top"===d&&!e||"bottom"===d&&e;g[c.id]=f-Number(i)*h.height,f+=(i?-1:1)*h.height}),g}),jf=b2(cD,cN,jb,i8,i9,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=((a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height}))(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"left":return a.left;case"right":return c-a.right;default:return 0}})(b,d,a));var i="left"===d&&!e||"right"===d&&e;g[c.id]=f-Number(i)*h.width,f+=(i?-1:1)*h.width}),g}),jg=b2(cN,h3,(a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height})),jh=(a,b,c)=>{switch(b){case"xAxis":return jd(a,c).width;case"yAxis":return jg(a,c).height;default:return}},ji=(a,b,c,d)=>{if(null!=c){var{allowDuplicatedCategory:e,type:f,dataKey:g}=c,h=ct(a,d),i=b.map(a=>a.value);if(g&&h&&"category"===f&&e&&t(i))return i}},jj=b2([cZ,im,h6,hX],ji),jk=(a,b,c,d)=>{if(null!=c&&null!=c.dataKey){var{type:e,scale:f}=c;if(ct(a,d)&&("number"===e||"auto"!==f))return b.map(a=>a.value)}},jl=b2([cZ,im,h7,hX],jk),jm=b2([cZ,(a,b,c)=>{switch(b){case"xAxis":return h1(a,c);case"yAxis":return h3(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},iU,i6,jj,jl,i4,iX,hX],(a,b,c,d,e,f,g,h,i)=>{if(null==b)return null;var j=ct(a,i);return{angle:b.angle,interval:b.interval,minTickGap:b.minTickGap,orientation:b.orientation,tick:b.tick,tickCount:b.tickCount,tickFormatter:b.tickFormatter,ticks:b.ticks,type:b.type,unit:b.unit,axisType:i,categoricalDomain:f,duplicateDomain:e,isCategorical:j,niceTicks:h,range:g,realScaleType:c,scale:d}}),jn=b2([cZ,h7,iU,i6,iX,i4,jj,jl,hX],(a,b,c,d,e,f,g,h,i)=>{if(null!=b&&null!=d){var j=ct(a,i),{type:k,ticks:n,tickCount:o}=b,p="scaleBand"===c&&"function"==typeof d.bandwidth?d.bandwidth()/2:2,q="category"===k&&d.bandwidth?d.bandwidth()/p:0;q="angleAxis"===i&&null!=f&&f.length>=2?2*l(f[0]-f[1])*q:q;var r=n||e;return r?r.map((a,b)=>({index:b,coordinate:d(g?g.indexOf(a):a)+q,value:a,offset:q})).filter(a=>!m(a.coordinate)):j&&h?h.map((a,b)=>({coordinate:d(a)+q,value:a,index:b,offset:q})):d.ticks?d.ticks(o).map(a=>({coordinate:d(a)+q,value:a,offset:q})):d.domain().map((a,b)=>({coordinate:d(a)+q,value:g?g[a]:a,index:b,offset:q}))}}),jo=b2([cZ,h7,i6,i4,jj,jl,hX],(a,b,c,d,e,f,g)=>{if(null!=b&&null!=c&&null!=d&&d[0]!==d[1]){var h=ct(a,g),{tickCount:i}=b,j=0;return(j="angleAxis"===g&&(null==d?void 0:d.length)>=2?2*l(d[0]-d[1])*j:j,h&&f)?f.map((a,b)=>({coordinate:c(a)+j,value:a,index:b,offset:j})):c.ticks?c.ticks(i).map(a=>({coordinate:c(a)+j,value:a,offset:j})):c.domain().map((a,b)=>({coordinate:c(a)+j,value:e?e[a]:a,index:b,offset:j}))}}),jp=b2(h6,i6,(a,b)=>{if(null!=a&&null!=b)return h$(h$({},a),{},{scale:b})}),jq=b2([h6,iU,iS,i5],iV);b2((a,b,c)=>h5(a,c),jq,(a,b)=>{if(null!=a&&null!=b)return h$(h$({},a),{},{scale:b})});var jr=b2([cZ,cH,cI],(a,b,c)=>{switch(a){case"horizontal":return b.some(a=>a.reversed)?"right-to-left":"left-to-right";case"vertical":return c.some(a=>a.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),js=a=>a.options.defaultTooltipEventType,jt=a=>a.options.validateTooltipEventTypes;function ju(a,b,c){if(null==a)return b;var d=a?"axis":"item";return null==c?b:c.includes(d)?d:b}function jv(a,b){return ju(b,js(a),jt(a))}var jw=(a,b)=>{var c,d=Number(b);if(!m(d)&&null!=b)return d>=0?null==a||null==(c=a[d])?void 0:c.value:void 0};function jx(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function jy(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?jx(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):jx(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var jz=(a,b,c,d)=>{if(null==b)return bo;var e=function(a,b,c){return"axis"===b?"click"===c?a.axisInteraction.click:a.axisInteraction.hover:"click"===c?a.itemInteraction.click:a.itemInteraction.hover}(a,b,c);if(null==e)return bo;if(e.active)return e;if(a.keyboardInteraction.active)return a.keyboardInteraction;if(a.syncInteraction.active&&null!=a.syncInteraction.index)return a.syncInteraction;var f=!0===a.settings.active;if(null!=e.index){if(f)return jy(jy({},e),{},{active:!0})}else if(null!=d)return{active:!0,coordinate:void 0,dataKey:void 0,index:d};return jy(jy({},bo),{},{coordinate:e.coordinate})},jA=(a,b)=>{var c=null==a?void 0:a.index;if(null==c)return null;var d=Number(c);if(!gI(d))return c;var e=Infinity;return b.length>0&&(e=b.length-1),String(Math.max(0,Math.min(d,e)))},jB=(a,b,c,d,e,f,g,h)=>{if(null!=f&&null!=h){var i=g[0],j=null==i?void 0:h(i.positions,f);if(null!=j)return j;var k=null==e?void 0:e[Number(f)];if(k)if("horizontal"===c)return{x:k.coordinate,y:(d.top+b)/2};else return{x:(d.left+a)/2,y:k.coordinate}}},jC=(a,b,c,d)=>{var e;return"axis"===b?a.tooltipItemPayloads:0===a.tooltipItemPayloads.length?[]:null==(e="hover"===c?a.itemInteraction.hover.dataKey:a.itemInteraction.click.dataKey)&&null!=d?[a.tooltipItemPayloads[0]]:a.tooltipItemPayloads.filter(a=>{var b;return(null==(b=a.settings)?void 0:b.dataKey)===e})},jD=a=>a.options.tooltipPayloadSearcher,jE=a=>a.tooltip;function jF(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function jG(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?jF(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):jF(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var jH=(a,b,c,d,e,f,g)=>{if(null!=b&&null!=f){var{chartData:h,computedData:i,dataStartIndex:j,dataEndIndex:l}=c;return a.reduce((a,c)=>{var m,n,o,p,q,{dataDefinedOnItem:r,settings:s}=c,t=function(a,b,c){return Array.isArray(a)&&a&&b+c!==0?a.slice(b,c+1):a}((m=r,n=h,null!=m?m:n),j,l),u=null!=(o=null==s?void 0:s.dataKey)?o:null==d?void 0:d.dataKey,v=null==s?void 0:s.nameKey;return Array.isArray(p=null!=d&&d.dataKey&&Array.isArray(t)&&!Array.isArray(t[0])&&"axis"===g?function(a,b,c){if(a&&a.length)return a.find(a=>a&&("function"==typeof b?b(a):k()(a,b))===c)}(t,d.dataKey,e):f(t,b,i,v))?p.forEach(b=>{var c=jG(jG({},s),{},{name:b.name,unit:b.unit,color:void 0,fill:void 0});a.push(cB({tooltipEntrySettings:c,dataKey:b.dataKey,payload:b.payload,value:cs(b.payload,b.dataKey),name:b.name}))}):a.push(cB({tooltipEntrySettings:s,dataKey:u,payload:p,value:cs(p,u),name:null!=(q=cs(p,v))?q:null==s?void 0:s.name})),a},[])}},jI=a=>{var b=cZ(a);return"horizontal"===b?"xAxis":"vertical"===b?"yAxis":"centric"===b?"angleAxis":"radiusAxis"},jJ=a=>a.tooltip.settings.axisId,jK=a=>{var b=jI(a),c=jJ(a);return h7(a,b,c)},jL=b2([jK,cZ,h8,hD,jI],iT),jM=b2([a=>a.graphicalItems.cartesianItems,a=>a.graphicalItems.polarItems],(a,b)=>[...a,...b]),jN=b2([jI,jJ],h9),jO=b2([jM,jK,jN],ic),jP=b2([jO],ih),jQ=b2([jP,gF],ij),jR=b2([jQ,jK,jO],il),jS=b2([jK],ix),jT=b2([jQ,jO,hC],iq),jU=b2([jT,gF,jI],is),jV=b2([jO],ie),jW=b2([jQ,jK,jV,jI],iu),jX=b2([iz,jI,jJ],iA),jY=b2([jX,jI],iG),jZ=b2([iC,jI,jJ],iA),j$=b2([jZ,jI],iI),j_=b2([iE,jI,jJ],iA),j0=b2([j_,jI],iK),j1=b2([jY,j0,j$],iy),j2=b2([jK,jS,jU,jW,j1],iO),j3=b2([jK,cZ,jQ,jR,hC,jI,j2],iR),j4=b2([j3,jK,jL],iW),j5=b2([jK,j3,j4,jI],iY),j6=a=>{var b=jI(a),c=jJ(a);return i4(a,b,c,!1)},j7=b2([jK,j6],hJ),j8=b2([jK,jL,j5,j7],iV),j9=b2([cZ,jR,jK,jI],ji),ka=b2([cZ,jR,jK,jI],jk),kb=b2([cZ,jK,jL,j8,j6,j9,ka,jI],(a,b,c,d,e,f,g,h)=>{if(b){var{type:i}=b,j=ct(a,h);if(d){var k="scaleBand"===c&&d.bandwidth?d.bandwidth()/2:2,m="category"===i&&d.bandwidth?d.bandwidth()/k:0;return(m="angleAxis"===h&&null!=e&&(null==e?void 0:e.length)>=2?2*l(e[0]-e[1])*m:m,j&&g)?g.map((a,b)=>({coordinate:d(a)+m,value:a,index:b,offset:m})):d.domain().map((a,b)=>({coordinate:d(a)+m,value:f?f[a]:a,index:b,offset:m}))}}}),kc=b2([js,jt,a=>a.tooltip.settings],(a,b,c)=>ju(c.shared,a,b)),kd=a=>a.tooltip.settings.trigger,ke=a=>a.tooltip.settings.defaultIndex,kf=b2([jE,kc,kd,ke],jz),kg=b2([kf,jQ],jA),kh=b2([kb,kg],jw),ki=b2([kf],a=>{if(a)return a.dataKey}),kj=b2([jE,kc,kd,ke],jC),kk=b2([cD,cE,cZ,cN,kb,ke,kj,jD],jB),kl=b2([kf,kk],(a,b)=>null!=a&&a.coordinate?a.coordinate:b),km=b2([kf],a=>a.active),kn=b2([kj,kg,gF,jK,kh,jD,kc],jH);b2([kn],a=>{if(null!=a)return Array.from(new Set(a.map(a=>a.payload).filter(a=>null!=a)))});var ko=(a,b)=>b,kp=(a,b,c)=>c,kq=(a,b,c,d)=>d,kr=b2(kb,a=>cd()(a,a=>a.coordinate)),ks=b2([jE,ko,kp,kq],jz),kt=b2([ks,jQ],jA),ku=b2([jE,ko,kp,kq],jC),kv=b2([cD,cE,cZ,cN,kb,kq,ku,jD],jB),kw=b2([ks,kv],(a,b)=>{var c;return null!=(c=a.coordinate)?c:b}),kx=b2(kb,kt,jw),ky=b2([ku,kt,gF,jK,kx,jD,ko],jH),kz=b2([ks],a=>({isActive:a.active,activeIndex:a.index})),kA=b2([(a,b)=>b,cZ,hW,jI,j7,kb,kr,cN],(a,b,c,d,e,f,g,h)=>{if(a&&b&&d&&e&&f){var i=function(a,b,c,d,e){return"horizontal"===c||"vertical"===c?a>=e.left&&a<=e.left+e.width&&b>=e.top&&b<=e.top+e.height?{x:a,y:b}:null:d?((a,b)=>{var c,{x:d,y:e}=a,{radius:f,angle:g}=((a,b)=>{var{x:c,y:d}=a,{cx:e,cy:f}=b,g=((a,b)=>{var{x:c,y:d}=a,{x:e,y:f}=b;return Math.sqrt((c-e)**2+(d-f)**2)})({x:c,y:d},{x:e,y:f});if(g<=0)return{radius:g,angle:0};var h=Math.acos((c-e)/g);return d>f&&(h=2*Math.PI-h),{radius:g,angle:180*h/Math.PI,angleInRadian:h}})({x:d,y:e},b),{innerRadius:h,outerRadius:i}=b;if(f<h||f>i||0===f)return null;var{startAngle:j,endAngle:k}=(a=>{var{startAngle:b,endAngle:c}=a,d=Math.min(Math.floor(b/360),Math.floor(c/360));return{startAngle:b-360*d,endAngle:c-360*d}})(b),l=g;if(j<=k){for(;l>k;)l-=360;for(;l<j;)l+=360;c=l>=j&&l<=k}else{for(;l>j;)l-=360;for(;l<k;)l+=360;c=l>=k&&l<=j}return c?cm(cm({},b),{},{radius:f,angle:((a,b)=>{var{startAngle:c,endAngle:d}=b;return a+360*Math.min(Math.floor(c/360),Math.floor(d/360))})(l,b)}):null})({x:a,y:b},d):null}(a.chartX,a.chartY,b,c,h);if(i){var j=((a,b,c,d,e)=>{var f,g=-1,h=null!=(f=null==b?void 0:b.length)?f:0;if(h<=1||null==a)return 0;if("angleAxis"===d&&null!=e&&1e-6>=Math.abs(Math.abs(e[1]-e[0])-360))for(var i=0;i<h;i++){var j=i>0?c[i-1].coordinate:c[h-1].coordinate,k=c[i].coordinate,m=i>=h-1?c[0].coordinate:c[i+1].coordinate,n=void 0;if(l(k-j)!==l(m-k)){var o=[];if(l(m-k)===l(e[1]-e[0])){n=m;var p=k+e[1]-e[0];o[0]=Math.min(p,(p+j)/2),o[1]=Math.max(p,(p+j)/2)}else{n=j;var q=m+e[1]-e[0];o[0]=Math.min(k,(q+k)/2),o[1]=Math.max(k,(q+k)/2)}var r=[Math.min(k,(n+k)/2),Math.max(k,(n+k)/2)];if(a>r[0]&&a<=r[1]||a>=o[0]&&a<=o[1]){({index:g}=c[i]);break}}else{var s=Math.min(j,m),t=Math.max(j,m);if(a>(s+k)/2&&a<=(t+k)/2){({index:g}=c[i]);break}}}else if(b){for(var u=0;u<h;u++)if(0===u&&a<=(b[u].coordinate+b[u+1].coordinate)/2||u>0&&u<h-1&&a>(b[u].coordinate+b[u-1].coordinate)/2&&a<=(b[u].coordinate+b[u+1].coordinate)/2||u===h-1&&a>(b[u].coordinate+b[u-1].coordinate)/2){({index:g}=b[u]);break}}return g})(((a,b)=>"horizontal"===b?a.x:"vertical"===b?a.y:"centric"===b?a.angle:a.radius)(i,b),g,f,d,e),k=((a,b,c,d)=>{var e=b.find(a=>a&&a.index===c);if(e){if("horizontal"===a)return{x:e.coordinate,y:d.y};if("vertical"===a)return{x:d.x,y:e.coordinate};if("centric"===a){var f=e.coordinate,{radius:g}=d;return cr(cr(cr({},d),co(d.cx,d.cy,g,f)),{},{angle:f,radius:g})}var h=e.coordinate,{angle:i}=d;return cr(cr(cr({},d),co(d.cx,d.cy,h,i)),{},{angle:i,radius:h})}return{x:0,y:0}})(b,f,j,i);return{activeIndex:String(j),activeCoordinate:k}}}}),kB=a=>{var b=a.currentTarget.getBoundingClientRect(),c=b.width/a.currentTarget.offsetWidth,d=b.height/a.currentTarget.offsetHeight;return{chartX:Math.round((a.clientX-b.left)/c),chartY:Math.round((a.clientY-b.top)/d)}},kC=ay("mouseClick"),kD=ba();kD.startListening({actionCreator:kC,effect:(a,b)=>{var c=a.payload,d=kA(b.getState(),kB(c));(null==d?void 0:d.activeIndex)!=null&&b.dispatch(by({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate}))}});var kE=ay("mouseMove"),kF=ba();function kG(a,b){return b instanceof HTMLElement?"HTMLElement <".concat(b.tagName,' class="').concat(b.className,'">'):b===window?"global.window":b}function kH(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function kI(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?kH(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):kH(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}kF.startListening({actionCreator:kE,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jv(d,d.tooltip.settings.shared),f=kA(d,kB(c));"axis"===e&&((null==f?void 0:f.activeIndex)!=null?b.dispatch(bx({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate})):b.dispatch(bv()))}});var kJ=aG({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(a,b){a.xAxis[b.payload.id]=b.payload},removeXAxis(a,b){delete a.xAxis[b.payload.id]},addYAxis(a,b){a.yAxis[b.payload.id]=b.payload},removeYAxis(a,b){delete a.yAxis[b.payload.id]},addZAxis(a,b){a.zAxis[b.payload.id]=b.payload},removeZAxis(a,b){delete a.zAxis[b.payload.id]},updateYAxisWidth(a,b){var{id:c,width:d}=b.payload;a.yAxis[c]&&(a.yAxis[c]=kI(kI({},a.yAxis[c]),{},{width:d}))}}}),{addXAxis:kK,removeXAxis:kL,addYAxis:kM,removeYAxis:kN,addZAxis:kO,removeZAxis:kP,updateYAxisWidth:kQ}=kJ.actions,kR=kJ.reducer,kS=aG({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(a){a.countOfBars+=1},removeBar(a){a.countOfBars-=1},addCartesianGraphicalItem(a,b){a.cartesianItems.push(b.payload)},replaceCartesianGraphicalItem(a,b){var{prev:c,next:d}=b.payload,e=au(a).cartesianItems.indexOf(c);e>-1&&(a.cartesianItems[e]=d)},removeCartesianGraphicalItem(a,b){var c=au(a).cartesianItems.indexOf(b.payload);c>-1&&a.cartesianItems.splice(c,1)},addPolarGraphicalItem(a,b){a.polarItems.push(b.payload)},removePolarGraphicalItem(a,b){var c=au(a).polarItems.indexOf(b.payload);c>-1&&a.polarItems.splice(c,1)}}}),{addBar:kT,removeBar:kU,addCartesianGraphicalItem:kV,replaceCartesianGraphicalItem:kW,removeCartesianGraphicalItem:kX,addPolarGraphicalItem:kY,removePolarGraphicalItem:kZ}=kS.actions,k$=kS.reducer,k_=aG({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(a,b)=>{a.dots.push(b.payload)},removeDot:(a,b)=>{var c=au(a).dots.findIndex(a=>a===b.payload);-1!==c&&a.dots.splice(c,1)},addArea:(a,b)=>{a.areas.push(b.payload)},removeArea:(a,b)=>{var c=au(a).areas.findIndex(a=>a===b.payload);-1!==c&&a.areas.splice(c,1)},addLine:(a,b)=>{a.lines.push(b.payload)},removeLine:(a,b)=>{var c=au(a).lines.findIndex(a=>a===b.payload);-1!==c&&a.lines.splice(c,1)}}}),{addDot:k0,removeDot:k1,addArea:k2,removeArea:k3,addLine:k4,removeLine:k5}=k_.actions,k6=k_.reducer,k7={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},k8=aG({name:"brush",initialState:k7,reducers:{setBrushSettings:(a,b)=>null==b.payload?k7:b.payload}}),{setBrushSettings:k9}=k8.actions,la=k8.reducer,lb=aG({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(a,b){a.size.width=b.payload.width,a.size.height=b.payload.height},setLegendSettings(a,b){a.settings.align=b.payload.align,a.settings.layout=b.payload.layout,a.settings.verticalAlign=b.payload.verticalAlign,a.settings.itemSorter=b.payload.itemSorter},addLegendPayload(a,b){a.payload.push(b.payload)},removeLegendPayload(a,b){var c=au(a).payload.indexOf(b.payload);c>-1&&a.payload.splice(c,1)}}}),{setLegendSize:lc,setLegendSettings:ld,addLegendPayload:le,removeLegendPayload:lf}=lb.actions,lg=lb.reducer,lh={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},li=aG({name:"rootProps",initialState:lh,reducers:{updateOptions:(a,b)=>{var c;a.accessibilityLayer=b.payload.accessibilityLayer,a.barCategoryGap=b.payload.barCategoryGap,a.barGap=null!=(c=b.payload.barGap)?c:lh.barGap,a.barSize=b.payload.barSize,a.maxBarSize=b.payload.maxBarSize,a.stackOffset=b.payload.stackOffset,a.syncId=b.payload.syncId,a.syncMethod=b.payload.syncMethod,a.className=b.payload.className}}}),lj=li.reducer,{updateOptions:lk}=li.actions,ll=aG({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(a,b){a.radiusAxis[b.payload.id]=b.payload},removeRadiusAxis(a,b){delete a.radiusAxis[b.payload.id]},addAngleAxis(a,b){a.angleAxis[b.payload.id]=b.payload},removeAngleAxis(a,b){delete a.angleAxis[b.payload.id]}}}),{addRadiusAxis:lm,removeRadiusAxis:ln,addAngleAxis:lo,removeAngleAxis:lp}=ll.actions,lq=ll.reducer,lr=aG({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(a,b)=>b.payload}}),{updatePolarOptions:ls}=lr.actions,lt=lr.reducer,lu=ay("keyDown"),lv=ay("focus"),lw=ba();lw.startListening({actionCreator:lu,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip,e=a.payload;if("ArrowRight"===e||"ArrowLeft"===e||"Enter"===e){var f=Number(jA(d,jQ(c))),g=kb(c);if("Enter"===e){var h=kv(c,"axis","hover",String(d.index));b.dispatch(bA({active:!d.active,activeIndex:d.index,activeDataKey:d.dataKey,activeCoordinate:h}));return}var i=f+("ArrowRight"===e?1:-1)*("left-to-right"===jr(c)?1:-1);if(null!=g&&!(i>=g.length)&&!(i<0)){var j=kv(c,"axis","hover",String(i));b.dispatch(bA({active:!0,activeIndex:i.toString(),activeDataKey:void 0,activeCoordinate:j}))}}}}}),lw.startListening({actionCreator:lv,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip;if(!d.active&&null==d.index){var e=kv(c,"axis","hover",String("0"));b.dispatch(bA({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:e}))}}}});var lx=ay("externalEvent"),ly=ba();ly.startListening({actionCreator:lx,effect:(a,b)=>{if(null!=a.payload.handler){var c=b.getState(),d={activeCoordinate:kl(c),activeDataKey:ki(c),activeIndex:kg(c),activeLabel:kh(c),activeTooltipIndex:kg(c),isTooltipActive:km(c)};a.payload.handler(d,a.payload.reactEvent)}}});var lz=b2([jE],a=>a.tooltipItemPayloads),lA=b2([lz,jD,(a,b,c)=>b,(a,b,c)=>c],(a,b,c,d)=>{var e=a.find(a=>a.settings.dataKey===d);if(null!=e){var{positions:f}=e;if(null!=f)return b(f,c)}}),lB=ay("touchMove"),lC=ba();lC.startListening({actionCreator:lB,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jv(d,d.tooltip.settings.shared);if("axis"===e){var f=kA(d,kB({clientX:c.touches[0].clientX,clientY:c.touches[0].clientY,currentTarget:c.currentTarget}));(null==f?void 0:f.activeIndex)!=null&&b.dispatch(bx({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate}))}else if("item"===e){var g,h=c.touches[0],i=document.elementFromPoint(h.clientX,h.clientY);if(!i||!i.getAttribute)return;var j=i.getAttribute(cJ),k=null!=(g=i.getAttribute(cK))?g:void 0,l=lA(b.getState(),j,k);b.dispatch(bt({activeDataKey:k,activeIndex:j,activeCoordinate:l}))}}});var lD=F({brush:la,cartesianAxis:kR,chartData:bG,graphicalItems:k$,layout:bM,legend:lg,options:be,polarAxis:lq,polarOptions:lt,referenceElements:k6,rootProps:lj,tooltip:bB}),lE=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(a){let b,c,d,e=function(a){let{thunk:b=!0,immutableCheck:c=!0,serializableCheck:d=!0,actionCreatorCheck:e=!0}=a??{},f=new az;return b&&("boolean"==typeof b?f.push(J):f.push(I(b.extraArgument))),f},{reducer:f,middleware:g,devTools:h=!0,duplicateMiddlewareCheck:i=!0,preloadedState:j,enhancers:k}=a||{};if("function"==typeof f)b=f;else if(E(f))b=F(f);else throw Error(bb(1));c="function"==typeof g?g(e):e();let l=G;h&&(l=ax({trace:!1,..."object"==typeof h&&h}));let m=(d=function(...a){return b=>(c,d)=>{let e=b(c,d),f=()=>{throw Error(A(15))},g={getState:e.getState,dispatch:(a,...b)=>f(a,...b)};return f=G(...a.map(a=>a(g)))(e.dispatch),{...e,dispatch:f}}}(...c),function(a){let{autoBatch:b=!0}=a??{},c=new az(d);return b&&c.push(((a={type:"raf"})=>b=>(...c)=>{let d=b(...c),e=!0,f=!1,g=!1,h=new Set,i="tick"===a.type?queueMicrotask:"raf"===a.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:aC(10):"callback"===a.type?a.queueNotification:aC(a.timeout),j=()=>{g=!1,f&&(f=!1,h.forEach(a=>a()))};return Object.assign({},d,{subscribe(a){let b=d.subscribe(()=>e&&a());return h.add(a),()=>{b(),h.delete(a)}},dispatch(a){try{return(f=!(e=!a?.meta?.RTK_autoBatch))&&!g&&(g=!0,i(j)),d.dispatch(a)}finally{e=!0}}})})("object"==typeof b?b:void 0)),c});return function a(b,c,d){if("function"!=typeof b)throw Error(A(2));if("function"==typeof c&&"function"==typeof d||"function"==typeof d&&"function"==typeof arguments[3])throw Error(A(0));if("function"==typeof c&&void 0===d&&(d=c,c=void 0),void 0!==d){if("function"!=typeof d)throw Error(A(1));return d(a)(b,c)}let e=b,f=c,g=new Map,h=g,i=0,j=!1;function k(){h===g&&(h=new Map,g.forEach((a,b)=>{h.set(b,a)}))}function l(){if(j)throw Error(A(3));return f}function m(a){if("function"!=typeof a)throw Error(A(4));if(j)throw Error(A(5));let b=!0;k();let c=i++;return h.set(c,a),function(){if(b){if(j)throw Error(A(6));b=!1,k(),h.delete(c),g=null}}}function n(a){if(!E(a))throw Error(A(7));if(void 0===a.type)throw Error(A(8));if("string"!=typeof a.type)throw Error(A(17));if(j)throw Error(A(9));try{j=!0,f=e(f,a)}finally{j=!1}return(g=h).forEach(a=>{a()}),a}return n({type:D.INIT}),{dispatch:n,subscribe:m,getState:l,replaceReducer:function(a){if("function"!=typeof a)throw Error(A(10));e=a,n({type:D.REPLACE})},[B]:function(){return{subscribe(a){if("object"!=typeof a||null===a)throw Error(A(11));function b(){a.next&&a.next(l())}return b(),{unsubscribe:m(b)}},[B](){return this}}}}}(b,j,l(..."function"==typeof k?k(m):m()))}({reducer:lD,preloadedState:a,middleware:a=>a({serializableCheck:!1}).concat([kD.middleware,kF.middleware,lw.middleware,ly.middleware,lC.middleware]),devTools:{serialize:{replacer:kG},name:"recharts-".concat(b)}})};function lF(a){var{preloadedState:b,children:c,reduxStoreName:d}=a,e=cR(),g=(0,f.useRef)(null);return e?c:(null==g.current&&(g.current=lE(b,d)),f.createElement(bn,{context:b5,store:g.current},c))}var lG=a=>{var{chartData:b}=a,c=b7(),d=cR();return(0,f.useEffect)(()=>d?()=>{}:(c(bD(b)),()=>{c(bD(void 0))}),[b,c,d]),null};function lH(a){var{layout:b,width:c,height:d,margin:e}=a;return b7(),cR(),null}function lI(a){return b7(),null}function lJ(a){return b7(),null}var lK=c(26188),lL=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],lM=["points","pathLength"],lN={svg:["viewBox","children"],polygon:lM,polyline:lM},lO=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],lP=(a,b,c)=>{if(null===a||"object"!=typeof a&&"function"!=typeof a)return null;var d=null;return Object.keys(a).forEach(e=>{var f=a[e];lO.includes(e)&&"function"==typeof f&&(d||(d={}),d[e]=a=>(f(b,c,a),null))}),d},lQ=a=>"string"==typeof a?a:a?a.displayName||a.name||"Component":"",lR=null,lS=null,lT=a=>{if(a===lR&&Array.isArray(lS))return lS;var b=[];return f.Children.forEach(a,a=>{null==a||((0,lK.isFragment)(a)?b=b.concat(lT(a.props.children)):b.push(a))}),lS=b,lR=a,b};function lU(a,b){var c=[],d=[];return d=Array.isArray(b)?b.map(a=>lQ(a)):[lQ(b)],lT(a).forEach(a=>{var b=k()(a,"type.displayName")||k()(a,"type.name");-1!==d.indexOf(b)&&c.push(a)}),c}var lV=(a,b,c)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var d=a;if((0,f.isValidElement)(a)&&(d=a.props),"object"!=typeof d&&"function"!=typeof d)return null;var e={};return Object.keys(d).forEach(a=>{var f;((a,b,c,d)=>{var e,f=null!=(e=d&&(null==lN?void 0:lN[d]))?e:[];return b.startsWith("data-")||"function"!=typeof a&&(d&&f.includes(b)||lL.includes(b))||c&&lO.includes(b)})(null==(f=d)?void 0:f[a],a,b,c)&&(e[a]=d[a])}),e},lW=()=>cb(a=>a.rootProps.accessibilityLayer),lX=["children","width","height","viewBox","className","style","title","desc"];function lY(){return(lY=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var lZ=(0,f.forwardRef)((a,b)=>{var{children:c,width:d,height:e,viewBox:h,className:i,style:j,title:k,desc:l}=a,m=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,lX),n=h||{width:d,height:e,x:0,y:0},o=g("recharts-surface",i);return f.createElement("svg",lY({},lV(m,!0,"svg"),{className:o,width:d,height:e,style:j,viewBox:"".concat(n.x," ").concat(n.y," ").concat(n.width," ").concat(n.height),ref:b}),f.createElement("title",null,k),f.createElement("desc",null,l),c)}),l$=["children"];function l_(){return(l_=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var l0={width:"100%",height:"100%"},l1=(0,f.forwardRef)((a,b)=>{var c,d,e=cX(),g=cY(),h=lW();if(!gJ(e)||!gJ(g))return null;var{children:i,otherAttributes:j,title:k,desc:l}=a;return c="number"==typeof j.tabIndex?j.tabIndex:h?0:void 0,d="string"==typeof j.role?j.role:h?"application":void 0,f.createElement(lZ,l_({},j,{title:k,desc:l,role:d,tabIndex:c,width:e,height:g,style:l0,ref:b}),i)}),l2=a=>{var{children:b}=a,c=cb(cT);if(!c)return null;var{width:d,height:e,y:g,x:h}=c;return f.createElement(lZ,{width:d,height:e,x:h,y:g},b)},l3=(0,f.forwardRef)((a,b)=>{var{children:c}=a,d=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,l$);return cR()?f.createElement(l2,null,c):f.createElement(l1,l_({ref:b},d),c)});function l4(a){return a.tooltip.syncInteraction}new(c(74633));var l5=(0,f.createContext)(null),l6=(0,f.createContext)(null);function l7(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var l8=(0,f.forwardRef)((a,b)=>{var{children:c,className:d,height:e,onClick:h,onContextMenu:i,onDoubleClick:j,onMouseDown:k,onMouseEnter:l,onMouseLeave:m,onMouseMove:n,onMouseUp:o,onTouchEnd:p,onTouchMove:q,onTouchStart:r,style:s,width:t}=a,u=b7(),[v,w]=(0,f.useState)(null),[x,y]=(0,f.useState)(null);b7(),cb(hE),cb(hG),b7(),cb(hF),cb(kb),c$(),cU(),cb(a=>a.rootProps.className),cb(hE),cb(hG),b7();var z=function(){b7();var[a,b]=(0,f.useState)(null);return cb(cF),b}(),A=(0,f.useCallback)(a=>{z(a),"function"==typeof b&&b(a),w(a),y(a)},[z,b,w,y]),B=(0,f.useCallback)(a=>{u(kC(a)),u(lx({handler:h,reactEvent:a}))},[u,h]),C=(0,f.useCallback)(a=>{u(kE(a)),u(lx({handler:l,reactEvent:a}))},[u,l]),D=(0,f.useCallback)(a=>{u(bv()),u(lx({handler:m,reactEvent:a}))},[u,m]),E=(0,f.useCallback)(a=>{u(kE(a)),u(lx({handler:n,reactEvent:a}))},[u,n]),F=(0,f.useCallback)(()=>{u(lv())},[u]),G=(0,f.useCallback)(a=>{u(lu(a.key))},[u]),H=(0,f.useCallback)(a=>{u(lx({handler:i,reactEvent:a}))},[u,i]),I=(0,f.useCallback)(a=>{u(lx({handler:j,reactEvent:a}))},[u,j]),J=(0,f.useCallback)(a=>{u(lx({handler:k,reactEvent:a}))},[u,k]),K=(0,f.useCallback)(a=>{u(lx({handler:o,reactEvent:a}))},[u,o]),L=(0,f.useCallback)(a=>{u(lx({handler:r,reactEvent:a}))},[u,r]),M=(0,f.useCallback)(a=>{u(lB(a)),u(lx({handler:q,reactEvent:a}))},[u,q]),N=(0,f.useCallback)(a=>{u(lx({handler:p,reactEvent:a}))},[u,p]);return f.createElement(l5.Provider,{value:v},f.createElement(l6.Provider,{value:x},f.createElement("div",{className:g("recharts-wrapper",d),style:function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?l7(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):l7(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({position:"relative",cursor:"default",width:t,height:e},s),onClick:B,onContextMenu:H,onDoubleClick:I,onFocus:F,onKeyDown:G,onMouseDown:J,onMouseEnter:C,onMouseLeave:D,onMouseMove:E,onMouseUp:K,onTouchEnd:N,onTouchMove:M,onTouchStart:L,ref:A},c)))}),l9=b2([cN],a=>{if(a)return{top:a.top,bottom:a.bottom,left:a.left,right:a.right}}),ma=b2([l9,cD,cE],(a,b,c)=>{if(a&&null!=b&&null!=c)return{x:a.left,y:a.top,width:Math.max(0,b-a.left-a.right),height:Math.max(0,c-a.top-a.bottom)}}),mb=()=>cb(ma),mc=(0,f.createContext)(void 0),md=a=>{var{children:b}=a,[c]=(0,f.useState)("".concat(r("recharts"),"-clip")),d=mb();if(null==d)return null;var{x:e,y:g,width:h,height:i}=d;return f.createElement(mc.Provider,{value:c},f.createElement("defs",null,f.createElement("clipPath",{id:c},f.createElement("rect",{x:e,y:g,height:i,width:h}))),b)},me=["children","className","width","height","style","compact","title","desc"],mf=(0,f.forwardRef)((a,b)=>{var{children:c,className:d,width:e,height:g,style:h,compact:i,title:j,desc:k}=a,l=lV(function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,me),!1);return i?f.createElement(l3,{otherAttributes:l,title:j,desc:k},c):f.createElement(l8,{className:d,style:h,width:e,height:g,onClick:a.onClick,onMouseLeave:a.onMouseLeave,onMouseEnter:a.onMouseEnter,onMouseMove:a.onMouseMove,onMouseDown:a.onMouseDown,onMouseUp:a.onMouseUp,onContextMenu:a.onContextMenu,onDoubleClick:a.onDoubleClick,onTouchStart:a.onTouchStart,onTouchMove:a.onTouchMove,onTouchEnd:a.onTouchEnd},f.createElement(l3,{otherAttributes:l,title:j,desc:k,ref:b},f.createElement(md,null,c)))});function mg(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mh(a,b){var c=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mg(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mg(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return Object.keys(b).reduce((a,c)=>(void 0===a[c]&&void 0!==b[c]&&(a[c]=b[c]),a),c)}var mi=["width","height","layout"];function mj(){return(mj=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var mk={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},ml=(0,f.forwardRef)(function(a,b){var c,d=mh(a.categoricalChartProps,mk),{width:e,height:g,layout:h}=d,i=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,mi);if(!gJ(e)||!gJ(g))return null;var{chartName:j,defaultTooltipEventType:k,validateTooltipEventTypes:l,tooltipPayloadSearcher:m}=a;return f.createElement(lF,{preloadedState:{options:{chartName:j,defaultTooltipEventType:k,validateTooltipEventTypes:l,tooltipPayloadSearcher:m,eventEmitter:void 0}},reduxStoreName:null!=(c=d.id)?c:j},f.createElement(lG,{chartData:d.data}),f.createElement(lH,{width:e,height:g,layout:h,margin:d.margin}),f.createElement(lI,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),f.createElement(lJ,{cx:d.cx,cy:d.cy,startAngle:d.startAngle,endAngle:d.endAngle,innerRadius:d.innerRadius,outerRadius:d.outerRadius}),f.createElement(mf,mj({width:e,height:g},i,{ref:b})))}),mm=["item"],mn={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},mo=(0,f.forwardRef)((a,b)=>{var c=mh(a,mn);return f.createElement(ml,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:mm,tooltipPayloadSearcher:bc,categoricalChartProps:c,ref:b})}),mp=a=>a.graphicalItems.polarItems,mq=b2([hX,hY],h9),mr=b2([mp,h6,mq],ic),ms=b2([mr],ih),mt=b2([ms,gG],ij),mu=b2([mt,h6,mr],il),mv=b2([mt,h6,mr],(a,b,c)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var d;return{value:cs(a,null!=(d=b.dataKey)?d:c.dataKey),errorDomain:[]}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cs(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]}))),mw=()=>void 0,mx=b2([h6,iN,mw,mv,mw],iO),my=b2([h6,cZ,mt,mu,hC,hX,mx],iR),mz=b2([my,h6,iU],iW);function mA(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mB(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mA(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mA(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}b2([h6,my,mz,hX],iY);var mC=(a,b)=>b,mD=[],mE=(a,b,c)=>(null==c?void 0:c.length)===0?mD:c,mF=b2([gG,mC,mE],(a,b,c)=>{var d,{chartData:e}=a;if((d=(null==b?void 0:b.data)!=null&&b.data.length>0?b.data:e)&&d.length||null==c||(d=c.map(a=>mB(mB({},b.presentationProps),a.props))),null!=d)return d}),mG=b2([mF,mC,mE],(a,b,c)=>{if(null!=a)return a.map((a,d)=>{var e,f,g=cs(a,b.nameKey,b.name);return f=null!=c&&null!=(e=c[d])&&null!=(e=e.props)&&e.fill?c[d].props.fill:"object"==typeof a&&null!=a&&"fill"in a?a.fill:b.fill,{value:cC(g,b.dataKey),color:f,payload:a,type:b.legendType}})}),mH=b2([mp,mC],(a,b)=>{if(a.some(a=>"pie"===a.type&&b.dataKey===a.dataKey&&b.data===a.data))return b}),mI=b2([mF,mH,mE,cN],(a,b,c,d)=>{if(null!=b&&null!=a)return function(a){var b,c,d,{pieSettings:e,displayedData:f,cells:g,offset:h}=a,{cornerRadius:i,startAngle:j,endAngle:k,dataKey:m,nameKey:n,tooltipType:p}=e,q=Math.abs(e.minAngle),r=l(k-j)*Math.min(Math.abs(k-j),360),t=Math.abs(r),u=f.length<=1?0:null!=(b=e.paddingAngle)?b:0,v=f.filter(a=>0!==cs(a,m,0)).length,w=t-v*q-(t>=360?v:v-1)*u,x=f.reduce((a,b)=>{var c=cs(b,m,0);return a+(o(c)?c:0)},0);return x>0&&(c=f.map((a,b)=>{var c,f=cs(a,m,0),k=cs(a,n,b),t=((a,b,c)=>{let d,e,f;var{top:g,left:h,width:i,height:j}=b,k=cp(i,j),l=h+s(a.cx,i,i/2),m=g+s(a.cy,j,j/2),n=s(a.innerRadius,k,0);return{cx:l,cy:m,innerRadius:n,outerRadius:(d=c,e=a.outerRadius,f=k,"function"==typeof e?e(d):s(e,f,.8*f)),maxRadius:a.maxRadius||Math.sqrt(i*i+j*j)/2}})(e,h,a),v=(o(f)?f:0)/x,y=oZ(oZ({},a),g&&g[b]&&g[b].props),z=(c=b?d.endAngle+l(r)*u*(0!==f):j)+l(r)*((0!==f?q:0)+v*w),A=(c+z)/2,B=(t.innerRadius+t.outerRadius)/2,C=[{name:k,value:f,payload:y,dataKey:m,type:p}],D=co(t.cx,t.cy,B,A);return d=oZ(oZ(oZ(oZ({},e.presentationProps),{},{percent:v,cornerRadius:i,name:k,tooltipPayload:C,midAngle:A,middleRadius:B,tooltipPosition:D},y),t),{},{value:cs(a,m),startAngle:c,endAngle:z,payload:y,paddingAngle:l(r)*u})})),c}({offset:d,pieSettings:b,displayedData:a,cells:c})});function mJ(a){return b7(),(0,f.useRef)(null),null}function mK(a){return b7(),null}var mL=["children","className"];function mM(){return(mM=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var mN=f.forwardRef((a,b)=>{var{children:c,className:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mL),h=g("recharts-layer",d);return f.createElement("g",mM({className:h},lV(e,!0),{ref:b}),c)});function mO(){}function mP(a,b,c){a._context.bezierCurveTo((2*a._x0+a._x1)/3,(2*a._y0+a._y1)/3,(a._x0+2*a._x1)/3,(a._y0+2*a._y1)/3,(a._x0+4*a._x1+b)/6,(a._y0+4*a._y1+c)/6)}function mQ(a){this._context=a}function mR(a){this._context=a}function mS(a){this._context=a}mQ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:mP(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:mP(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},mR.prototype={areaStart:mO,areaEnd:mO,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._x2=a,this._y2=b;break;case 1:this._point=2,this._x3=a,this._y3=b;break;case 2:this._point=3,this._x4=a,this._y4=b,this._context.moveTo((this._x0+4*this._x1+a)/6,(this._y0+4*this._y1+b)/6);break;default:mP(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},mS.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var c=(this._x0+4*this._x1+a)/6,d=(this._y0+4*this._y1+b)/6;this._line?this._context.lineTo(c,d):this._context.moveTo(c,d);break;case 3:this._point=4;default:mP(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}};class mT{constructor(a,b){this._context=a,this._x=b}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+a)/2,this._y0,this._x0,b,a,b):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+b)/2,a,this._y0,a,b)}this._x0=a,this._y0=b}}function mU(a){this._context=a}function mV(a){this._context=a}function mW(a){return new mV(a)}mU.prototype={areaStart:mO,areaEnd:mO,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(a,b){a*=1,b*=1,this._point?this._context.lineTo(a,b):(this._point=1,this._context.moveTo(a,b))}};function mX(a,b,c){var d=a._x1-a._x0,e=b-a._x1,f=(a._y1-a._y0)/(d||e<0&&-0),g=(c-a._y1)/(e||d<0&&-0);return((f<0?-1:1)+(g<0?-1:1))*Math.min(Math.abs(f),Math.abs(g),.5*Math.abs((f*e+g*d)/(d+e)))||0}function mY(a,b){var c=a._x1-a._x0;return c?(3*(a._y1-a._y0)/c-b)/2:b}function mZ(a,b,c){var d=a._x0,e=a._y0,f=a._x1,g=a._y1,h=(f-d)/3;a._context.bezierCurveTo(d+h,e+h*b,f-h,g-h*c,f,g)}function m$(a){this._context=a}function m_(a){this._context=new m0(a)}function m0(a){this._context=a}function m1(a){this._context=a}function m2(a){var b,c,d=a.length-1,e=Array(d),f=Array(d),g=Array(d);for(e[0]=0,f[0]=2,g[0]=a[0]+2*a[1],b=1;b<d-1;++b)e[b]=1,f[b]=4,g[b]=4*a[b]+2*a[b+1];for(e[d-1]=2,f[d-1]=7,g[d-1]=8*a[d-1]+a[d],b=1;b<d;++b)c=e[b]/f[b-1],f[b]-=c,g[b]-=c*g[b-1];for(e[d-1]=g[d-1]/f[d-1],b=d-2;b>=0;--b)e[b]=(g[b]-e[b+1])/f[b];for(b=0,f[d-1]=(a[d]+e[d-1])/2;b<d-1;++b)f[b]=2*a[b+1]-e[b+1];return[e,f]}function m3(a,b){this._context=a,this._t=b}mV.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._context.lineTo(a,b)}}},m$.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:mZ(this,this._t0,mY(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){var c=NaN;if(b*=1,(a*=1)!==this._x1||b!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,mZ(this,mY(this,c=mX(this,a,b)),c);break;default:mZ(this,this._t0,c=mX(this,a,b))}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b,this._t0=c}}},(m_.prototype=Object.create(m$.prototype)).point=function(a,b){m$.prototype.point.call(this,b,a)},m0.prototype={moveTo:function(a,b){this._context.moveTo(b,a)},closePath:function(){this._context.closePath()},lineTo:function(a,b){this._context.lineTo(b,a)},bezierCurveTo:function(a,b,c,d,e,f){this._context.bezierCurveTo(b,a,d,c,f,e)}},m1.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var a=this._x,b=this._y,c=a.length;if(c)if(this._line?this._context.lineTo(a[0],b[0]):this._context.moveTo(a[0],b[0]),2===c)this._context.lineTo(a[1],b[1]);else for(var d=m2(a),e=m2(b),f=0,g=1;g<c;++f,++g)this._context.bezierCurveTo(d[0][f],e[0][f],d[1][f],e[1][f],a[g],b[g]);(this._line||0!==this._line&&1===c)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(a,b){this._x.push(+a),this._y.push(+b)}},m3.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,b),this._context.lineTo(a,b);else{var c=this._x*(1-this._t)+a*this._t;this._context.lineTo(c,this._y),this._context.lineTo(c,b)}}this._x=a,this._y=b}};let m4=Math.PI,m5=2*m4,m6=m5-1e-6;function m7(a){this._+=a[0];for(let b=1,c=a.length;b<c;++b)this._+=arguments[b]+a[b]}class m8{constructor(a){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==a?m7:function(a){let b=Math.floor(a);if(!(b>=0))throw Error(`invalid digits: ${a}`);if(b>15)return m7;let c=10**b;return function(a){this._+=a[0];for(let b=1,d=a.length;b<d;++b)this._+=Math.round(arguments[b]*c)/c+a[b]}}(a)}moveTo(a,b){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(a,b){this._append`L${this._x1=+a},${this._y1=+b}`}quadraticCurveTo(a,b,c,d){this._append`Q${+a},${+b},${this._x1=+c},${this._y1=+d}`}bezierCurveTo(a,b,c,d,e,f){this._append`C${+a},${+b},${+c},${+d},${this._x1=+e},${this._y1=+f}`}arcTo(a,b,c,d,e){if(a*=1,b*=1,c*=1,d*=1,(e*=1)<0)throw Error(`negative radius: ${e}`);let f=this._x1,g=this._y1,h=c-a,i=d-b,j=f-a,k=g-b,l=j*j+k*k;if(null===this._x1)this._append`M${this._x1=a},${this._y1=b}`;else if(l>1e-6)if(Math.abs(k*h-i*j)>1e-6&&e){let m=c-f,n=d-g,o=h*h+i*i,p=Math.sqrt(o),q=Math.sqrt(l),r=e*Math.tan((m4-Math.acos((o+l-(m*m+n*n))/(2*p*q)))/2),s=r/q,t=r/p;Math.abs(s-1)>1e-6&&this._append`L${a+s*j},${b+s*k}`,this._append`A${e},${e},0,0,${+(k*m>j*n)},${this._x1=a+t*h},${this._y1=b+t*i}`}else this._append`L${this._x1=a},${this._y1=b}`}arc(a,b,c,d,e,f){if(a*=1,b*=1,c*=1,f=!!f,c<0)throw Error(`negative radius: ${c}`);let g=c*Math.cos(d),h=c*Math.sin(d),i=a+g,j=b+h,k=1^f,l=f?d-e:e-d;null===this._x1?this._append`M${i},${j}`:(Math.abs(this._x1-i)>1e-6||Math.abs(this._y1-j)>1e-6)&&this._append`L${i},${j}`,c&&(l<0&&(l=l%m5+m5),l>m6?this._append`A${c},${c},0,1,${k},${a-g},${b-h}A${c},${c},0,1,${k},${this._x1=i},${this._y1=j}`:l>1e-6&&this._append`A${c},${c},0,${+(l>=m4)},${k},${this._x1=a+c*Math.cos(e)},${this._y1=b+c*Math.sin(e)}`)}rect(a,b,c,d){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}h${c*=1}v${+d}h${-c}Z`}toString(){return this._}}function m9(a){let b=3;return a.digits=function(c){if(!arguments.length)return b;if(null==c)b=null;else{let a=Math.floor(c);if(!(a>=0))throw RangeError(`invalid digits: ${c}`);b=a}return a},()=>new m8(b)}function na(a){return a[0]}function nb(a){return a[1]}function nc(a,b){var c=ch(!0),d=null,e=mW,f=null,g=m9(h);function h(h){var i,j,k,l=(h=cg(h)).length,m=!1;for(null==d&&(f=e(k=g())),i=0;i<=l;++i)!(i<l&&c(j=h[i],i,h))===m&&((m=!m)?f.lineStart():f.lineEnd()),m&&f.point(+a(j,i,h),+b(j,i,h));if(k)return f=null,k+""||null}return a="function"==typeof a?a:void 0===a?na:ch(a),b="function"==typeof b?b:void 0===b?nb:ch(b),h.x=function(b){return arguments.length?(a="function"==typeof b?b:ch(+b),h):a},h.y=function(a){return arguments.length?(b="function"==typeof a?a:ch(+a),h):b},h.defined=function(a){return arguments.length?(c="function"==typeof a?a:ch(!!a),h):c},h.curve=function(a){return arguments.length?(e=a,null!=d&&(f=e(d)),h):e},h.context=function(a){return arguments.length?(null==a?d=f=null:f=e(d=a),h):d},h}function nd(a,b,c){var d=null,e=ch(!0),f=null,g=mW,h=null,i=m9(j);function j(j){var k,l,m,n,o,p=(j=cg(j)).length,q=!1,r=Array(p),s=Array(p);for(null==f&&(h=g(o=i())),k=0;k<=p;++k){if(!(k<p&&e(n=j[k],k,j))===q)if(q=!q)l=k,h.areaStart(),h.lineStart();else{for(h.lineEnd(),h.lineStart(),m=k-1;m>=l;--m)h.point(r[m],s[m]);h.lineEnd(),h.areaEnd()}q&&(r[k]=+a(n,k,j),s[k]=+b(n,k,j),h.point(d?+d(n,k,j):r[k],c?+c(n,k,j):s[k]))}if(o)return h=null,o+""||null}function k(){return nc().defined(e).curve(g).context(f)}return a="function"==typeof a?a:void 0===a?na:ch(+a),b="function"==typeof b?b:void 0===b?ch(0):ch(+b),c="function"==typeof c?c:void 0===c?nb:ch(+c),j.x=function(b){return arguments.length?(a="function"==typeof b?b:ch(+b),d=null,j):a},j.x0=function(b){return arguments.length?(a="function"==typeof b?b:ch(+b),j):a},j.x1=function(a){return arguments.length?(d=null==a?null:"function"==typeof a?a:ch(+a),j):d},j.y=function(a){return arguments.length?(b="function"==typeof a?a:ch(+a),c=null,j):b},j.y0=function(a){return arguments.length?(b="function"==typeof a?a:ch(+a),j):b},j.y1=function(a){return arguments.length?(c=null==a?null:"function"==typeof a?a:ch(+a),j):c},j.lineX0=j.lineY0=function(){return k().x(a).y(b)},j.lineY1=function(){return k().x(a).y(c)},j.lineX1=function(){return k().x(d).y(b)},j.defined=function(a){return arguments.length?(e="function"==typeof a?a:ch(!!a),j):e},j.curve=function(a){return arguments.length?(g=a,null!=f&&(h=g(f)),j):g},j.context=function(a){return arguments.length?(null==a?f=h=null:h=g(f=a),j):f},j}function ne(){return(ne=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nf(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ng(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nf(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nf(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}m8.prototype;var nh={curveBasisClosed:function(a){return new mR(a)},curveBasisOpen:function(a){return new mS(a)},curveBasis:function(a){return new mQ(a)},curveBumpX:function(a){return new mT(a,!0)},curveBumpY:function(a){return new mT(a,!1)},curveLinearClosed:function(a){return new mU(a)},curveLinear:mW,curveMonotoneX:function(a){return new m$(a)},curveMonotoneY:function(a){return new m_(a)},curveNatural:function(a){return new m1(a)},curveStep:function(a){return new m3(a,.5)},curveStepAfter:function(a){return new m3(a,1)},curveStepBefore:function(a){return new m3(a,0)}},ni=a=>gI(a.x)&&gI(a.y),nj=a=>a.x,nk=a=>a.y,nl=a=>{var{className:b,points:c,path:d,pathRef:e}=a;if((!c||!c.length)&&!d)return null;var h=c&&c.length?(a=>{var b,{type:c="linear",points:d=[],baseLine:e,layout:f,connectNulls:g=!1}=a,h=((a,b)=>{if("function"==typeof a)return a;var c="curve".concat(v(a));return("curveMonotone"===c||"curveBump"===c)&&b?nh["".concat(c).concat("vertical"===b?"Y":"X")]:nh[c]||mW})(c,f),i=g?d.filter(ni):d;if(Array.isArray(e)){var j=g?e.filter(a=>ni(a)):e,k=i.map((a,b)=>ng(ng({},a),{},{base:j[b]}));return(b="vertical"===f?nd().y(nk).x1(nj).x0(a=>a.base.x):nd().x(nj).y1(nk).y0(a=>a.base.y)).defined(ni).curve(h),b(k)}return(b="vertical"===f&&o(e)?nd().y(nk).x1(nj).x0(e):o(e)?nd().x(nj).y1(nk).y0(e):nc().x(nj).y(nk)).defined(ni).curve(h),b(i)})(a):d;return f.createElement("path",ne({},lV(a,!1),((a,b)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var c=a;if((0,f.isValidElement)(a)&&(c=a.props),"object"!=typeof c&&"function"!=typeof c)return null;var d={};return Object.keys(c).forEach(a=>{lO.includes(a)&&(d[a]=b||(b=>c[a](c,b)))}),d})(a),{className:g("recharts-curve",b),d:null===h?void 0:h,ref:e}))},nm={isSsr:!0};function nn(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function no(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nn(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nn(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var np={widthCache:{},cacheCount:0},nq={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nr="recharts_measurement_span",ns=function(a){var b,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==a||nm.isSsr)return{width:0,height:0};var d=(Object.keys(b=no({},c)).forEach(a=>{b[a]||delete b[a]}),b),e=JSON.stringify({text:a,copyStyle:d});if(np.widthCache[e])return np.widthCache[e];try{var f=document.getElementById(nr);f||((f=document.createElement("span")).setAttribute("id",nr),f.setAttribute("aria-hidden","true"),document.body.appendChild(f));var g=no(no({},nq),d);Object.assign(f.style,g),f.textContent="".concat(a);var h=f.getBoundingClientRect(),i={width:h.width,height:h.height};return np.widthCache[e]=i,++np.cacheCount>2e3&&(np.cacheCount=0,np.widthCache={}),i}catch(a){return{width:0,height:0}}},nt=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nu=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nv=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nw=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nx={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},ny=Object.keys(nx);class nz{static parse(a){var b,[,c,d]=null!=(b=nw.exec(a))?b:[];return new nz(parseFloat(c),null!=d?d:"")}constructor(a,b){this.num=a,this.unit=b,this.num=a,this.unit=b,m(a)&&(this.unit=""),""===b||nv.test(b)||(this.num=NaN,this.unit=""),ny.includes(b)&&(this.num=a*nx[b],this.unit="px")}add(a){return this.unit!==a.unit?new nz(NaN,""):new nz(this.num+a.num,this.unit)}subtract(a){return this.unit!==a.unit?new nz(NaN,""):new nz(this.num-a.num,this.unit)}multiply(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new nz(NaN,""):new nz(this.num*a.num,this.unit||a.unit)}divide(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new nz(NaN,""):new nz(this.num/a.num,this.unit||a.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return m(this.num)}}function nA(a){if(a.includes("NaN"))return"NaN";for(var b=a;b.includes("*")||b.includes("/");){var c,[,d,e,f]=null!=(c=nt.exec(b))?c:[],g=nz.parse(null!=d?d:""),h=nz.parse(null!=f?f:""),i="*"===e?g.multiply(h):g.divide(h);if(i.isNaN())return"NaN";b=b.replace(nt,i.toString())}for(;b.includes("+")||/.-\d+(?:\.\d+)?/.test(b);){var j,[,k,l,m]=null!=(j=nu.exec(b))?j:[],n=nz.parse(null!=k?k:""),o=nz.parse(null!=m?m:""),p="+"===l?n.add(o):n.subtract(o);if(p.isNaN())return"NaN";b=b.replace(nu,p.toString())}return b}var nB=/\(([^()]*)\)/;function nC(a){var b=function(a){try{var b;return b=a.replace(/\s+/g,""),b=function(a){for(var b,c=a;null!=(b=nB.exec(c));){var[,d]=b;c=c.replace(nB,nA(d))}return c}(b),b=nA(b)}catch(a){return"NaN"}}(a.slice(5,-1));return"NaN"===b?"":b}var nD=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nE=["dx","dy","angle","className","breakAll"];function nF(){return(nF=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nG(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var nH=/[ \f\n\r\t\v\u2028\u2029]+/,nI=a=>{var{children:b,breakAll:c,style:d}=a;try{let a;var e=[];a=b,null==a||(e=c?b.toString().split(""):b.toString().split(nH));var f=e.map(a=>({word:a,width:ns(a,d).width})),g=c?0:ns("\xa0",d).width;return{wordsWithComputedWidth:f,spaceWidth:g}}catch(a){return null}},nJ=a=>[{words:null==a?[]:a.toString().split(nH)}],nK="#808080",nL=(0,f.forwardRef)((a,b)=>{var c,{x:d=0,y:e=0,lineHeight:h="1em",capHeight:i="0.71em",scaleToFit:j=!1,textAnchor:k="start",verticalAnchor:l="end",fill:m=nK}=a,n=nG(a,nD),q=(0,f.useMemo)(()=>(a=>{var{width:b,scaleToFit:c,children:d,style:e,breakAll:f,maxLines:g}=a;if((b||c)&&!nm.isSsr){var h=nI({breakAll:f,children:d,style:e});if(!h)return nJ(d);var{wordsWithComputedWidth:i,spaceWidth:j}=h;return((a,b,c,d,e)=>{var f,{maxLines:g,children:h,style:i,breakAll:j}=a,k=o(g),l=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return a.reduce((a,b)=>{var{word:f,width:g}=b,h=a[a.length-1];return h&&(null==d||e||h.width+g+c<Number(d))?(h.words.push(f),h.width+=g+c):a.push({words:[f],width:g}),a},[])},m=l(b),n=a=>a.reduce((a,b)=>a.width>b.width?a:b);if(!k||e||!(m.length>g||n(m).width>Number(d)))return m;for(var p=a=>{var b=l(nI({breakAll:j,style:i,children:h.slice(0,a)+"…"}).wordsWithComputedWidth);return[b.length>g||n(b).width>Number(d),b]},q=0,r=h.length-1,s=0;q<=r&&s<=h.length-1;){var t=Math.floor((q+r)/2),[u,v]=p(t-1),[w]=p(t);if(u||w||(q=t+1),u&&w&&(r=t-1),!u&&w){f=v;break}s++}return f||m})({breakAll:f,children:d,maxLines:g,style:e},i,j,b,c)}return nJ(d)})({breakAll:n.breakAll,children:n.children,maxLines:n.maxLines,scaleToFit:j,style:n.style,width:n.width}),[n.breakAll,n.children,n.maxLines,j,n.style,n.width]),{dx:r,dy:s,angle:t,className:u,breakAll:v}=n,w=nG(n,nE);if(!p(d)||!p(e))return null;var x=d+(o(r)?r:0),y=e+(o(s)?s:0);switch(l){case"start":c=nC("calc(".concat(i,")"));break;case"middle":c=nC("calc(".concat((q.length-1)/2," * -").concat(h," + (").concat(i," / 2))"));break;default:c=nC("calc(".concat(q.length-1," * -").concat(h,")"))}var z=[];if(j){var A=q[0].width,{width:B}=n;z.push("scale(".concat(o(B)?B/A:1,")"))}return t&&z.push("rotate(".concat(t,", ").concat(x,", ").concat(y,")")),z.length&&(w.transform=z.join(" ")),f.createElement("text",nF({},lV(w,!0),{ref:b,x:x,y:y,className:g("recharts-text",u),textAnchor:k,fill:m.includes("url")?nK:m}),q.map((a,b)=>{var d=a.words.join(v?"":" ");return f.createElement("tspan",{x:x,dy:0===b?c:h,key:"".concat(d,"-").concat(b)},d)}))});nL.displayName="Text";var nM=a=>null;nM.displayName="Cell";var nN=c(98583),nO=c.n(nN),nP=c(24108),nQ=c.n(nP),nR=(a,b)=>[0,3*a,3*b-6*a,3*a-3*b+1],nS=(a,b)=>a.map((a,c)=>a*b**c).reduce((a,b)=>a+b),nT=(a,b)=>c=>nS(nR(a,b),c),nU=function(){let a,b;for(var c,d,e,f,g=arguments.length,h=Array(g),i=0;i<g;i++)h[i]=arguments[i];if(1===h.length)switch(h[0]){case"linear":[c,e,d,f]=[0,0,1,1];break;case"ease":[c,e,d,f]=[.25,.1,.25,1];break;case"ease-in":[c,e,d,f]=[.42,0,1,1];break;case"ease-out":[c,e,d,f]=[.42,0,.58,1];break;case"ease-in-out":[c,e,d,f]=[0,0,.58,1];break;default:var j=h[0].split("(");"cubic-bezier"===j[0]&&4===j[1].split(")")[0].split(",").length&&([c,e,d,f]=j[1].split(")")[0].split(",").map(a=>parseFloat(a)))}else 4===h.length&&([c,e,d,f]=h);var k=nT(c,d),l=nT(e,f),m=(a=c,b=d,c=>nS([...nR(a,b).map((a,b)=>a*b).slice(1),0],c)),n=a=>a>1?1:a<0?0:a,o=a=>{for(var b=a>1?1:a,c=b,d=0;d<8;++d){var e=k(c)-b,f=m(c);if(1e-4>Math.abs(e-b)||f<1e-4)break;c=n(c-e/f)}return l(c)};return o.isStepper=!1,o},nV=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:b=100,damping:c=8,dt:d=17}=a,e=(a,e,f)=>{var g=f+(-(a-e)*b-f*c)*d/1e3,h=f*d/1e3+a;return 1e-4>Math.abs(h-e)&&1e-4>Math.abs(g)?[e,0]:[h,g]};return e.isStepper=!0,e.dt=d,e};function nW(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nX(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nW(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nW(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var nY=(a,b)=>Object.keys(b).reduce((c,d)=>nX(nX({},c),{},{[d]:a(d,b[d])}),{});function nZ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n$(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nZ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nZ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var n_=(a,b,c)=>a+(b-a)*c,n0=a=>{var{from:b,to:c}=a;return b!==c},n1=(a,b,c)=>{var d=nY((b,c)=>{if(n0(c)){var[d,e]=a(c.from,c.to,c.velocity);return n$(n$({},c),{},{from:d,velocity:e})}return c},b);return c<1?nY((a,b)=>n0(b)?n$(n$({},b),{},{velocity:n_(b.velocity,d[a].velocity,c),from:n_(b.from,d[a].from,c)}):b,b):n1(a,d,c-1)};class n2{setTimeout(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,c=performance.now(),d=null,e=f=>{f-c>=b?a(f):"function"==typeof requestAnimationFrame&&(d=requestAnimationFrame(e))};return d=requestAnimationFrame(e),()=>{cancelAnimationFrame(d)}}}var n3=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function n4(){return(n4=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function n5(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n6(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?n5(Object(c),!0).forEach(function(b){n7(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):n5(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function n7(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class n8 extends f.PureComponent{constructor(a,b){super(a,b),n7(this,"mounted",!1),n7(this,"manager",null),n7(this,"stopJSAnimation",null),n7(this,"unSubscribe",null);var{isActive:c,attributeName:d,from:e,to:f,children:g,duration:h,animationManager:i}=this.props;if(this.manager=i,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!c||h<=0){this.state={style:{}},"function"==typeof g&&(this.state={style:f});return}if(e){if("function"==typeof g){this.state={style:e};return}this.state={style:d?{[d]:e}:e}}else this.state={style:{}}}componentDidMount(){var{isActive:a,canBegin:b}=this.props;this.mounted=!0,a&&b&&this.runAnimation(this.props)}componentDidUpdate(a){var{isActive:b,canBegin:c,attributeName:d,shouldReAnimate:e,to:f,from:g}=this.props,{style:h}=this.state;if(c){if(!b){this.state&&h&&(d&&h[d]!==f||!d&&h!==f)&&this.setState({style:d?{[d]:f}:f});return}if(!nQ()(a.to,f)||!a.canBegin||!a.isActive){var i=!a.canBegin||!a.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var j=i||e?g:a.to;this.state&&h&&(d&&h[d]!==j||!d&&h!==j)&&this.setState({style:d?{[d]:j}:j}),this.runAnimation(n6(n6({},this.props),{},{from:j,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:a}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),a&&a()}handleStyleChange(a){this.changeStyle(a)}changeStyle(a){this.mounted&&this.setState({style:a})}runJSAnimation(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,{from:A,to:B,duration:C,easing:D,begin:E,onAnimationEnd:F,onAnimationStart:G}=a,H=(w=(a=>{if("string"==typeof a)switch(a){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return nU(a);case"spring":return nV();default:if("cubic-bezier"===a.split("(")[0])return nU(a)}return"function"==typeof a?a:null})(D),x=this.changeStyle,y=this.manager.getTimeoutController(),z=[Object.keys(A),Object.keys(B)].reduce((a,b)=>a.filter(a=>b.includes(a))),!0===w.isStepper?(b=A,c=B,d=w,e=z,f=x,g=y,i=e.reduce((a,d)=>n$(n$({},a),{},{[d]:{from:b[d],velocity:0,to:c[d]}}),{}),j=null,k=a=>{h||(h=a);var e=(a-h)/d.dt;i=n1(d,i,e),f(n$(n$(n$({},b),c),nY((a,b)=>b.from,i))),h=a,Object.values(i).filter(n0).length&&(j=g.setTimeout(k))},()=>(j=g.setTimeout(k),()=>{j()})):(l=A,m=B,n=w,o=C,p=z,q=x,r=y,t=null,u=p.reduce((a,b)=>n$(n$({},a),{},{[b]:[l[b],m[b]]}),{}),v=a=>{s||(s=a);var b=(a-s)/o,c=nY((a,c)=>n_(...c,n(b)),u);if(q(n$(n$(n$({},l),m),c)),b<1)t=r.setTimeout(v);else{var d=nY((a,b)=>n_(...b,n(1)),u);q(n$(n$(n$({},l),m),d))}},()=>(t=r.setTimeout(v),()=>{t()}))),I=()=>{this.stopJSAnimation=H()};this.manager.start([G,E,I,C,F])}runAnimation(a){let b;var{begin:c,duration:d,attributeName:e,to:f,easing:g,onAnimationStart:h,onAnimationEnd:i,children:j}=a;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof g||"function"==typeof j||"spring"===g)return void this.runJSAnimation(a);var k=e?{[e]:f}:f,l=(b=Object.keys(k),b.map(a=>"".concat(a.replace(/([A-Z])/g,a=>"-".concat(a.toLowerCase()))," ").concat(d,"ms ").concat(g)).join(","));this.manager.start([h,c,n6(n6({},k),{},{transition:l}),d,i])}render(){var a=this.props,{children:b,begin:c,duration:d,attributeName:e,easing:g,isActive:h,from:i,to:j,canBegin:k,onAnimationEnd:l,shouldReAnimate:m,onAnimationReStart:n,animationManager:o}=a,p=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,n3),q=f.Children.count(b),r=this.state.style;if("function"==typeof b)return b(r);if(!h||0===q||d<=0)return b;var s=a=>{var{style:b={},className:c}=a.props;return(0,f.cloneElement)(a,n6(n6({},p),{},{style:n6(n6({},b),r),className:c}))};return 1===q?s(f.Children.only(b)):f.createElement("div",null,f.Children.map(b,a=>s(a)))}}n7(n8,"displayName","Animate"),n7(n8,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var n9=(0,f.createContext)(null);function oa(a){var b,c,d,e,g,h,i,j=(0,f.useContext)(n9);return f.createElement(n8,n4({},a,{animationManager:null!=(h=null!=(i=a.animationManager)?i:j)?h:(b=new n2,c=()=>null,d=!1,e=null,g=a=>{if(!d){if(Array.isArray(a)){if(!a.length)return;var[f,...h]=a;if("number"==typeof f){e=b.setTimeout(g.bind(null,h),f);return}g(f),e=b.setTimeout(g.bind(null,h));return}"object"==typeof a&&c(a),"function"==typeof a&&a()}},{stop:()=>{d=!0},start:a=>{d=!1,e&&(e(),e=null),g(a)},subscribe:a=>(c=a,()=>{c=()=>null}),getTimeoutController:()=>b})}))}function ob(){return(ob=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var oc=(a,b,c,d,e)=>{var f,g=Math.min(Math.abs(c)/2,Math.abs(d)/2),h=d>=0?1:-1,i=c>=0?1:-1,j=+(d>=0&&c>=0||d<0&&c<0);if(g>0&&e instanceof Array){for(var k=[0,0,0,0],l=0;l<4;l++)k[l]=e[l]>g?g:e[l];f="M".concat(a,",").concat(b+h*k[0]),k[0]>0&&(f+="A ".concat(k[0],",").concat(k[0],",0,0,").concat(j,",").concat(a+i*k[0],",").concat(b)),f+="L ".concat(a+c-i*k[1],",").concat(b),k[1]>0&&(f+="A ".concat(k[1],",").concat(k[1],",0,0,").concat(j,",\n        ").concat(a+c,",").concat(b+h*k[1])),f+="L ".concat(a+c,",").concat(b+d-h*k[2]),k[2]>0&&(f+="A ".concat(k[2],",").concat(k[2],",0,0,").concat(j,",\n        ").concat(a+c-i*k[2],",").concat(b+d)),f+="L ".concat(a+i*k[3],",").concat(b+d),k[3]>0&&(f+="A ".concat(k[3],",").concat(k[3],",0,0,").concat(j,",\n        ").concat(a,",").concat(b+d-h*k[3])),f+="Z"}else if(g>0&&e===+e&&e>0){var m=Math.min(g,e);f="M ".concat(a,",").concat(b+h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+i*m,",").concat(b,"\n            L ").concat(a+c-i*m,",").concat(b,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c,",").concat(b+h*m,"\n            L ").concat(a+c,",").concat(b+d-h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c-i*m,",").concat(b+d,"\n            L ").concat(a+i*m,",").concat(b+d,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a,",").concat(b+d-h*m," Z")}else f="M ".concat(a,",").concat(b," h ").concat(c," v ").concat(d," h ").concat(-c," Z");return f},od={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},oe=a=>{var b=mh(a,od),c=(0,f.useRef)(null),[d,e]=(0,f.useState)(-1);(0,f.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:h,y:i,width:j,height:k,radius:l,className:m}=b,{animationEasing:n,animationDuration:o,animationBegin:p,isAnimationActive:q,isUpdateAnimationActive:r}=b;if(h!==+h||i!==+i||j!==+j||k!==+k||0===j||0===k)return null;var s=g("recharts-rectangle",m);return r?f.createElement(oa,{canBegin:d>0,from:{width:j,height:k,x:h,y:i},to:{width:j,height:k,x:h,y:i},duration:o,animationEasing:n,isActive:r},a=>{var{width:e,height:g,x:h,y:i}=a;return f.createElement(oa,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:o,isActive:q,easing:n},f.createElement("path",ob({},lV(b,!0),{className:s,d:oc(h,i,e,g,l),ref:c})))}):f.createElement("path",ob({},lV(b,!0),{className:s,d:oc(h,i,j,k,l)}))};function of(){return(of=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var og=(a,b,c,d,e)=>{var f=c-d;return"M ".concat(a,",").concat(b)+"L ".concat(a+c,",").concat(b)+"L ".concat(a+c-f/2,",").concat(b+e)+"L ".concat(a+c-f/2-d,",").concat(b+e)+"L ".concat(a,",").concat(b," Z")},oh={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},oi=a=>{var b=mh(a,oh),c=(0,f.useRef)(),[d,e]=(0,f.useState)(-1);(0,f.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:h,y:i,upperWidth:j,lowerWidth:k,height:l,className:m}=b,{animationEasing:n,animationDuration:o,animationBegin:p,isUpdateAnimationActive:q}=b;if(h!==+h||i!==+i||j!==+j||k!==+k||l!==+l||0===j&&0===k||0===l)return null;var r=g("recharts-trapezoid",m);return q?f.createElement(oa,{canBegin:d>0,from:{upperWidth:0,lowerWidth:0,height:l,x:h,y:i},to:{upperWidth:j,lowerWidth:k,height:l,x:h,y:i},duration:o,animationEasing:n,isActive:q},a=>{var{upperWidth:e,lowerWidth:g,height:h,x:i,y:j}=a;return f.createElement(oa,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:o,easing:n},f.createElement("path",of({},lV(b,!0),{className:r,d:og(i,j,e,g,h),ref:c})))}):f.createElement("g",null,f.createElement("path",of({},lV(b,!0),{className:r,d:og(h,i,j,k,l)})))};function oj(){return(oj=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var ok=a=>{var{cx:b,cy:c,radius:d,angle:e,sign:f,isExternal:g,cornerRadius:h,cornerIsExternal:i}=a,j=h*(g?1:-1)+d,k=Math.asin(h/j)/cn,l=i?e:e+f*k,m=co(b,c,j,l);return{center:m,circleTangency:co(b,c,d,l),lineTangency:co(b,c,j*Math.cos(k*cn),i?e-f*k:e),theta:k}},ol=a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:f,endAngle:g}=a,h=((a,b)=>l(b-a)*Math.min(Math.abs(b-a),359.999))(f,g),i=f+h,j=co(b,c,e,f),k=co(b,c,e,i),m="M ".concat(j.x,",").concat(j.y,"\n    A ").concat(e,",").concat(e,",0,\n    ").concat(+(Math.abs(h)>180),",").concat(+(f>i),",\n    ").concat(k.x,",").concat(k.y,"\n  ");if(d>0){var n=co(b,c,d,f),o=co(b,c,d,i);m+="L ".concat(o.x,",").concat(o.y,"\n            A ").concat(d,",").concat(d,",0,\n            ").concat(+(Math.abs(h)>180),",").concat(+(f<=i),",\n            ").concat(n.x,",").concat(n.y," Z")}else m+="L ".concat(b,",").concat(c," Z");return m},om={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},on=a=>{var b,c=mh(a,om),{cx:d,cy:e,innerRadius:h,outerRadius:i,cornerRadius:j,forceCornerRadius:k,cornerIsExternal:m,startAngle:n,endAngle:o,className:p}=c;if(i<h||n===o)return null;var q=g("recharts-sector",p),r=i-h,t=s(j,r,0,!0);return b=t>0&&360>Math.abs(n-o)?(a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,cornerRadius:f,forceCornerRadius:g,cornerIsExternal:h,startAngle:i,endAngle:j}=a,k=l(j-i),{circleTangency:m,lineTangency:n,theta:o}=ok({cx:b,cy:c,radius:e,angle:i,sign:k,cornerRadius:f,cornerIsExternal:h}),{circleTangency:p,lineTangency:q,theta:r}=ok({cx:b,cy:c,radius:e,angle:j,sign:-k,cornerRadius:f,cornerIsExternal:h}),s=h?Math.abs(i-j):Math.abs(i-j)-o-r;if(s<0)return g?"M ".concat(n.x,",").concat(n.y,"\n        a").concat(f,",").concat(f,",0,0,1,").concat(2*f,",0\n        a").concat(f,",").concat(f,",0,0,1,").concat(-(2*f),",0\n      "):ol({cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:i,endAngle:j});var t="M ".concat(n.x,",").concat(n.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(m.x,",").concat(m.y,"\n    A").concat(e,",").concat(e,",0,").concat(+(s>180),",").concat(+(k<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(q.x,",").concat(q.y,"\n  ");if(d>0){var{circleTangency:u,lineTangency:v,theta:w}=ok({cx:b,cy:c,radius:d,angle:i,sign:k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),{circleTangency:x,lineTangency:y,theta:z}=ok({cx:b,cy:c,radius:d,angle:j,sign:-k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),A=h?Math.abs(i-j):Math.abs(i-j)-w-z;if(A<0&&0===f)return"".concat(t,"L").concat(b,",").concat(c,"Z");t+="L".concat(y.x,",").concat(y.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(x.x,",").concat(x.y,"\n      A").concat(d,",").concat(d,",0,").concat(+(A>180),",").concat(+(k>0),",").concat(u.x,",").concat(u.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(v.x,",").concat(v.y,"Z")}else t+="L".concat(b,",").concat(c,"Z");return t})({cx:d,cy:e,innerRadius:h,outerRadius:i,cornerRadius:Math.min(t,r/2),forceCornerRadius:k,cornerIsExternal:m,startAngle:n,endAngle:o}):ol({cx:d,cy:e,innerRadius:h,outerRadius:i,startAngle:n,endAngle:o}),f.createElement("path",oj({},lV(c,!0),{className:q,d:b}))};let oo=Math.cos,op=Math.sin,oq=Math.sqrt,or=Math.PI,os=2*or,ot={draw(a,b){let c=oq(b/or);a.moveTo(c,0),a.arc(0,0,c,0,os)}},ou=oq(1/3),ov=2*ou,ow=op(or/10)/op(7*or/10),ox=op(os/10)*ow,oy=-oo(os/10)*ow,oz=oq(3),oA=oq(3)/2,oB=1/oq(12),oC=(oB/2+1)*3;oq(3),oq(3);var oD=["type","size","sizeType"];function oE(){return(oE=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function oF(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oG(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oF(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oF(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var oH={symbolCircle:ot,symbolCross:{draw(a,b){let c=oq(b/5)/2;a.moveTo(-3*c,-c),a.lineTo(-c,-c),a.lineTo(-c,-3*c),a.lineTo(c,-3*c),a.lineTo(c,-c),a.lineTo(3*c,-c),a.lineTo(3*c,c),a.lineTo(c,c),a.lineTo(c,3*c),a.lineTo(-c,3*c),a.lineTo(-c,c),a.lineTo(-3*c,c),a.closePath()}},symbolDiamond:{draw(a,b){let c=oq(b/ov),d=c*ou;a.moveTo(0,-c),a.lineTo(d,0),a.lineTo(0,c),a.lineTo(-d,0),a.closePath()}},symbolSquare:{draw(a,b){let c=oq(b),d=-c/2;a.rect(d,d,c,c)}},symbolStar:{draw(a,b){let c=oq(.8908130915292852*b),d=ox*c,e=oy*c;a.moveTo(0,-c),a.lineTo(d,e);for(let b=1;b<5;++b){let f=os*b/5,g=oo(f),h=op(f);a.lineTo(h*c,-g*c),a.lineTo(g*d-h*e,h*d+g*e)}a.closePath()}},symbolTriangle:{draw(a,b){let c=-oq(b/(3*oz));a.moveTo(0,2*c),a.lineTo(-oz*c,-c),a.lineTo(oz*c,-c),a.closePath()}},symbolWye:{draw(a,b){let c=oq(b/oC),d=c/2,e=c*oB,f=c*oB+c,g=-d;a.moveTo(d,e),a.lineTo(d,f),a.lineTo(g,f),a.lineTo(-.5*d-oA*e,oA*d+-.5*e),a.lineTo(-.5*d-oA*f,oA*d+-.5*f),a.lineTo(-.5*g-oA*f,oA*g+-.5*f),a.lineTo(-.5*d+oA*e,-.5*e-oA*d),a.lineTo(-.5*d+oA*f,-.5*f-oA*d),a.lineTo(-.5*g+oA*f,-.5*f-oA*g),a.closePath()}}},oI=Math.PI/180,oJ=a=>{var{type:b="circle",size:c=64,sizeType:d="area"}=a,e=oG(oG({},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,oD)),{},{type:b,size:c,sizeType:d}),{className:h,cx:i,cy:j}=e,k=lV(e,!0);return i===+i&&j===+j&&c===+c?f.createElement("path",oE({},k,{className:g("recharts-symbols",h),transform:"translate(".concat(i,", ").concat(j,")"),d:(()=>{var a=oH["symbol".concat(v(b))]||ot;return(function(a,b){let c=null,d=m9(e);function e(){let e;if(c||(c=e=d()),a.apply(this,arguments).draw(c,+b.apply(this,arguments)),e)return c=null,e+""||null}return a="function"==typeof a?a:ch(a||ot),b="function"==typeof b?b:ch(void 0===b?64:+b),e.type=function(b){return arguments.length?(a="function"==typeof b?b:ch(b),e):a},e.size=function(a){return arguments.length?(b="function"==typeof a?a:ch(+a),e):b},e.context=function(a){return arguments.length?(c=null==a?null:a,e):c},e})().type(a).size(((a,b,c)=>{if("area"===b)return a;switch(c){case"cross":return 5*a*a/9;case"diamond":return .5*a*a/Math.sqrt(3);case"square":return a*a;case"star":var d=18*oI;return 1.25*a*a*(Math.tan(d)-Math.tan(2*d)*Math.tan(d)**2);case"triangle":return Math.sqrt(3)*a*a/4;case"wye":return(21-10*Math.sqrt(3))*a*a/8;default:return Math.PI*a*a/4}})(c,d,b))()})()})):null};oJ.registerSymbol=(a,b)=>{oH["symbol".concat(v(a))]=b};var oK=["option","shapeType","propTransformer","activeClassName","isActive"];function oL(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oM(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oL(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oL(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function oN(a,b){return oM(oM({},b),a)}function oO(a){var{shapeType:b,elementProps:c}=a;switch(b){case"rectangle":return f.createElement(oe,c);case"trapezoid":return f.createElement(oi,c);case"sector":return f.createElement(on,c);case"symbols":if("symbols"===b)return f.createElement(oJ,c);break;default:return null}}function oP(a){var b,{option:c,shapeType:d,propTransformer:e=oN,activeClassName:g="recharts-active-shape",isActive:h}=a,i=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,oK);if((0,f.isValidElement)(c))b=(0,f.cloneElement)(c,oM(oM({},i),(0,f.isValidElement)(c)?c.props:c));else if("function"==typeof c)b=c(i);else if(nO()(c)&&"boolean"!=typeof c){var j=e(c,i);b=f.createElement(oO,{shapeType:d,elementProps:j})}else b=f.createElement(oO,{shapeType:d,elementProps:i});return h?f.createElement(mN,{className:g},b):b}var oQ=(a,b)=>{var c=b7();return(d,e)=>f=>{null==a||a(d,e,f),c(bt({activeIndex:String(e),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}},oR=a=>{var b=b7();return(c,d)=>e=>{null==a||a(c,d,e),b(bu())}},oS=(a,b)=>{var c=b7();return(d,e)=>f=>{null==a||a(d,e,f),c(bw({activeIndex:String(e),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}};function oT(a){var{fn:b,args:c}=a;return b7(),cR(),null}function oU(a){var{legendPayload:b}=a;return b7(),cR(),null}function oV(a){var{legendPayload:b}=a;return b7(),cb(cZ),null}function oW(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",c=(0,f.useRef)(r(b)),d=(0,f.useRef)(a);return d.current!==a&&(c.current=r(b),d.current=a),c.current}var oX=["onMouseEnter","onClick","onMouseLeave"];function oY(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oZ(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oY(Object(c),!0).forEach(function(b){o$(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oY(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function o$(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function o_(){return(o_=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function o0(a){var b=(0,f.useMemo)(()=>lV(a,!1),[a]),c=(0,f.useMemo)(()=>lU(a.children,nM),[a.children]),d=(0,f.useMemo)(()=>({name:a.name,nameKey:a.nameKey,tooltipType:a.tooltipType,data:a.data,dataKey:a.dataKey,cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,minAngle:a.minAngle,paddingAngle:a.paddingAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius,cornerRadius:a.cornerRadius,legendType:a.legendType,fill:a.fill,presentationProps:b}),[a.cornerRadius,a.cx,a.cy,a.data,a.dataKey,a.endAngle,a.innerRadius,a.minAngle,a.name,a.nameKey,a.outerRadius,a.paddingAngle,a.startAngle,a.tooltipType,a.legendType,a.fill,b]),e=cb(a=>mG(a,d,c));return f.createElement(oV,{legendPayload:e})}function o1(a){var{dataKey:b,nameKey:c,sectors:d,stroke:e,strokeWidth:f,fill:g,name:h,hide:i,tooltipType:j}=a;return{dataDefinedOnItem:null==d?void 0:d.map(a=>a.tooltipPayload),positions:null==d?void 0:d.map(a=>a.tooltipPosition),settings:{stroke:e,strokeWidth:f,fill:g,dataKey:b,nameKey:c,name:cC(h,b),hide:i,type:j,color:g,unit:""}}}function o2(a){var{sectors:b,props:c,showLabels:d}=a,{label:e,labelLine:h,dataKey:i}=c;if(!d||!e||!b)return null;var j=lV(c,!1),k=lV(e,!1),l=lV(h,!1),m="object"==typeof e&&"offsetRadius"in e&&e.offsetRadius||20,n=b.map((a,b)=>{var c,d,n=(a.startAngle+a.endAngle)/2,o=co(a.cx,a.cy,a.outerRadius+m,n),p=oZ(oZ(oZ(oZ({},j),a),{},{stroke:"none"},k),{},{index:b,textAnchor:(c=o.x)>(d=a.cx)?"start":c<d?"end":"middle"},o),q=oZ(oZ(oZ(oZ({},j),a),{},{fill:"none",stroke:a.fill},l),{},{index:b,points:[co(a.cx,a.cy,a.outerRadius,n),o],key:"line"});return f.createElement(mN,{key:"label-".concat(a.startAngle,"-").concat(a.endAngle,"-").concat(a.midAngle,"-").concat(b)},h&&((a,b)=>{if(f.isValidElement(a))return f.cloneElement(a,b);if("function"==typeof a)return a(b);var c=g("recharts-pie-label-line","boolean"!=typeof a?a.className:"");return f.createElement(nl,o_({},b,{type:"linear",className:c}))})(h,q),((a,b,c)=>{if(f.isValidElement(a))return f.cloneElement(a,b);var d=c;if("function"==typeof a&&(d=a(b),f.isValidElement(d)))return d;var e=g("recharts-pie-label-text","boolean"!=typeof a&&"function"!=typeof a?a.className:"");return f.createElement(nL,o_({},b,{alignmentBaseline:"middle",className:e}),d)})(e,p,cs(a,i)))});return f.createElement(mN,{className:"recharts-pie-labels"},n)}function o3(a){var{sectors:b,activeShape:c,inactiveShape:d,allOtherPieProps:e,showLabels:g}=a,h=cb(kg),{onMouseEnter:i,onClick:j,onMouseLeave:k}=e,l=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(e,oX),m=oQ(i,e.dataKey),n=oR(k),o=oS(j,e.dataKey);return null==b?null:f.createElement(f.Fragment,null,b.map((a,g)=>{if((null==a?void 0:a.startAngle)===0&&(null==a?void 0:a.endAngle)===0&&1!==b.length)return null;var i=c&&String(g)===h,j=i?c:h?d:null,k=oZ(oZ({},a),{},{stroke:a.stroke,tabIndex:-1,[cJ]:g,[cK]:e.dataKey});return f.createElement(mN,o_({tabIndex:-1,className:"recharts-pie-sector"},lP(l,a,g),{onMouseEnter:m(a,g),onMouseLeave:n(a,g),onClick:o(a,g),key:"sector-".concat(null==a?void 0:a.startAngle,"-").concat(null==a?void 0:a.endAngle,"-").concat(a.midAngle,"-").concat(g)}),f.createElement(oP,o_({option:j,isActive:i,shapeType:"sector"},k)))}),f.createElement(o2,{sectors:b,props:e,showLabels:g}))}function o4(a){var{props:b,previousSectorsRef:c}=a,{sectors:d,isAnimationActive:e,animationBegin:g,animationDuration:h,animationEasing:i,activeShape:j,inactiveShape:l,onAnimationStart:m,onAnimationEnd:n}=b,o=oW(b,"recharts-pie-"),p=c.current,[q,r]=(0,f.useState)(!0),s=(0,f.useCallback)(()=>{"function"==typeof n&&n(),r(!1)},[n]),t=(0,f.useCallback)(()=>{"function"==typeof m&&m(),r(!0)},[m]);return f.createElement(oa,{begin:g,duration:h,isActive:e,easing:i,from:{t:0},to:{t:1},onAnimationStart:t,onAnimationEnd:s,key:o},a=>{var{t:e}=a,g=[],h=(d&&d[0]).startAngle;return d.forEach((a,b)=>{var c=p&&p[b],d=b>0?k()(a,"paddingAngle",0):0;if(c){var f=u(c.endAngle-c.startAngle,a.endAngle-a.startAngle),i=oZ(oZ({},a),{},{startAngle:h+d,endAngle:h+f(e)+d});g.push(i),h=i.endAngle}else{var{endAngle:j,startAngle:l}=a,m=u(0,j-l)(e),n=oZ(oZ({},a),{},{startAngle:h+d,endAngle:h+m+d});g.push(n),h=n.endAngle}}),c.current=g,f.createElement(mN,null,f.createElement(o3,{sectors:g,activeShape:j,inactiveShape:l,allOtherPieProps:b,showLabels:!q}))})}function o5(a){var{sectors:b,isAnimationActive:c,activeShape:d,inactiveShape:e}=a,g=(0,f.useRef)(null),h=g.current;return c&&b&&b.length&&(!h||h!==b)?f.createElement(o4,{props:a,previousSectorsRef:g}):f.createElement(o3,{sectors:b,activeShape:d,inactiveShape:e,allOtherPieProps:a,showLabels:!0})}function o6(a){var{hide:b,className:c,rootTabIndex:d}=a,e=g("recharts-pie",c);return b?null:f.createElement(mN,{tabIndex:d,className:e},f.createElement(o5,a))}var o7={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!nm.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function o8(a){var b=mh(a,o7),c=(0,f.useMemo)(()=>lU(a.children,nM),[a.children]),d=lV(b,!1),e=(0,f.useMemo)(()=>({name:b.name,nameKey:b.nameKey,tooltipType:b.tooltipType,data:b.data,dataKey:b.dataKey,cx:b.cx,cy:b.cy,startAngle:b.startAngle,endAngle:b.endAngle,minAngle:b.minAngle,paddingAngle:b.paddingAngle,innerRadius:b.innerRadius,outerRadius:b.outerRadius,cornerRadius:b.cornerRadius,legendType:b.legendType,fill:b.fill,presentationProps:d}),[b.cornerRadius,b.cx,b.cy,b.data,b.dataKey,b.endAngle,b.innerRadius,b.minAngle,b.name,b.nameKey,b.outerRadius,b.paddingAngle,b.startAngle,b.tooltipType,b.legendType,b.fill,d]),g=cb(a=>mI(a,e,c));return f.createElement(f.Fragment,null,f.createElement(oT,{fn:o1,args:oZ(oZ({},b),{},{sectors:g})}),f.createElement(o6,o_({},b,{sectors:g})))}class o9 extends f.PureComponent{constructor(){super(...arguments),o$(this,"id",r("recharts-pie-"))}render(){return f.createElement(f.Fragment,null,f.createElement(mK,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),f.createElement(o0,this.props),f.createElement(o8,this.props),this.props.children)}}o$(o9,"displayName","Pie"),o$(o9,"defaultProps",o7);var pa=c(51215);function pb(){return(pb=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pc(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pd(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pc(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pc(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pe(a){return Array.isArray(a)&&p(a[0])&&p(a[1])?a.join(" ~ "):a}var pf=a=>{var{separator:b=" : ",contentStyle:c={},itemStyle:d={},labelStyle:e={},payload:h,formatter:i,itemSorter:j,wrapperClassName:k,labelClassName:l,label:m,labelFormatter:n,accessibilityLayer:o=!1}=a,q=pd({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},c),r=pd({margin:0},e),s=null!=m,t=s?m:"",u=g("recharts-default-tooltip",k),v=g("recharts-tooltip-label",l);return s&&n&&null!=h&&(t=n(m,h)),f.createElement("div",pb({className:u,style:q},o?{role:"status","aria-live":"assertive"}:{}),f.createElement("p",{className:v,style:r},f.isValidElement(t)?t:"".concat(t)),(()=>{if(h&&h.length){var a=(j?cd()(h,j):h).map((a,c)=>{if("none"===a.type)return null;var e=a.formatter||i||pe,{value:g,name:j}=a,k=g,l=j;if(e){var m=e(g,j,a,c,h);if(Array.isArray(m))[k,l]=m;else{if(null==m)return null;k=m}}var n=pd({display:"block",paddingTop:4,paddingBottom:4,color:a.color||"#000"},d);return f.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(c),style:n},p(l)?f.createElement("span",{className:"recharts-tooltip-item-name"},l):null,p(l)?f.createElement("span",{className:"recharts-tooltip-item-separator"},b):null,f.createElement("span",{className:"recharts-tooltip-item-value"},k),f.createElement("span",{className:"recharts-tooltip-item-unit"},a.unit||""))});return f.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},a)}return null})())},pg="recharts-tooltip-wrapper",ph={visibility:"hidden"};function pi(a){var{allowEscapeViewBox:b,coordinate:c,key:d,offsetTopLeft:e,position:f,reverseDirection:g,tooltipDimension:h,viewBox:i,viewBoxDimension:j}=a;if(f&&o(f[d]))return f[d];var k=c[d]-h-(e>0?e:0),l=c[d]+e;if(b[d])return g[d]?k:l;var m=i[d];return null==m?0:g[d]?k<m?Math.max(l,m):Math.max(k,m):null==j?0:l+h>m+j?Math.max(k,m):Math.max(l,m)}function pj(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pk(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pj(Object(c),!0).forEach(function(b){pl(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pj(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pl(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class pm extends f.PureComponent{constructor(){super(...arguments),pl(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),pl(this,"handleKeyDown",a=>{if("Escape"===a.key){var b,c,d,e;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(b=null==(c=this.props.coordinate)?void 0:c.x)?b:0,y:null!=(d=null==(e=this.props.coordinate)?void 0:e.y)?d:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var a,b;this.state.dismissed&&((null==(a=this.props.coordinate)?void 0:a.x)!==this.state.dismissedAtCoordinate.x||(null==(b=this.props.coordinate)?void 0:b.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:a,allowEscapeViewBox:b,animationDuration:c,animationEasing:d,children:e,coordinate:h,hasPayload:i,isAnimationActive:j,offset:k,position:l,reverseDirection:m,useTranslate3d:n,viewBox:p,wrapperStyle:q,lastBoundingBox:r,innerRef:s,hasPortalFromProps:t}=this.props,{cssClasses:u,cssProperties:v}=function(a){var b,c,d,{allowEscapeViewBox:e,coordinate:f,offsetTopLeft:h,position:i,reverseDirection:j,tooltipBox:k,useTranslate3d:l,viewBox:m}=a;return{cssProperties:b=k.height>0&&k.width>0&&f?function(a){var{translateX:b,translateY:c,useTranslate3d:d}=a;return{transform:d?"translate3d(".concat(b,"px, ").concat(c,"px, 0)"):"translate(".concat(b,"px, ").concat(c,"px)")}}({translateX:c=pi({allowEscapeViewBox:e,coordinate:f,key:"x",offsetTopLeft:h,position:i,reverseDirection:j,tooltipDimension:k.width,viewBox:m,viewBoxDimension:m.width}),translateY:d=pi({allowEscapeViewBox:e,coordinate:f,key:"y",offsetTopLeft:h,position:i,reverseDirection:j,tooltipDimension:k.height,viewBox:m,viewBoxDimension:m.height}),useTranslate3d:l}):ph,cssClasses:function(a){var{coordinate:b,translateX:c,translateY:d}=a;return g(pg,{["".concat(pg,"-right")]:o(c)&&b&&o(b.x)&&c>=b.x,["".concat(pg,"-left")]:o(c)&&b&&o(b.x)&&c<b.x,["".concat(pg,"-bottom")]:o(d)&&b&&o(b.y)&&d>=b.y,["".concat(pg,"-top")]:o(d)&&b&&o(b.y)&&d<b.y})}({translateX:c,translateY:d,coordinate:f})}}({allowEscapeViewBox:b,coordinate:h,offsetTopLeft:k,position:l,reverseDirection:m,tooltipBox:{height:r.height,width:r.width},useTranslate3d:n,viewBox:p}),w=t?{}:pk(pk({transition:j&&a?"transform ".concat(c,"ms ").concat(d):void 0},v),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&i?"visible":"hidden",position:"absolute",top:0,left:0}),x=pk(pk({},w),{},{visibility:!this.state.dismissed&&a&&i?"visible":"hidden"},q);return f.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:u,style:x,ref:s},e)}}var pn=c(83570),po=c.n(pn),pp=["x","y","top","left","width","height","className"];function pq(){return(pq=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pr(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var ps=a=>{var{x:b=0,y:c=0,top:d=0,left:e=0,width:h=0,height:i=0,className:j}=a,k=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pr(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pr(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({x:b,y:c,top:d,left:e,width:h,height:i},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,pp));return o(b)&&o(c)&&o(h)&&o(i)&&o(d)&&o(e)?f.createElement("path",pq({},lV(k,!0),{className:g("recharts-cross",j),d:"M".concat(b,",").concat(d,"v").concat(i,"M").concat(e,",").concat(c,"h").concat(h)})):null};function pt(a){var{cx:b,cy:c,radius:d,startAngle:e,endAngle:f}=a;return{points:[co(b,c,d,e),co(b,c,d,f)],cx:b,cy:c,radius:d,startAngle:e,endAngle:f}}function pu(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pv(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pu(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pu(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pw(){return(pw=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function px(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function py(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?px(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):px(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pz(a){var b,c,d,{coordinate:e,payload:h,index:i,offset:j,tooltipAxisBandSize:k,layout:l,cursor:m,tooltipEventType:n,chartName:o}=a;if(!m||!e||"ScatterChart"!==o&&"axis"!==n)return null;if("ScatterChart"===o)c=e,d=ps;else if("BarChart"===o)b=k/2,c={stroke:"none",fill:"#ccc",x:"horizontal"===l?e.x-b:j.left+.5,y:"horizontal"===l?j.top+.5:e.y-b,width:"horizontal"===l?k:j.width-1,height:"horizontal"===l?j.height-1:k},d=oe;else if("radial"===l){var{cx:p,cy:q,radius:r,startAngle:s,endAngle:t}=pt(e);c={cx:p,cy:q,startAngle:s,endAngle:t,innerRadius:r,outerRadius:r},d=on}else c={points:function(a,b,c){var d,e,f,g;if("horizontal"===a)f=d=b.x,e=c.top,g=c.top+c.height;else if("vertical"===a)g=e=b.y,d=c.left,f=c.left+c.width;else if(null!=b.cx&&null!=b.cy)if("centric"!==a)return pt(b);else{var{cx:h,cy:i,innerRadius:j,outerRadius:k,angle:l}=b,m=co(h,i,j,l),n=co(h,i,k,l);d=m.x,e=m.y,f=n.x,g=n.y}return[{x:d,y:e},{x:f,y:g}]}(l,e,j)},d=nl;var u="object"==typeof m&&"className"in m?m.className:void 0,v=py(py(py(py({stroke:"#ccc",pointerEvents:"none"},j),c),lV(m,!1)),{},{payload:h,payloadIndex:i,className:g("recharts-tooltip-cursor",u)});return(0,f.isValidElement)(m)?(0,f.cloneElement)(m,v):(0,f.createElement)(d,v)}function pA(a){var b,c,d,e=(b=cb(jK),c=cb(kb),d=cb(j8),cA(pv(pv({},b),{},{scale:d}),c)),g=cW(),h=c$(),i=cb(hD);return f.createElement(pz,pw({},a,{coordinate:a.coordinate,index:a.index,payload:a.payload,offset:g,layout:h,tooltipAxisBandSize:e,chartName:i}))}function pB(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pC(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pB(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pB(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pD(a){return a.dataKey}var pE=[],pF={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!nm.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function pG(a){var b,c,d,e=mh(a,pF),{active:g,allowEscapeViewBox:h,animationDuration:i,animationEasing:j,content:k,filterNull:l,isAnimationActive:m,offset:n,payloadUniqBy:o,position:p,reverseDirection:q,useTranslate3d:r,wrapperStyle:s,cursor:t,shared:u,trigger:v,defaultIndex:w,portal:x,axisId:y}=e;b7();var z="number"==typeof w?String(w):w,A=cU(),B=lW(),C=cb(a=>jv(a,u)),{activeIndex:D,isActive:E}=cb(a=>kz(a,C,v,z)),F=cb(a=>ky(a,C,v,z)),G=cb(a=>kx(a,C,v,z)),H=cb(a=>kw(a,C,v,z)),I=(0,f.useContext)(l5),J=null!=g?g:E,[K,L]=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[b,c]=(0,f.useState)({height:0,left:0,top:0,width:0}),d=(0,f.useCallback)(a=>{if(null!=a){var d=a.getBoundingClientRect(),e={height:d.height,left:d.left,top:d.top,width:d.width};(Math.abs(e.height-b.height)>1||Math.abs(e.left-b.left)>1||Math.abs(e.top-b.top)>1||Math.abs(e.width-b.width)>1)&&c({height:e.height,left:e.left,top:e.top,width:e.width})}},[b.width,b.height,b.top,b.left,...a]);return[b,d]}([F,J]),M="axis"===C?G:void 0;cb(a=>((a,b,c)=>{if(null!=b){var d=jE(a);return"axis"===b?"hover"===c?d.axisInteraction.hover.dataKey:d.axisInteraction.click.dataKey:"hover"===c?d.itemInteraction.hover.dataKey:d.itemInteraction.click.dataKey}})(a,C,v)),cb(hG),cb(hE),cb(hF),null==(b=cb(l4))||b.active;var N=null!=x?x:I;if(null==N)return null;var O=null!=F?F:pE;J||(O=pE),l&&O.length&&(c=F.filter(a=>null!=a.value&&(!0!==a.hide||e.includeHidden)),O=!0===o?po()(c,pD):"function"==typeof o?po()(c,o):c);var P=O.length>0,Q=f.createElement(pm,{allowEscapeViewBox:h,animationDuration:i,animationEasing:j,isAnimationActive:m,active:J,coordinate:H,hasPayload:P,offset:n,position:p,reverseDirection:q,useTranslate3d:r,viewBox:A,wrapperStyle:s,lastBoundingBox:K,innerRef:L,hasPortalFromProps:!!x},(d=pC(pC({},e),{},{payload:O,label:M,active:J,coordinate:H,accessibilityLayer:B}),f.isValidElement(k)?f.cloneElement(k,d):"function"==typeof k?f.createElement(k,d):f.createElement(pf,d)));return f.createElement(f.Fragment,null,(0,pa.createPortal)(Q,N),J&&f.createElement(pA,{cursor:t,tooltipEventType:C,coordinate:H,payload:F,index:D}))}var pH=["width","height"];function pI(){return(pI=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var pJ={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},pK=(0,f.forwardRef)(function(a,b){var c,d=mh(a.categoricalChartProps,pJ),{width:e,height:g}=d,h=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,pH);if(!gJ(e)||!gJ(g))return null;var{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l,categoricalChartProps:m}=a;return f.createElement(lF,{preloadedState:{options:{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l,eventEmitter:void 0}},reduxStoreName:null!=(c=m.id)?c:i},f.createElement(lG,{chartData:m.data}),f.createElement(lH,{width:e,height:g,layout:d.layout,margin:d.margin}),f.createElement(lI,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),f.createElement(mf,pI({},h,{width:e,height:g,ref:b})))}),pL=["axis","item"],pM=(0,f.forwardRef)((a,b)=>f.createElement(pK,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:pL,tooltipPayloadSearcher:bc,categoricalChartProps:a,ref:b}));class pN{static create(a){return new pN(a)}constructor(a){this.scale=a}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(a){var{bandAware:b,position:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==a){if(c)switch(c){case"start":default:return this.scale(a);case"middle":var d=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+d;case"end":var e=this.bandwidth?this.bandwidth():0;return this.scale(a)+e}if(b){var f=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+f}return this.scale(a)}}isInRange(a){var b=this.range(),c=b[0],d=b[b.length-1];return c<=d?a>=c&&a<=d:a>=d&&a<=c}}!function(a,b,c){var d;(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):a[b]=1e-4}(pN,"EPS",1e-4);var pO=function(a){var{width:b,height:c}=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=(d%180+180)%180*Math.PI/180,f=Math.atan(c/b);return Math.abs(e>f&&e<Math.PI-f?c/Math.sin(e):b/Math.cos(e))};function pP(a,b,c){if(b<1)return[];if(1===b&&void 0===c)return a;for(var d=[],e=0;e<a.length;e+=b)if(void 0!==c&&!0!==c(a[e]))return;else d.push(a[e]);return d}function pQ(a,b,c,d,e){if(a*b<a*d||a*b>a*e)return!1;var f=c();return a*(b-a*f/2-d)>=0&&a*(b+a*f/2-e)<=0}function pR(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pS(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pR(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pR(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pT(a,b,c){var d,{tick:e,ticks:f,viewBox:g,minTickGap:h,orientation:i,interval:j,tickFormatter:k,unit:m,angle:n}=a;if(!f||!f.length||!e)return[];if(o(j)||nm.isSsr)return null!=(d=pP(f,(o(j)?j:0)+1))?d:[];var p="top"===i||"bottom"===i?"width":"height",q=m&&"width"===p?ns(m,{fontSize:b,letterSpacing:c}):{width:0,height:0},r=(a,d)=>{var e,f="function"==typeof k?k(a.value,d):a.value;return"width"===p?(e=ns(f,{fontSize:b,letterSpacing:c}),pO({width:e.width+q.width,height:e.height+q.height},n)):ns(f,{fontSize:b,letterSpacing:c})[p]},s=f.length>=2?l(f[1].coordinate-f[0].coordinate):1,t=function(a,b,c){var d="width"===c,{x:e,y:f,width:g,height:h}=a;return 1===b?{start:d?e:f,end:d?e+g:f+h}:{start:d?e+g:f+h,end:d?e:f}}(g,s,p);return"equidistantPreserveStart"===j?function(a,b,c,d,e){for(var f,g=(d||[]).slice(),{start:h,end:i}=b,j=0,k=1,l=h;k<=g.length;)if(f=function(){var b,f=null==d?void 0:d[j];if(void 0===f)return{v:pP(d,k)};var g=j,m=()=>(void 0===b&&(b=c(f,g)),b),n=f.coordinate,o=0===j||pQ(a,n,m,l,i);o||(j=0,l=h,k+=1),o&&(l=n+a*(m()/2+e),j+=k)}())return f.v;return[]}(s,t,r,f,h):("preserveStart"===j||"preserveStartEnd"===j?function(a,b,c,d,e,f){var g=(d||[]).slice(),h=g.length,{start:i,end:j}=b;if(f){var k=d[h-1],l=c(k,h-1),m=a*(k.coordinate+a*l/2-j);g[h-1]=k=pS(pS({},k),{},{tickCoord:m>0?k.coordinate-m*a:k.coordinate}),pQ(a,k.tickCoord,()=>l,i,j)&&(j=k.tickCoord-a*(l/2+e),g[h-1]=pS(pS({},k),{},{isShow:!0}))}for(var n=f?h-1:h,o=function(b){var d,f=g[b],h=()=>(void 0===d&&(d=c(f,b)),d);if(0===b){var k=a*(f.coordinate-a*h()/2-i);g[b]=f=pS(pS({},f),{},{tickCoord:k<0?f.coordinate-k*a:f.coordinate})}else g[b]=f=pS(pS({},f),{},{tickCoord:f.coordinate});pQ(a,f.tickCoord,h,i,j)&&(i=f.tickCoord+a*(h()/2+e),g[b]=pS(pS({},f),{},{isShow:!0}))},p=0;p<n;p++)o(p);return g}(s,t,r,f,h,"preserveStartEnd"===j):function(a,b,c,d,e){for(var f=(d||[]).slice(),g=f.length,{start:h}=b,{end:i}=b,j=function(b){var d,j=f[b],k=()=>(void 0===d&&(d=c(j,b)),d);if(b===g-1){var l=a*(j.coordinate+a*k()/2-i);f[b]=j=pS(pS({},j),{},{tickCoord:l>0?j.coordinate-l*a:j.coordinate})}else f[b]=j=pS(pS({},j),{},{tickCoord:j.coordinate});pQ(a,j.tickCoord,k,h,i)&&(i=j.tickCoord-a*(k()/2+e),f[b]=pS(pS({},j),{},{isShow:!0}))},k=g-1;k>=0;k--)j(k);return f}(s,t,r,f,h)).filter(a=>a.isShow)}function pU(a,b){for(var c in a)if(({}).hasOwnProperty.call(a,c)&&(!({}).hasOwnProperty.call(b,c)||a[c]!==b[c]))return!1;for(var d in b)if(({}).hasOwnProperty.call(b,d)&&!({}).hasOwnProperty.call(a,d))return!1;return!0}var pV=["offset"],pW=["labelRef"];function pX(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function pY(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pZ(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pY(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pY(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function p$(){return(p$=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var p_=a=>null!=a&&"function"==typeof a;function p0(a){var b,{offset:c=5}=a,d=pZ({offset:c},pX(a,pV)),{viewBox:e,position:h,value:i,children:j,content:k,className:m="",textBreakAll:p,labelRef:q}=d,t=cU(),u=e||t;if(!u||null==i&&null==j&&!(0,f.isValidElement)(k)&&"function"!=typeof k)return null;if((0,f.isValidElement)(k)){var{labelRef:v}=d,w=pX(d,pW);return(0,f.cloneElement)(k,w)}if("function"==typeof k){if(b=(0,f.createElement)(k,d),(0,f.isValidElement)(b))return b}else b=(a=>{var{value:b,formatter:c}=a,d=null==a.children?b:a.children;return"function"==typeof c?c(d):d})(d);var x="cx"in u&&o(u.cx),y=lV(d,!0);if(x&&("insideStart"===h||"insideEnd"===h||"end"===h))return((a,b,c)=>{let d,e;var h,i,{position:j,viewBox:k,offset:m,className:n}=a,{cx:o,cy:p,innerRadius:q,outerRadius:s,startAngle:t,endAngle:u,clockWise:v}=k,w=(q+s)/2,x=(d=t,l((e=u)-d)*Math.min(Math.abs(e-d),360)),y=x>=0?1:-1;"insideStart"===j?(h=t+y*m,i=v):"insideEnd"===j?(h=u-y*m,i=!v):"end"===j&&(h=u+y*m,i=v),i=x<=0?i:!i;var z=co(o,p,w,h),A=co(o,p,w,h+(i?1:-1)*359),B="M".concat(z.x,",").concat(z.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(+!i,",\n    ").concat(A.x,",").concat(A.y),C=null==a.id?r("recharts-radial-line-"):a.id;return f.createElement("text",p$({},c,{dominantBaseline:"central",className:g("recharts-radial-bar-label",n)}),f.createElement("defs",null,f.createElement("path",{id:C,d:B})),f.createElement("textPath",{xlinkHref:"#".concat(C)},b))})(d,b,y);var z=x?(a=>{var{viewBox:b,offset:c,position:d}=a,{cx:e,cy:f,innerRadius:g,outerRadius:h,startAngle:i,endAngle:j}=b,k=(i+j)/2;if("outside"===d){var{x:l,y:m}=co(e,f,h+c,k);return{x:l,y:m,textAnchor:l>=e?"start":"end",verticalAnchor:"middle"}}if("center"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"end"};var{x:n,y:o}=co(e,f,(g+h)/2,k);return{x:n,y:o,textAnchor:"middle",verticalAnchor:"middle"}})(d):((a,b)=>{var{parentViewBox:c,offset:d,position:e}=a,{x:f,y:g,width:h,height:i}=b,j=i>=0?1:-1,k=j*d,l=j>0?"end":"start",m=j>0?"start":"end",p=h>=0?1:-1,q=p*d,r=p>0?"end":"start",t=p>0?"start":"end";if("top"===e)return pZ(pZ({},{x:f+h/2,y:g-j*d,textAnchor:"middle",verticalAnchor:l}),c?{height:Math.max(g-c.y,0),width:h}:{});if("bottom"===e)return pZ(pZ({},{x:f+h/2,y:g+i+k,textAnchor:"middle",verticalAnchor:m}),c?{height:Math.max(c.y+c.height-(g+i),0),width:h}:{});if("left"===e){var u={x:f-q,y:g+i/2,textAnchor:r,verticalAnchor:"middle"};return pZ(pZ({},u),c?{width:Math.max(u.x-c.x,0),height:i}:{})}if("right"===e){var v={x:f+h+q,y:g+i/2,textAnchor:t,verticalAnchor:"middle"};return pZ(pZ({},v),c?{width:Math.max(c.x+c.width-v.x,0),height:i}:{})}var w=c?{width:h,height:i}:{};return"insideLeft"===e?pZ({x:f+q,y:g+i/2,textAnchor:t,verticalAnchor:"middle"},w):"insideRight"===e?pZ({x:f+h-q,y:g+i/2,textAnchor:r,verticalAnchor:"middle"},w):"insideTop"===e?pZ({x:f+h/2,y:g+k,textAnchor:"middle",verticalAnchor:m},w):"insideBottom"===e?pZ({x:f+h/2,y:g+i-k,textAnchor:"middle",verticalAnchor:l},w):"insideTopLeft"===e?pZ({x:f+q,y:g+k,textAnchor:t,verticalAnchor:m},w):"insideTopRight"===e?pZ({x:f+h-q,y:g+k,textAnchor:r,verticalAnchor:m},w):"insideBottomLeft"===e?pZ({x:f+q,y:g+i-k,textAnchor:t,verticalAnchor:l},w):"insideBottomRight"===e?pZ({x:f+h-q,y:g+i-k,textAnchor:r,verticalAnchor:l},w):e&&"object"==typeof e&&(o(e.x)||n(e.x))&&(o(e.y)||n(e.y))?pZ({x:f+s(e.x,h),y:g+s(e.y,i),textAnchor:"end",verticalAnchor:"end"},w):pZ({x:f+h/2,y:g+i/2,textAnchor:"middle",verticalAnchor:"middle"},w)})(d,u);return f.createElement(nL,p$({ref:q,className:g("recharts-label",m)},y,z,{breakAll:p}),b)}p0.displayName="Label";var p1=a=>{var{cx:b,cy:c,angle:d,startAngle:e,endAngle:f,r:g,radius:h,innerRadius:i,outerRadius:j,x:k,y:l,top:m,left:n,width:p,height:q,clockWise:r,labelViewBox:s}=a;if(s)return s;if(o(p)&&o(q)){if(o(k)&&o(l))return{x:k,y:l,width:p,height:q};if(o(m)&&o(n))return{x:m,y:n,width:p,height:q}}return o(k)&&o(l)?{x:k,y:l,width:0,height:0}:o(b)&&o(c)?{cx:b,cy:c,startAngle:e||d||0,endAngle:f||d||0,innerRadius:i||0,outerRadius:j||h||g||0,clockWise:r}:a.viewBox?a.viewBox:void 0};p0.parseViewBox=p1,p0.renderCallByParent=function(a,b){var c=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&c&&!a.label)return null;var{children:d,labelRef:e}=a,g=p1(a),h=lU(d,p0).map((a,c)=>(0,f.cloneElement)(a,{viewBox:b||g,key:"label-".concat(c)}));return c?[((a,b,c)=>{if(!a)return null;var d={viewBox:b,labelRef:c};return!0===a?f.createElement(p0,p$({key:"label-implicit"},d)):p(a)?f.createElement(p0,p$({key:"label-implicit",value:a},d)):(0,f.isValidElement)(a)?a.type===p0?(0,f.cloneElement)(a,pZ({key:"label-implicit"},d)):f.createElement(p0,p$({key:"label-implicit",content:a},d)):p_(a)?f.createElement(p0,p$({key:"label-implicit",content:a},d)):a&&"object"==typeof a?f.createElement(p0,p$({},a,{key:"label-implicit"},d)):null})(a.label,b||g,e),...h]:h};var p2=["viewBox"],p3=["viewBox"];function p4(){return(p4=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function p5(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function p6(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?p5(Object(c),!0).forEach(function(b){p8(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):p5(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function p7(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function p8(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class p9 extends f.Component{constructor(a){super(a),this.tickRefs=f.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(a,b){var{viewBox:c}=a,d=p7(a,p2),e=this.props,{viewBox:f}=e,g=p7(e,p3);return!pU(c,f)||!pU(d,g)||!pU(b,this.state)}getTickLineCoord(a){var b,c,d,e,f,g,{x:h,y:i,width:j,height:k,orientation:l,tickSize:m,mirror:n,tickMargin:p}=this.props,q=n?-1:1,r=a.tickSize||m,s=o(a.tickCoord)?a.tickCoord:a.coordinate;switch(l){case"top":b=c=a.coordinate,g=(d=(e=i+!n*k)-q*r)-q*p,f=s;break;case"left":d=e=a.coordinate,f=(b=(c=h+!n*j)-q*r)-q*p,g=s;break;case"right":d=e=a.coordinate,f=(b=(c=h+n*j)+q*r)+q*p,g=s;break;default:b=c=a.coordinate,g=(d=(e=i+n*k)+q*r)+q*p,f=s}return{line:{x1:b,y1:d,x2:c,y2:e},tick:{x:f,y:g}}}getTickTextAnchor(){var a,{orientation:b,mirror:c}=this.props;switch(b){case"left":a=c?"start":"end";break;case"right":a=c?"end":"start";break;default:a="middle"}return a}getTickVerticalAnchor(){var{orientation:a,mirror:b}=this.props;switch(a){case"left":case"right":return"middle";case"top":return b?"start":"end";default:return b?"end":"start"}}renderAxisLine(){var{x:a,y:b,width:c,height:d,orientation:e,mirror:h,axisLine:i}=this.props,j=p6(p6(p6({},lV(this.props,!1)),lV(i,!1)),{},{fill:"none"});if("top"===e||"bottom"===e){var l=+("top"===e&&!h||"bottom"===e&&h);j=p6(p6({},j),{},{x1:a,y1:b+l*d,x2:a+c,y2:b+l*d})}else{var m=+("left"===e&&!h||"right"===e&&h);j=p6(p6({},j),{},{x1:a+m*c,y1:b,x2:a+m*c,y2:b+d})}return f.createElement("line",p4({},j,{className:g("recharts-cartesian-axis-line",k()(i,"className"))}))}static renderTickItem(a,b,c){var d,e=g(b.className,"recharts-cartesian-axis-tick-value");if(f.isValidElement(a))d=f.cloneElement(a,p6(p6({},b),{},{className:e}));else if("function"==typeof a)d=a(p6(p6({},b),{},{className:e}));else{var h="recharts-cartesian-axis-tick-value";"boolean"!=typeof a&&(h=g(h,a.className)),d=f.createElement(nL,p4({},b,{className:h}),c)}return d}renderTicks(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:d,stroke:e,tick:h,tickFormatter:i,unit:j}=this.props,l=pT(p6(p6({},this.props),{},{ticks:c}),a,b),m=this.getTickTextAnchor(),n=this.getTickVerticalAnchor(),o=lV(this.props,!1),p=lV(h,!1),q=p6(p6({},o),{},{fill:"none"},lV(d,!1)),r=l.map((a,b)=>{var{line:c,tick:r}=this.getTickLineCoord(a),s=p6(p6(p6(p6({textAnchor:m,verticalAnchor:n},o),{},{stroke:"none",fill:e},p),r),{},{index:b,payload:a,visibleTicksCount:l.length,tickFormatter:i});return f.createElement(mN,p4({className:"recharts-cartesian-axis-tick",key:"tick-".concat(a.value,"-").concat(a.coordinate,"-").concat(a.tickCoord)},lP(this.props,a,b)),d&&f.createElement("line",p4({},q,c,{className:g("recharts-cartesian-axis-tick-line",k()(d,"className"))})),h&&p9.renderTickItem(h,s,"".concat("function"==typeof i?i(a.value,b):a.value).concat(j||"")))});return r.length>0?f.createElement("g",{className:"recharts-cartesian-axis-ticks"},r):null}render(){var{axisLine:a,width:b,height:c,className:d,hide:e}=this.props;if(e)return null;var{ticks:h}=this.props;return null!=b&&b<=0||null!=c&&c<=0?null:f.createElement(mN,{className:g("recharts-cartesian-axis",d),ref:a=>{if(a){var b=a.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(b);var c=b[0];if(c){var d=window.getComputedStyle(c).fontSize,e=window.getComputedStyle(c).letterSpacing;(d!==this.state.fontSize||e!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(c).fontSize,letterSpacing:window.getComputedStyle(c).letterSpacing})}}}},a&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,h),p0.renderCallByParent(this.props))}}p8(p9,"displayName","CartesianAxis"),p8(p9,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var qa=["x1","y1","x2","y2","key"],qb=["offset"],qc=["xAxisId","yAxisId"],qd=["xAxisId","yAxisId"];function qe(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qf(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qe(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qe(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qg(){return(qg=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qh(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var qi=a=>{var{fill:b}=a;if(!b||"none"===b)return null;var{fillOpacity:c,x:d,y:e,width:g,height:h,ry:i}=a;return f.createElement("rect",{x:d,y:e,ry:i,width:g,height:h,stroke:"none",fill:b,fillOpacity:c,className:"recharts-cartesian-grid-bg"})};function qj(a,b){var c;if(f.isValidElement(a))c=f.cloneElement(a,b);else if("function"==typeof a)c=a(b);else{var{x1:d,y1:e,x2:g,y2:h,key:i}=b,j=lV(qh(b,qa),!1),{offset:k}=j,l=qh(j,qb);c=f.createElement("line",qg({},l,{x1:d,y1:e,x2:g,y2:h,fill:"none",key:i}))}return c}function qk(a){var{x:b,width:c,horizontal:d=!0,horizontalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:g,yAxisId:h}=a,i=qh(a,qc),j=e.map((a,e)=>qj(d,qf(qf({},i),{},{x1:b,y1:a,x2:b+c,y2:a,key:"line-".concat(e),index:e})));return f.createElement("g",{className:"recharts-cartesian-grid-horizontal"},j)}function ql(a){var{y:b,height:c,vertical:d=!0,verticalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:g,yAxisId:h}=a,i=qh(a,qd),j=e.map((a,e)=>qj(d,qf(qf({},i),{},{x1:a,y1:b,x2:a,y2:b+c,key:"line-".concat(e),index:e})));return f.createElement("g",{className:"recharts-cartesian-grid-vertical"},j)}function qm(a){var{horizontalFill:b,fillOpacity:c,x:d,y:e,width:g,height:h,horizontalPoints:i,horizontal:j=!0}=a;if(!j||!b||!b.length)return null;var k=i.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==k[0]&&k.unshift(0);var l=k.map((a,i)=>{var j=k[i+1]?k[i+1]-a:e+h-a;if(j<=0)return null;var l=i%b.length;return f.createElement("rect",{key:"react-".concat(i),y:a,x:d,height:j,width:g,stroke:"none",fill:b[l],fillOpacity:c,className:"recharts-cartesian-grid-bg"})});return f.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function qn(a){var{vertical:b=!0,verticalFill:c,fillOpacity:d,x:e,y:g,width:h,height:i,verticalPoints:j}=a;if(!b||!c||!c.length)return null;var k=j.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==k[0]&&k.unshift(0);var l=k.map((a,b)=>{var j=k[b+1]?k[b+1]-a:e+h-a;if(j<=0)return null;var l=b%c.length;return f.createElement("rect",{key:"react-".concat(b),x:a,y:g,width:j,height:i,stroke:"none",fill:c[l],fillOpacity:d,className:"recharts-cartesian-grid-bg"})});return f.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var qo=(a,b)=>{var{xAxis:c,width:d,height:e,offset:f}=a;return cu(pT(qf(qf(qf({},p9.defaultProps),c),{},{ticks:cv(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.left,f.left+f.width,b)},qp=(a,b)=>{var{yAxis:c,width:d,height:e,offset:f}=a;return cu(pT(qf(qf(qf({},p9.defaultProps),c),{},{ticks:cv(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.top,f.top+f.height,b)},qq={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function qr(a){var b=cX(),c=cY(),d=cW(),e=qf(qf({},mh(a,qq)),{},{x:o(a.x)?a.x:d.left,y:o(a.y)?a.y:d.top,width:o(a.width)?a.width:d.width,height:o(a.height)?a.height:d.height}),{xAxisId:g,yAxisId:h,x:i,y:j,width:k,height:l,syncWithTicks:m,horizontalValues:n,verticalValues:p}=e,q=cR(),r=cb(a=>jm(a,"xAxis",g,q)),s=cb(a=>jm(a,"yAxis",h,q));if(!o(k)||k<=0||!o(l)||l<=0||!o(i)||i!==+i||!o(j)||j!==+j)return null;var t=e.verticalCoordinatesGenerator||qo,u=e.horizontalCoordinatesGenerator||qp,{horizontalPoints:v,verticalPoints:x}=e;if((!v||!v.length)&&"function"==typeof u){var y=n&&n.length,z=u({yAxis:s?qf(qf({},s),{},{ticks:y?n:s.ticks}):void 0,width:b,height:c,offset:d},!!y||m);w(Array.isArray(z),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof z,"]")),Array.isArray(z)&&(v=z)}if((!x||!x.length)&&"function"==typeof t){var A=p&&p.length,B=t({xAxis:r?qf(qf({},r),{},{ticks:A?p:r.ticks}):void 0,width:b,height:c,offset:d},!!A||m);w(Array.isArray(B),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof B,"]")),Array.isArray(B)&&(x=B)}return f.createElement("g",{className:"recharts-cartesian-grid"},f.createElement(qi,{fill:e.fill,fillOpacity:e.fillOpacity,x:e.x,y:e.y,width:e.width,height:e.height,ry:e.ry}),f.createElement(qm,qg({},e,{horizontalPoints:v})),f.createElement(qn,qg({},e,{verticalPoints:x})),f.createElement(qk,qg({},e,{offset:d,horizontalPoints:v,xAxis:r,yAxis:s})),f.createElement(ql,qg({},e,{offset:d,verticalPoints:x,xAxis:r,yAxis:s})))}qr.displayName="CartesianGrid";var qs=["children"],qt=["dangerouslySetInnerHTML","ticks"];function qu(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function qv(){return(qv=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qw(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function qx(a){b7();var b=(0,f.useMemo)(()=>{var{children:b}=a;return qw(a,qs)},[a]),c=cb(a=>h1(a,b.id));return b===c?a.children:null}var qy=a=>{var{xAxisId:b,className:c}=a,d=cb(cP),e=cR(),h="xAxis",i=cb(a=>i6(a,h,b,e)),j=cb(a=>jn(a,h,b,e)),k=cb(a=>jd(a,b)),l=cb(a=>((a,b)=>{var c=cN(a),d=h1(a,b);if(null!=d){var e=je(a,d.orientation,d.mirror)[b];return null==e?{x:c.left,y:0}:{x:c.left,y:e}}})(a,b));if(null==k||null==l)return null;var{dangerouslySetInnerHTML:m,ticks:n}=a,o=qw(a,qt);return f.createElement(p9,qv({},o,{scale:i,x:l.x,y:l.y,width:k.width,height:k.height,className:g("recharts-".concat(h," ").concat(h),c),viewBox:d,ticks:j}))},qz=a=>{var b,c,d,e,g;return f.createElement(qx,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.xAxisId,scale:a.scale,type:a.type,padding:a.padding,allowDataOverflow:a.allowDataOverflow,domain:a.domain,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,height:a.height,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(g=a.tick)||g,tickFormatter:a.tickFormatter},f.createElement(qy,a))};class qA extends f.Component{render(){return f.createElement(qz,this.props)}}qu(qA,"displayName","XAxis"),qu(qA,"defaultProps",{allowDataOverflow:h0.allowDataOverflow,allowDecimals:h0.allowDecimals,allowDuplicatedCategory:h0.allowDuplicatedCategory,height:h0.height,hide:!1,mirror:h0.mirror,orientation:h0.orientation,padding:h0.padding,reversed:h0.reversed,scale:h0.scale,tickCount:h0.tickCount,type:h0.type,xAxisId:0});var qB=["dangerouslySetInnerHTML","ticks"];function qC(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function qD(){return(qD=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qE(a){return b7(),null}var qF=a=>{var b,{yAxisId:c,className:d,width:e,label:h}=a,i=(0,f.useRef)(null),j=(0,f.useRef)(null),k=cb(cP),l=cR(),m=b7(),n="yAxis",o=cb(a=>i6(a,n,c,l)),p=cb(a=>jg(a,c)),q=cb(a=>((a,b)=>{var c=cN(a),d=h3(a,b);if(null!=d){var e=jf(a,d.orientation,d.mirror)[b];return null==e?{x:0,y:c.top}:{x:e,y:c.top}}})(a,c)),r=cb(a=>jn(a,n,c,l));if((0,f.useLayoutEffect)(()=>{if(!("auto"!==e||!p||p_(h)||(0,f.isValidElement)(h))){var a,b=i.current,d=null==b||null==(a=b.tickRefs)?void 0:a.current,{tickSize:g,tickMargin:k}=b.props,l=(a=>{var{ticks:b,label:c,labelGapWithTick:d=5,tickSize:e=0,tickMargin:f=0}=a,g=0;if(b){b.forEach(a=>{if(a){var b=a.getBoundingClientRect();b.width>g&&(g=b.width)}});var h=c?c.getBoundingClientRect().width:0;return Math.round(g+(e+f)+h+(c?d:0))}return 0})({ticks:d,label:j.current,labelGapWithTick:5,tickSize:g,tickMargin:k});Math.round(p.width)!==Math.round(l)&&m(kQ({id:c,width:l}))}},[i,null==i||null==(b=i.current)||null==(b=b.tickRefs)?void 0:b.current,null==p?void 0:p.width,p,m,h,c,e]),null==p||null==q)return null;var{dangerouslySetInnerHTML:s,ticks:t}=a,u=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,qB);return f.createElement(p9,qD({},u,{ref:i,labelRef:j,scale:o,x:q.x,y:q.y,width:p.width,height:p.height,className:g("recharts-".concat(n," ").concat(n),d),viewBox:k,ticks:r}))},qG=a=>{var b,c,d,e,g;return f.createElement(f.Fragment,null,f.createElement(qE,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.yAxisId,scale:a.scale,type:a.type,domain:a.domain,allowDataOverflow:a.allowDataOverflow,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,padding:a.padding,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,width:a.width,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(g=a.tick)||g,tickFormatter:a.tickFormatter}),f.createElement(qF,a))},qH={allowDataOverflow:h2.allowDataOverflow,allowDecimals:h2.allowDecimals,allowDuplicatedCategory:h2.allowDuplicatedCategory,hide:!1,mirror:h2.mirror,orientation:h2.orientation,padding:h2.padding,reversed:h2.reversed,scale:h2.scale,tickCount:h2.tickCount,type:h2.type,width:h2.width,yAxisId:0};class qI extends f.Component{render(){return f.createElement(qG,this.props)}}qC(qI,"displayName","YAxis"),qC(qI,"defaultProps",qH);var qJ=["children"],qK=()=>{},qL=(0,f.createContext)({addErrorBar:qK,removeErrorBar:qK}),qM=(0,f.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function qN(a){var{children:b}=a,c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,qJ);return f.createElement(qM.Provider,{value:c},b)}var qO=a=>{var{children:b,xAxisId:c,yAxisId:d,zAxisId:e,dataKey:g,data:h,stackId:i,hide:j,type:k,barSize:l}=a,[m,n]=f.useState([]),o=(0,f.useCallback)(a=>{n(b=>[...b,a])},[n]),p=(0,f.useCallback)(a=>{n(b=>b.filter(b=>b!==a))},[n]),q=cR();return f.createElement(qL.Provider,{value:{addErrorBar:o,removeErrorBar:p}},f.createElement(mJ,{type:k,data:h,xAxisId:c,yAxisId:d,zAxisId:e,dataKey:g,errorBars:m,stackId:i,hide:j,barSize:l,isPanorama:q}),b)};function qP(a){var{addErrorBar:b,removeErrorBar:c}=(0,f.useContext)(qL);return null}var qQ=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function qR(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function qS(){return(qS=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qT(a){var b,c,{direction:d,width:e,dataKey:g,isAnimationActive:h,animationBegin:i,animationDuration:j,animationEasing:k}=a,l=lV(function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,qQ),!1),{data:m,dataPointFormatter:n,xAxisId:o,yAxisId:p,errorBarOffset:q}=(0,f.useContext)(qM),r=(b=cR(),cb(a=>jp(a,"xAxis",o,b))),s=(c=cR(),cb(a=>jp(a,"yAxis",p,c)));if((null==r?void 0:r.scale)==null||(null==s?void 0:s.scale)==null||null==m||"x"===d&&"number"!==r.type)return null;var t=m.map(a=>{var b,c,{x:m,y:o,value:p,errorVal:t}=n(a,g,d);if(!t)return null;var u=[];if(Array.isArray(t)?[b,c]=t:b=c=t,"x"===d){var{scale:v}=r,w=o+q,x=w+e,y=w-e,z=v(p-b),A=v(p+c);u.push({x1:A,y1:x,x2:A,y2:y}),u.push({x1:z,y1:w,x2:A,y2:w}),u.push({x1:z,y1:x,x2:z,y2:y})}else if("y"===d){var{scale:B}=s,C=m+q,D=C-e,E=C+e,F=B(p-b),G=B(p+c);u.push({x1:D,y1:G,x2:E,y2:G}),u.push({x1:C,y1:F,x2:C,y2:G}),u.push({x1:D,y1:F,x2:E,y2:F})}var H="".concat(m+q,"px ").concat(o+q,"px");return f.createElement(mN,qS({className:"recharts-errorBar",key:"bar-".concat(u.map(a=>"".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2)))},l),u.map(a=>{var b=h?{transformOrigin:"".concat(a.x1-5,"px")}:void 0;return f.createElement(oa,{from:{transform:"scaleY(0)",transformOrigin:H},to:{transform:"scaleY(1)",transformOrigin:H},begin:i,easing:k,isActive:h,duration:j,key:"line-".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2),style:{transformOrigin:H}},f.createElement("line",qS({},a,{style:b})))}))});return f.createElement(mN,{className:"recharts-errorBars"},t)}var qU=(0,f.createContext)(void 0);function qV(a){var{direction:b,children:c}=a;return f.createElement(qU.Provider,{value:b},c)}var qW={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function qX(a){var b,c,d=(b=a.direction,c=(0,f.useContext)(qU),null!=b?b:null!=c?c:"x"),{width:e,isAnimationActive:g,animationBegin:h,animationDuration:i,animationEasing:j}=mh(a,qW);return f.createElement(f.Fragment,null,f.createElement(qP,{dataKey:a.dataKey,direction:d}),f.createElement(qT,qS({},a,{direction:d,width:e,isAnimationActive:g,animationBegin:h,animationDuration:i,animationEasing:j})))}class qY extends f.Component{render(){return f.createElement(qX,this.props)}}qR(qY,"defaultProps",qW),qR(qY,"displayName","ErrorBar");var qZ=c(18678),q$=c.n(qZ),q_=["valueAccessor"],q0=["data","dataKey","clockWise","id","textBreakAll"];function q1(){return(q1=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function q2(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function q3(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?q2(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):q2(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function q4(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var q5=a=>Array.isArray(a.value)?q$()(a.value):a.value;function q6(a){var{valueAccessor:b=q5}=a,c=q4(a,q_),{data:d,dataKey:e,clockWise:g,id:h,textBreakAll:i}=c,j=q4(c,q0);return d&&d.length?f.createElement(mN,{className:"recharts-label-list"},d.map((a,c)=>{var d=null==e?b(a,c):cs(a&&a.payload,e),k=null==h?{}:{id:"".concat(h,"-").concat(c)};return f.createElement(p0,q1({},lV(a,!0),j,k,{parentViewBox:a.parentViewBox,value:d,textBreakAll:i,viewBox:p0.parseViewBox(null==g?a:q3(q3({},a),{},{clockWise:g})),key:"label-".concat(c),index:c}))})):null}q6.displayName="LabelList",q6.renderCallByParent=function(a,b){var c,d=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&d&&!a.label)return null;var{children:e}=a,g=lU(e,q6).map((a,c)=>(0,f.cloneElement)(a,{data:b,key:"labelList-".concat(c)}));return d?[(c=a.label,c?!0===c?f.createElement(q6,{key:"labelList-implicit",data:b}):f.isValidElement(c)||p_(c)?f.createElement(q6,{key:"labelList-implicit",data:b,content:c}):"object"==typeof c?f.createElement(q6,q1({data:b},c,{key:"labelList-implicit"})):null:null),...g]:g};var q7=["x","y"];function q8(){return(q8=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function q9(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ra(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?q9(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):q9(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function rb(a,b){var{x:c,y:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,q7),f=parseInt("".concat(c),10),g=parseInt("".concat(d),10),h=parseInt("".concat(b.height||e.height),10),i=parseInt("".concat(b.width||e.width),10);return ra(ra(ra(ra(ra({},b),e),f?{x:f}:{}),g?{y:g}:{}),{},{height:h,width:i,name:b.name,radius:b.radius})}function rc(a){return f.createElement(oP,q8({shapeType:"rectangle",propTransformer:rb,activeClassName:"recharts-active-bar"},a))}var rd=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(c,d)=>{if(o(a))return a;var e=o(c)||null==c;return e?a(c,d):(e||function(a,b){if(!a)throw Error("Invariant failed")}(!1),b)}},re=()=>{var a=b7();return(0,f.useEffect)(()=>(a(kT()),()=>{a(kU())})),null};function rf(a,b){var c,d,e=cb(b=>h1(b,a)),f=cb(a=>h3(a,b)),g=null!=(c=null==e?void 0:e.allowDataOverflow)?c:h0.allowDataOverflow,h=null!=(d=null==f?void 0:f.allowDataOverflow)?d:h2.allowDataOverflow;return{needClip:g||h,needClipX:g,needClipY:h}}function rg(a){var{xAxisId:b,yAxisId:c,clipPathId:d}=a,e=mb(),{needClipX:g,needClipY:h,needClip:i}=rf(b,c);if(!i)return null;var{x:j,y:k,width:l,height:m}=e;return f.createElement("clipPath",{id:"clipPath-".concat(d)},f.createElement("rect",{x:g?j:j-l/2,y:h?k:k-m/2,width:g?l:2*l,height:h?m:2*m}))}function rh(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ri(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rh(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rh(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var rj=(a,b,c,d,e)=>e,rk=(a,b,c)=>{var d=null!=c?c:a;if(null!=d)return s(d,b,0)},rl=b2([cZ,ia,(a,b)=>b,(a,b,c)=>c,(a,b,c,d)=>d],(a,b,c,d,e)=>b.filter(b=>"horizontal"===a?b.xAxisId===c:b.yAxisId===d).filter(a=>a.isPanorama===e).filter(a=>!1===a.hide).filter(a=>"bar"===a.type));function rm(a){return null!=a.stackId&&null!=a.dataKey}var rn=b2([rl,a=>a.rootProps.barSize,(a,b,c)=>"horizontal"===cZ(a)?jh(a,"xAxis",b):jh(a,"yAxis",c)],(a,b,c)=>{var d=a.filter(rm),e=a.filter(a=>null==a.stackId);return[...Object.entries(d.reduce((a,b)=>(a[b.stackId]||(a[b.stackId]=[]),a[b.stackId].push(b),a),{})).map(a=>{var[d,e]=a;return{stackId:d,dataKeys:e.map(a=>a.dataKey),barSize:rk(b,c,e[0].barSize)}}),...e.map(a=>({stackId:void 0,dataKeys:[a.dataKey].filter(a=>null!=a),barSize:rk(b,c,a.barSize)}))]}),ro=(a,b,c,d)=>{var e,f;return"horizontal"===cZ(a)?(e=jp(a,"xAxis",b,d),f=jo(a,"xAxis",b,d)):(e=jp(a,"yAxis",c,d),f=jo(a,"yAxis",c,d)),cA(e,f)},rp=b2([rn,hA,a=>a.rootProps.barGap,hB,(a,b,c,d,e)=>{var f,g,h,i,j=cZ(a),k=hA(a),{maxBarSize:l}=e,m=null==l?k:l;return"horizontal"===j?(h=jp(a,"xAxis",b,d),i=jo(a,"xAxis",b,d)):(h=jp(a,"yAxis",c,d),i=jo(a,"yAxis",c,d)),null!=(f=null!=(g=cA(h,i,!0))?g:m)?f:0},ro,(a,b,c,d,e)=>e.maxBarSize],(a,b,c,d,e,f,g)=>{var h=function(a,b,c,d,e){var f,g=d.length;if(!(g<1)){var h=s(a,c,0,!0),i=[];if(gI(d[0].barSize)){var j=!1,k=c/g,l=d.reduce((a,b)=>a+(b.barSize||0),0);(l+=(g-1)*h)>=c&&(l-=(g-1)*h,h=0),l>=c&&k>0&&(j=!0,k*=.9,l=g*k);var m={offset:((c-l)/2|0)-h,size:0};f=d.reduce((a,b)=>{var c,d=[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:m.offset+m.size+h,size:j?k:null!=(c=b.barSize)?c:0}}];return m=d[d.length-1].position,d},i)}else{var n=s(b,c,0,!0);c-2*n-(g-1)*h<=0&&(h=0);var o=(c-2*n-(g-1)*h)/g;o>1&&(o>>=0);var p=gI(e)?Math.min(o,e):o;f=d.reduce((a,b,c)=>[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:n+(o+h)*c+(o-p)/2,size:p}}],i)}return f}}(c,d,e!==f?e:f,a,null==g?b:g);return e!==f&&null!=h&&(h=h.map(a=>ri(ri({},a),{},{position:ri(ri({},a.position),{},{offset:a.position.offset-e/2})}))),h}),rq=b2([rp,rj],(a,b)=>{if(null!=a){var c=a.find(a=>a.stackId===b.stackId&&a.dataKeys.includes(b.dataKey));if(null!=c)return c.position}}),rr=b2([ia,rj],(a,b)=>{if(a.some(a=>"bar"===a.type&&b.dataKey===a.dataKey&&b.stackId===a.stackId&&b.stackId===a.stackId))return b}),rs=b2([(a,b,c,d)=>"horizontal"===cZ(a)?ir(a,"yAxis",c,d):ir(a,"xAxis",b,d),rj],(a,b)=>{if(!a||(null==b?void 0:b.dataKey)==null)return;var{stackId:c}=b;if(null!=c){var d=a[c];if(d){var{stackedData:e}=d;if(e)return e.find(a=>a.key===b.dataKey)}}}),rt=b2([cN,(a,b,c,d)=>jp(a,"xAxis",b,d),(a,b,c,d)=>jp(a,"yAxis",c,d),(a,b,c,d)=>jo(a,"xAxis",b,d),(a,b,c,d)=>jo(a,"yAxis",c,d),rq,cZ,gH,ro,rs,rr,(a,b,c,d,e,f)=>f],(a,b,c,d,e,f,g,h,i,j,k,n)=>{var p,{chartData:q,dataStartIndex:r,dataEndIndex:s}=h;if(null!=k&&null!=f&&("horizontal"===g||"vertical"===g)&&null!=b&&null!=c&&null!=d&&null!=e&&null!=i){var{data:t}=k;if(null!=(p=null!=t&&t.length>0?t:null==q?void 0:q.slice(r,s+1)))return function(a){var{layout:b,barSettings:{dataKey:c,minPointSize:d},pos:e,bandSize:f,xAxis:g,yAxis:h,xAxisTicks:i,yAxisTicks:j,stackedData:k,displayedData:n,offset:p,cells:q}=a,r="horizontal"===b?h:g,s=k?r.scale.domain():null,t=(a=>{var{numericAxis:b}=a,c=b.scale.domain();if("number"===b.type){var d=Math.min(c[0],c[1]),e=Math.max(c[0],c[1]);return d<=0&&e>=0?0:e<0?e:d}return c[0]})({numericAxis:r});return n.map((a,n)=>{k?u=((a,b)=>{if(!b||2!==b.length||!o(b[0])||!o(b[1]))return a;var c=Math.min(b[0],b[1]),d=Math.max(b[0],b[1]),e=[a[0],a[1]];return(!o(a[0])||a[0]<c)&&(e[0]=c),(!o(a[1])||a[1]>d)&&(e[1]=d),e[0]>d&&(e[0]=d),e[1]<c&&(e[1]=c),e})(k[n],s):Array.isArray(u=cs(a,c))||(u=[t,u]);var r=rd(d,0)(u[1],n);if("horizontal"===b){var u,v,w,x,y,z,A,[B,C]=[h.scale(u[0]),h.scale(u[1])];v=cx({axis:g,ticks:i,bandSize:f,offset:e.offset,entry:a,index:n}),w=null!=(A=null!=C?C:B)?A:void 0,x=e.size;var D=B-C;if(y=m(D)?0:D,z={x:v,y:p.top,width:x,height:p.height},Math.abs(r)>0&&Math.abs(y)<Math.abs(r)){var E=l(y||r)*(Math.abs(r)-Math.abs(y));w-=E,y+=E}}else{var[F,G]=[g.scale(u[0]),g.scale(u[1])];if(v=F,w=cx({axis:h,ticks:j,bandSize:f,offset:e.offset,entry:a,index:n}),x=G-F,y=e.size,z={x:p.left,y:w,width:p.width,height:y},Math.abs(r)>0&&Math.abs(x)<Math.abs(r)){var H=l(x||r)*(Math.abs(r)-Math.abs(x));x+=H}}return rz(rz({},a),{},{x:v,y:w,width:x,height:y,value:k?u:u[1],payload:a,background:z,tooltipPosition:{x:v+x/2,y:w+y/2}},q&&q[n]&&q[n].props)})}({layout:g,barSettings:k,pos:f,bandSize:i,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,stackedData:j,displayedData:p,offset:a,cells:n})}}),ru=["onMouseEnter","onMouseLeave","onClick"],rv=["value","background","tooltipPosition"],rw=["onMouseEnter","onClick","onMouseLeave"];function rx(){return(rx=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function ry(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function rz(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ry(Object(c),!0).forEach(function(b){rA(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ry(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function rA(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function rB(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function rC(a){var{dataKey:b,stroke:c,strokeWidth:d,fill:e,name:f,hide:g,unit:h}=a;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:c,strokeWidth:d,fill:e,dataKey:b,nameKey:void 0,name:cC(f,b),hide:g,type:a.tooltipType,color:a.fill,unit:h}}}function rD(a){var b=cb(kg),{data:c,dataKey:d,background:e,allOtherBarProps:g}=a,{onMouseEnter:h,onMouseLeave:i,onClick:j}=g,k=rB(g,ru),l=oQ(h,d),m=oR(i),n=oS(j,d);if(!e||null==c)return null;var o=lV(e,!1);return f.createElement(f.Fragment,null,c.map((a,c)=>{var{value:g,background:h,tooltipPosition:i}=a,j=rB(a,rv);if(!h)return null;var p=l(a,c),q=m(a,c),r=n(a,c),s=rz(rz(rz(rz(rz({option:e,isActive:String(c)===b},j),{},{fill:"#eee"},h),o),lP(k,a,c)),{},{onMouseEnter:p,onMouseLeave:q,onClick:r,dataKey:d,index:c,className:"recharts-bar-background-rectangle"});return f.createElement(rc,rx({key:"background-bar-".concat(c)},s))}))}function rE(a){var{data:b,props:c,showLabels:d}=a,e=lV(c,!1),{shape:g,dataKey:h,activeBar:i}=c,j=cb(kg),k=cb(ki),{onMouseEnter:l,onClick:m,onMouseLeave:n}=c,o=rB(c,rw),p=oQ(l,h),q=oR(n),r=oS(m,h);return b?f.createElement(f.Fragment,null,b.map((a,b)=>{var c=i&&String(b)===j&&(null==k||h===k),d=rz(rz(rz({},e),a),{},{isActive:c,option:c?i:g,index:b,dataKey:h});return f.createElement(mN,rx({className:"recharts-bar-rectangle"},lP(o,a,b),{onMouseEnter:p(a,b),onMouseLeave:q(a,b),onClick:r(a,b),key:"rectangle-".concat(null==a?void 0:a.x,"-").concat(null==a?void 0:a.y,"-").concat(null==a?void 0:a.value,"-").concat(b)}),f.createElement(rc,d))}),d&&q6.renderCallByParent(c,b)):null}function rF(a){var{props:b,previousRectanglesRef:c}=a,{data:d,layout:e,isAnimationActive:g,animationBegin:h,animationDuration:i,animationEasing:j,onAnimationEnd:k,onAnimationStart:l}=b,m=c.current,n=oW(b,"recharts-bar-"),[o,p]=(0,f.useState)(!1),q=(0,f.useCallback)(()=>{"function"==typeof k&&k(),p(!1)},[k]),r=(0,f.useCallback)(()=>{"function"==typeof l&&l(),p(!0)},[l]);return f.createElement(oa,{begin:h,duration:i,isActive:g,easing:j,from:{t:0},to:{t:1},onAnimationEnd:q,onAnimationStart:r,key:n},a=>{var{t:g}=a,h=1===g?d:d.map((a,b)=>{var c=m&&m[b];if(c){var d=u(c.x,a.x),f=u(c.y,a.y),h=u(c.width,a.width),i=u(c.height,a.height);return rz(rz({},a),{},{x:d(g),y:f(g),width:h(g),height:i(g)})}if("horizontal"===e){var j=u(0,a.height)(g);return rz(rz({},a),{},{y:a.y+a.height-j,height:j})}var k=u(0,a.width)(g);return rz(rz({},a),{},{width:k})});return g>0&&(c.current=h),f.createElement(mN,null,f.createElement(rE,{props:b,data:h,showLabels:!o}))})}function rG(a){var{data:b,isAnimationActive:c}=a,d=(0,f.useRef)(null);return c&&b&&b.length&&(null==d.current||d.current!==b)?f.createElement(rF,{previousRectanglesRef:d,props:a}):f.createElement(rE,{props:a,data:b,showLabels:!0})}var rH=(a,b)=>{var c=Array.isArray(a.value)?a.value[1]:a.value;return{x:a.x,y:a.y,value:c,errorVal:cs(a,b)}};class rI extends f.PureComponent{constructor(){super(...arguments),rA(this,"id",r("recharts-bar-"))}render(){var{hide:a,data:b,dataKey:c,className:d,xAxisId:e,yAxisId:h,needClip:i,background:j,id:k,layout:l}=this.props;if(a)return null;var m=g("recharts-bar",d),n=null==k?this.id:k;return f.createElement(mN,{className:m},i&&f.createElement("defs",null,f.createElement(rg,{clipPathId:n,xAxisId:e,yAxisId:h})),f.createElement(mN,{className:"recharts-bar-rectangles",clipPath:i?"url(#clipPath-".concat(n,")"):null},f.createElement(rD,{data:b,dataKey:c,background:j,allOtherBarProps:this.props}),f.createElement(rG,this.props)),f.createElement(qV,{direction:"horizontal"===l?"y":"x"},this.props.children))}}var rJ={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!nm.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function rK(a){var b,{xAxisId:c,yAxisId:d,hide:e,legendType:g,minPointSize:h,activeBar:i,animationBegin:j,animationDuration:k,animationEasing:l,isAnimationActive:m}=mh(a,rJ),{needClip:n}=rf(c,d),o=c$(),p=cR(),q=(0,f.useMemo)(()=>{var b;return{barSize:a.barSize,data:void 0,dataKey:a.dataKey,maxBarSize:a.maxBarSize,minPointSize:h,stackId:null==(b=a.stackId)?void 0:String(b)}},[a.barSize,a.dataKey,a.maxBarSize,h,a.stackId]),r=lU(a.children,nM),s=cb(a=>rt(a,c,d,p,q,r));if("vertical"!==o&&"horizontal"!==o)return null;var t=null==s?void 0:s[0];return b=null==t||null==t.height||null==t.width?0:"vertical"===o?t.height/2:t.width/2,f.createElement(qN,{xAxisId:c,yAxisId:d,data:s,dataPointFormatter:rH,errorBarOffset:b},f.createElement(rI,rx({},a,{layout:o,needClip:n,data:s,xAxisId:c,yAxisId:d,hide:e,legendType:g,minPointSize:h,activeBar:i,animationBegin:j,animationDuration:k,animationEasing:l,isAnimationActive:m})))}class rL extends f.PureComponent{render(){return f.createElement(qO,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},f.createElement(re,null),f.createElement(oU,{legendPayload:(a=>{var{dataKey:b,name:c,fill:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:cC(c,b),payload:a}]})(this.props)}),f.createElement(oT,{fn:rC,args:this.props}),f.createElement(rK,this.props))}}rA(rL,"displayName","Bar"),rA(rL,"defaultProps",rJ);var rM=c(19603),rN=c(49700),rO=c(88089),rP=c(19251),rQ=c(75580),rR=c(16189);function rS(){let a=(0,rR.useRouter)(),[b,c]=(0,f.useState)([]),[d,g]=(0,f.useState)(null),[h,i]=(0,f.useState)(!0),[j,k]=(0,f.useState)(null),l=async()=>{try{i(!0);let a=await fetch("/api/vip"),b=await a.json();b.success?(c(b.data.users),g(b.data.stats)):k(b.error||"Failed to load data")}catch(a){k("Network error occurred"),console.error("Error fetching VIP data:",a)}finally{i(!1)}};if(h)return(0,e.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,e.jsxs)("div",{className:"text-center",children:[(0,e.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,e.jsx)("p",{className:"text-gray-600",children:"加载统计数据中..."})]})});if(j)return(0,e.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,e.jsxs)("div",{className:"text-center",children:[(0,e.jsxs)("p",{className:"text-red-600 mb-4",children:["错误: ",j]}),(0,e.jsx)("button",{onClick:l,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"重试"})]})});let m=d?[{name:"VIP1",value:d.levelDistribution.VIP1,color:"#3B82F6"},{name:"VIP2",value:d.levelDistribution.VIP2,color:"#8B5CF6"},{name:"VIP3",value:d.levelDistribution.VIP3,color:"#EAB308"}]:[],n=b.sort((a,b)=>b.currentBalance-a.currentBalance).slice(0,10).map(a=>({name:a.name,balance:a.currentBalance,deposit:a.monthlyDeposit,consumption:a.monthlyConsumption})),o=b.map(a=>({name:a.name,balance:a.currentBalance,level:a.level})).sort((a,b)=>b.balance-a.balance);return(0,e.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,e.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,e.jsxs)("button",{onClick:()=>a.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-6",children:[(0,e.jsx)(rM.A,{className:"h-5 w-5 mr-2"}),"返回首页"]}),(0,e.jsxs)("div",{className:"mb-8",children:[(0,e.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"VIP数据统计"}),(0,e.jsx)("p",{className:"text-gray-600",children:"详细的VIP用户数据分析和可视化"})]}),d&&(0,e.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,e.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)("div",{className:"bg-blue-100 rounded-md p-3",children:(0,e.jsx)(rN.A,{className:"h-6 w-6 text-blue-600"})}),(0,e.jsxs)("div",{className:"ml-4",children:[(0,e.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总用户数"}),(0,e.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:d.totalUsers})]})]})}),(0,e.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)("div",{className:"bg-green-100 rounded-md p-3",children:(0,e.jsx)(rO.A,{className:"h-6 w-6 text-green-600"})}),(0,e.jsxs)("div",{className:"ml-4",children:[(0,e.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"平均余额"}),(0,e.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:["\xa5",d.averageBalance.toLocaleString()]})]})]})}),(0,e.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)("div",{className:"bg-purple-100 rounded-md p-3",children:(0,e.jsx)(rP.A,{className:"h-6 w-6 text-purple-600"})}),(0,e.jsxs)("div",{className:"ml-4",children:[(0,e.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"存消比"}),(0,e.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:d.totalConsumption>0?(d.totalDeposits/d.totalConsumption).toFixed(2):"∞"})]})]})}),(0,e.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)("div",{className:"bg-red-100 rounded-md p-3",children:(0,e.jsx)(rQ.A,{className:"h-6 w-6 text-red-600"})}),(0,e.jsxs)("div",{className:"ml-4",children:[(0,e.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"净流入"}),(0,e.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:["\xa5",(d.totalDeposits-d.totalConsumption).toLocaleString()]})]})]})})]}),(0,e.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,e.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"VIP等级分布"}),(0,e.jsx)("div",{className:"h-64",children:(0,e.jsx)(z,{width:"100%",height:"100%",children:(0,e.jsxs)(mo,{children:[(0,e.jsx)(o9,{data:m,cx:"50%",cy:"50%",labelLine:!1,label:a=>`${a.name||""}: ${a.value||0} (${(100*(a.percent||0)).toFixed(0)}%)`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:m.map((a,b)=>(0,e.jsx)(nM,{fill:a.color},`cell-${b}`))}),(0,e.jsx)(pG,{})]})})})]}),(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,e.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"余额排行榜 (前10名)"}),(0,e.jsx)("div",{className:"h-64",children:(0,e.jsx)(z,{width:"100%",height:"100%",children:(0,e.jsxs)(pM,{data:n,margin:{top:5,right:30,left:20,bottom:5},children:[(0,e.jsx)(qr,{strokeDasharray:"3 3"}),(0,e.jsx)(qA,{dataKey:"name"}),(0,e.jsx)(qI,{}),(0,e.jsx)(pG,{formatter:a=>[`\xa5${Number(a).toLocaleString()}`,"余额"]}),(0,e.jsx)(rL,{dataKey:"balance",fill:"#3B82F6"})]})})})]})]}),(0,e.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:[(0,e.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,e.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"用户余额详情"})}),(0,e.jsx)("div",{className:"overflow-x-auto",children:(0,e.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,e.jsx)("thead",{className:"bg-gray-50",children:(0,e.jsxs)("tr",{children:[(0,e.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"排名"}),(0,e.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户名"}),(0,e.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"等级"}),(0,e.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"当前余额"}),(0,e.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"本月存入"}),(0,e.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"本月消费"})]})}),(0,e.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map((a,c)=>(0,e.jsxs)("tr",{className:c%2==0?"bg-white":"bg-gray-50",children:[(0,e.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",c+1]}),(0,e.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.name}),(0,e.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,e.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"VIP1"===a.level?"bg-blue-100 text-blue-800":"VIP2"===a.level?"bg-purple-100 text-purple-800":"bg-yellow-100 text-yellow-800"}`,children:a.level})}),(0,e.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,e.jsxs)("span",{className:a.balance>=0?"text-green-600":"text-red-600",children:["\xa5",a.balance.toLocaleString()]})}),(0,e.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-green-600",children:["\xa5",b.find(b=>b.name===a.name)?.monthlyDeposit.toLocaleString()||"0"]}),(0,e.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-red-600",children:["\xa5",b.find(b=>b.name===a.name)?.monthlyConsumption.toLocaleString()||"0"]})]},a.name))})]})})]})]})})}},14337:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getSymbols=function(a){return Object.getOwnPropertySymbols(a).filter(b=>Object.prototype.propertyIsEnumerable.call(a,b))}},16189:(a,b,c)=>{"use strict";var d=c(65773);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},16759:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["stats",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,27860)),"/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/stats/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/aj/vip-simple-website/vip-dashboard/app/stats/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/stats/page",pathname:"/stats",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/stats/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},18678:(a,b,c)=>{a.exports=c(23647).last},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19251:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23033).A)("wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},19603:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23033).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},20504:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(73199);b.toNumber=function(a){return d.isSymbol(a)?NaN:Number(a)}},23033:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(43210);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}},23210:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toPath=function(a){let b=[],c=a.length;if(0===c)return b;let d=0,e="",f="",g=!1;for(46===a.charCodeAt(0)&&(b.push(""),d++);d<c;){let h=a[d];f?"\\"===h&&d+1<c?e+=a[++d]:h===f?f="":e+=h:g?'"'===h||"'"===h?f=h:"]"===h?(g=!1,b.push(e),e=""):e+=h:"["===h?(g=!0,e&&(b.push(e),e="")):"."===h?e&&(b.push(e),e=""):e+=h,d++}return e&&b.push(e),b}},23249:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(77262),e=c(75267),f=c(60266),g=c(23210);b.get=function a(b,c,h){if(null==b)return h;switch(typeof c){case"string":{if(d.isUnsafeProperty(c))return h;let f=b[c];if(void 0===f)if(e.isDeepKey(c))return a(b,g.toPath(c),h);else return h;return f}case"number":case"symbol":{"number"==typeof c&&(c=f.toKey(c));let a=b[c];if(void 0===a)return h;return a}default:{if(Array.isArray(c)){var i=b,j=c,k=h;if(0===j.length)return k;let a=i;for(let b=0;b<j.length;b++){if(null==a||d.isUnsafeProperty(j[b]))return k;a=a[j[b]]}return void 0===a?k:a}if(c=Object.is(c?.valueOf(),-0)?"-0":String(c),d.isUnsafeProperty(c))return h;let a=b[c];if(void 0===a)return h;return a}}}},23647:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(87130),e=c(44644),f=c(81569);b.last=function(a){if(f.isArrayLike(a))return d.last(e.toArray(a))}},24108:(a,b,c)=>{a.exports=c(55544).isEqual},24269:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPrimitive=function(a){return null==a||"object"!=typeof a&&"function"!=typeof a}},24595:(a,b,c)=>{"use strict";c(49283)},26188:(a,b,c)=>{"use strict";a.exports=c(47528)},26604:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObject=function(a){return null!==a&&("object"==typeof a||"function"==typeof a)}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27235:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if(!a||"object"!=typeof a)return!1;let b=Object.getPrototypeOf(a);return(null===b||b===Object.prototype||null===Object.getPrototypeOf(b))&&"[object Object]"===Object.prototype.toString.call(a)}},27338:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(81569),e=c(30223);b.isArrayLikeObject=function(a){return e.isObjectLike(a)&&d.isArrayLike(a)}},27860:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/stats/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/aj/vip-simple-website/vip-dashboard/src/app/stats/page.tsx","default")},28354:a=>{"use strict";a.exports=require("util")},29251:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.noop=function(){}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29443:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},29694:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.eq=function(a,b){return a===b||Number.isNaN(a)&&Number.isNaN(b)}},30223:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObjectLike=function(a){return"object"==typeof a&&null!==a}},32041:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.argumentsTag="[object Arguments]",b.arrayBufferTag="[object ArrayBuffer]",b.arrayTag="[object Array]",b.bigInt64ArrayTag="[object BigInt64Array]",b.bigUint64ArrayTag="[object BigUint64Array]",b.booleanTag="[object Boolean]",b.dataViewTag="[object DataView]",b.dateTag="[object Date]",b.errorTag="[object Error]",b.float32ArrayTag="[object Float32Array]",b.float64ArrayTag="[object Float64Array]",b.functionTag="[object Function]",b.int16ArrayTag="[object Int16Array]",b.int32ArrayTag="[object Int32Array]",b.int8ArrayTag="[object Int8Array]",b.mapTag="[object Map]",b.numberTag="[object Number]",b.objectTag="[object Object]",b.regexpTag="[object RegExp]",b.setTag="[object Set]",b.stringTag="[object String]",b.symbolTag="[object Symbol]",b.uint16ArrayTag="[object Uint16Array]",b.uint32ArrayTag="[object Uint32Array]",b.uint8ArrayTag="[object Uint8Array]",b.uint8ClampedArrayTag="[object Uint8ClampedArray]"},33873:a=>{"use strict";a.exports=require("path")},36450:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(2654),e=c(36970),f=c(525);b.sortBy=function(a,...b){let c=b.length;return c>1&&f.isIterateeCall(a,b[0],b[1])?b=[]:c>2&&f.isIterateeCall(b[0],b[1],b[2])&&(b=[b[0]]),d.orderBy(a,e.flatten(b),["asc"])}},36970:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.flatten=function(a,b=1){let c=[],d=Math.floor(b),e=(a,b)=>{for(let f=0;f<a.length;f++){let g=a[f];Array.isArray(g)&&b<d?e(g,b+1):c.push(g)}};return e(a,0),c}},40420:(a,b,c)=>{Promise.resolve().then(c.bind(c,27860))},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41421:(a,b,c)=>{a.exports=c(9549).range},41653:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(9773),e=c(97716),f=c(3458),g=c(41763);b.iteratee=function(a){if(null==a)return d.identity;switch(typeof a){case"function":return a;case"object":if(Array.isArray(a)&&2===a.length)return g.matchesProperty(a[0],a[1]);return f.matches(a);case"string":case"symbol":case"number":return e.property(a)}}},41763:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(6262),e=c(60266),f=c(88158),g=c(23249),h=c(53269);b.matchesProperty=function(a,b){switch(typeof a){case"object":Object.is(a?.valueOf(),-0)&&(a="-0");break;case"number":a=e.toKey(a)}return b=f.cloneDeep(b),function(c){let e=g.get(c,a);return void 0===e?h.has(c,a):void 0===b?void 0===e:d.isMatch(e,b)}}},44644:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toArray=function(a){return Array.isArray(a)?a:Array.from(a)}},47528:(a,b)=>{"use strict";var c=Symbol.for("react.element"),d=Symbol.for("react.portal"),e=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),h=Symbol.for("react.provider"),i=Symbol.for("react.context"),j=Symbol.for("react.server_context"),k=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),n=Symbol.for("react.memo"),o=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),b.isFragment=function(a){return function(a){if("object"==typeof a&&null!==a){var b=a.$$typeof;switch(b){case c:switch(a=a.type){case e:case g:case f:case l:case m:return a;default:switch(a=a&&a.$$typeof){case j:case i:case k:case o:case n:case h:return a;default:return b}}case d:return b}}}(a)===e}},49283:(a,b,c)=>{"use strict";var d=c(43210);"function"==typeof Object.is&&Object.is,d.useSyncExternalStore,d.useRef,d.useEffect,d.useMemo,d.useDebugValue},49700:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23033).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},52375:(a,b,c)=>{"use strict";a.exports=c(7544)},53269:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(75267),e=c(54050),f=c(88927),g=c(23210);b.has=function(a,b){let c;if(0===(c=Array.isArray(b)?b:"string"==typeof b&&d.isDeepKey(b)&&a?.[b]==null?g.toPath(b):[b]).length)return!1;let h=a;for(let a=0;a<c.length;a++){let b=c[a];if((null==h||!Object.hasOwn(h,b))&&!((Array.isArray(h)||f.isArguments(h))&&e.isIndex(b)&&b<h.length))return!1;h=h[b]}return!0}},53877:()=>{},54050:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let c=/^(?:0|[1-9]\d*)$/;b.isIndex=function(a,b=Number.MAX_SAFE_INTEGER){switch(typeof a){case"number":return Number.isInteger(a)&&a>=0&&a<b;case"symbol":return!1;case"string":return c.test(a)}}},55544:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(60144),e=c(29251);b.isEqual=function(a,b){return d.isEqualWith(a,b,e.noop)}},57496:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getTag=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":Object.prototype.toString.call(a)}},60144:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(27235),e=c(14337),f=c(57496),g=c(32041),h=c(29694);b.isEqualWith=function(a,b,c){return function a(b,c,i,j,k,l,m){let n=m(b,c,i,j,k,l);if(void 0!==n)return n;if(typeof b==typeof c)switch(typeof b){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return b===c;case"number":return b===c||Object.is(b,c)}return function b(c,i,j,k){if(Object.is(c,i))return!0;let l=f.getTag(c),m=f.getTag(i);if(l===g.argumentsTag&&(l=g.objectTag),m===g.argumentsTag&&(m=g.objectTag),l!==m)return!1;switch(l){case g.stringTag:return c.toString()===i.toString();case g.numberTag:{let a=c.valueOf(),b=i.valueOf();return h.eq(a,b)}case g.booleanTag:case g.dateTag:case g.symbolTag:return Object.is(c.valueOf(),i.valueOf());case g.regexpTag:return c.source===i.source&&c.flags===i.flags;case g.functionTag:return c===i}let n=(j=j??new Map).get(c),o=j.get(i);if(null!=n&&null!=o)return n===i;j.set(c,i),j.set(i,c);try{switch(l){case g.mapTag:if(c.size!==i.size)return!1;for(let[b,d]of c.entries())if(!i.has(b)||!a(d,i.get(b),b,c,i,j,k))return!1;return!0;case g.setTag:{if(c.size!==i.size)return!1;let b=Array.from(c.values()),d=Array.from(i.values());for(let e=0;e<b.length;e++){let f=b[e],g=d.findIndex(b=>a(f,b,void 0,c,i,j,k));if(-1===g)return!1;d.splice(g,1)}return!0}case g.arrayTag:case g.uint8ArrayTag:case g.uint8ClampedArrayTag:case g.uint16ArrayTag:case g.uint32ArrayTag:case g.bigUint64ArrayTag:case g.int8ArrayTag:case g.int16ArrayTag:case g.int32ArrayTag:case g.bigInt64ArrayTag:case g.float32ArrayTag:case g.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(c)!==Buffer.isBuffer(i)||c.length!==i.length)return!1;for(let b=0;b<c.length;b++)if(!a(c[b],i[b],b,c,i,j,k))return!1;return!0;case g.arrayBufferTag:if(c.byteLength!==i.byteLength)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.dataViewTag:if(c.byteLength!==i.byteLength||c.byteOffset!==i.byteOffset)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.errorTag:return c.name===i.name&&c.message===i.message;case g.objectTag:{if(!(b(c.constructor,i.constructor,j,k)||d.isPlainObject(c)&&d.isPlainObject(i)))return!1;let f=[...Object.keys(c),...e.getSymbols(c)],g=[...Object.keys(i),...e.getSymbols(i)];if(f.length!==g.length)return!1;for(let b=0;b<f.length;b++){let d=f[b],e=c[d];if(!Object.hasOwn(i,d))return!1;let g=i[d];if(!a(e,g,d,c,i,j,k))return!1}return!0}default:return!1}}finally{j.delete(c),j.delete(i)}}(b,c,l,m)}(a,b,void 0,void 0,void 0,void 0,c)}},60266:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toKey=function(a){return"string"==typeof a||"symbol"==typeof a?a:Object.is(a?.valueOf?.(),-0)?"-0":String(a)}},61135:()=>{},62210:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(79999),e=c(32041);b.cloneDeepWith=function(a,b){return d.cloneDeepWith(a,(c,f,g,h)=>{let i=b?.(c,f,g,h);if(null!=i)return i;if("object"==typeof a)switch(Object.prototype.toString.call(a)){case e.numberTag:case e.stringTag:case e.booleanTag:{let b=new a.constructor(a?.valueOf());return d.copyProperties(b,a),b}case e.argumentsTag:{let b={};return d.copyProperties(b,a),b.length=a.length,b[Symbol.iterator]=a[Symbol.iterator],b}default:return}})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64698:(a,b,c)=>{a.exports=c(94096).throttle},66726:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.uniqBy=function(a,b){let c=new Map;for(let d=0;d<a.length;d++){let e=a[d],f=b(e);c.has(f)||c.set(f,e)}return Array.from(c.values())}},68947:(a,b)=>{"use strict";function c(a){return"symbol"==typeof a?1:null===a?2:void 0===a?3:4*(a!=a)}Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.compareValues=(a,b,d)=>{if(a!==b){let e=c(a),f=c(b);if(e===f&&0===e){if(a<b)return"desc"===d?1:-1;if(a>b)return"desc"===d?-1:1}return"desc"===d?f-e:e-f}return 0}},69771:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},70276:(a,b,c)=>{Promise.resolve().then(c.bind(c,13976))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},72259:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92120);b.debounce=function(a,b=0,c={}){let e;"object"!=typeof c&&(c={});let{leading:f=!1,trailing:g=!0,maxWait:h}=c,i=[,,];f&&(i[0]="leading"),g&&(i[1]="trailing");let j=null,k=d.debounce(function(...b){e=a.apply(this,b),j=null},b,{edges:i}),l=function(...b){return null!=h&&(null===j&&(j=Date.now()),Date.now()-j>=h)?(e=a.apply(this,b),j=Date.now(),k.cancel(),k.schedule(),e):(k.apply(this,b),e)};return l.cancel=k.cancel,l.flush=()=>(k.flush(),e),l}},73199:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isSymbol=function(a){return"symbol"==typeof a||a instanceof Symbol}},74633:a=>{"use strict";var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),new d().__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},75267:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isDeepKey=function(a){switch(typeof a){case"number":case"symbol":return!1;case"string":return a.includes(".")||a.includes("[")||a.includes("]")}}},75580:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23033).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},77262:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isUnsafeProperty=function(a){return"__proto__"===a}},79999:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(14337),e=c(57496),f=c(32041),g=c(24269),h=c(97471);function i(a,b,c,d=new Map,k){let l=k?.(a,b,c,d);if(null!=l)return l;if(g.isPrimitive(a))return a;if(d.has(a))return d.get(a);if(Array.isArray(a)){let b=Array(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return Object.hasOwn(a,"index")&&(b.index=a.index),Object.hasOwn(a,"input")&&(b.input=a.input),b}if(a instanceof Date)return new Date(a.getTime());if(a instanceof RegExp){let b=new RegExp(a.source,a.flags);return b.lastIndex=a.lastIndex,b}if(a instanceof Map){let b=new Map;for(let[e,f]of(d.set(a,b),a))b.set(e,i(f,e,c,d,k));return b}if(a instanceof Set){let b=new Set;for(let e of(d.set(a,b),a))b.add(i(e,void 0,c,d,k));return b}if("undefined"!=typeof Buffer&&Buffer.isBuffer(a))return a.subarray();if(h.isTypedArray(a)){let b=new(Object.getPrototypeOf(a)).constructor(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return b}if(a instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&a instanceof SharedArrayBuffer)return a.slice(0);if(a instanceof DataView){let b=new DataView(a.buffer.slice(0),a.byteOffset,a.byteLength);return d.set(a,b),j(b,a,c,d,k),b}if("undefined"!=typeof File&&a instanceof File){let b=new File([a],a.name,{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Blob){let b=new Blob([a],{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Error){let b=new a.constructor;return d.set(a,b),b.message=a.message,b.name=a.name,b.stack=a.stack,b.cause=a.cause,j(b,a,c,d,k),b}if("object"==typeof a&&function(a){switch(e.getTag(a)){case f.argumentsTag:case f.arrayTag:case f.arrayBufferTag:case f.dataViewTag:case f.booleanTag:case f.dateTag:case f.float32ArrayTag:case f.float64ArrayTag:case f.int8ArrayTag:case f.int16ArrayTag:case f.int32ArrayTag:case f.mapTag:case f.numberTag:case f.objectTag:case f.regexpTag:case f.setTag:case f.stringTag:case f.symbolTag:case f.uint8ArrayTag:case f.uint8ClampedArrayTag:case f.uint16ArrayTag:case f.uint32ArrayTag:return!0;default:return!1}}(a)){let b=Object.create(Object.getPrototypeOf(a));return d.set(a,b),j(b,a,c,d,k),b}return a}function j(a,b,c=a,e,f){let g=[...Object.keys(b),...d.getSymbols(b)];for(let d=0;d<g.length;d++){let h=g[d],j=Object.getOwnPropertyDescriptor(a,h);(null==j||j.writable)&&(a[h]=i(b[h],h,c,e,f))}}b.cloneDeepWith=function(a,b){return i(a,void 0,a,new Map,b)},b.cloneDeepWithImpl=i,b.copyProperties=j},80737:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(73199),e=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,f=/^\w*$/;b.isKey=function(a,b){return!Array.isArray(a)&&(!!("number"==typeof a||"boolean"==typeof a||null==a||d.isSymbol(a))||"string"==typeof a&&(f.test(a)||!e.test(a))||null!=b&&Object.hasOwn(b,a))}},81569:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(87918);b.isArrayLike=function(a){return null!=a&&"function"!=typeof a&&d.isLength(a.length)}},83570:(a,b,c)=>{a.exports=c(89003).uniqBy},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86980:(a,b,c)=>{a.exports=c(23249).get},87130:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.last=function(a){return a[a.length-1]}},87918:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isLength=function(a){return Number.isSafeInteger(a)&&a>=0}},88089:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23033).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},88158:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(62210);b.cloneDeep=function(a){return d.cloneDeepWith(a)}},88927:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(57496);b.isArguments=function(a){return null!==a&&"object"==typeof a&&"[object Arguments]"===d.getTag(a)}},89003:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(66726),e=c(9773),f=c(27338),g=c(41653);b.uniqBy=function(a,b=e.identity){return f.isArrayLikeObject(a)?d.uniqBy(Array.from(a),g.iteratee(b)):[]}},89274:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(6262),e=c(26604),f=c(24269),g=c(29694);function h(a,b,c,d){if(b===a)return!0;switch(typeof b){case"object":return function(a,b,c,d){if(null==b)return!0;if(Array.isArray(b))return i(a,b,c,d);if(b instanceof Map){var e=a,g=b,h=c,k=d;if(0===g.size)return!0;if(!(e instanceof Map))return!1;for(let[a,b]of g.entries())if(!1===h(e.get(a),b,a,e,g,k))return!1;return!0}if(b instanceof Set)return j(a,b,c,d);let l=Object.keys(b);if(null==a)return 0===l.length;if(0===l.length)return!0;if(d&&d.has(b))return d.get(b)===a;d&&d.set(b,a);try{for(let e=0;e<l.length;e++){let g=l[e];if(!f.isPrimitive(a)&&!(g in a)||void 0===b[g]&&void 0!==a[g]||null===b[g]&&null!==a[g]||!c(a[g],b[g],g,a,b,d))return!1}return!0}finally{d&&d.delete(b)}}(a,b,c,d);case"function":if(Object.keys(b).length>0)return h(a,{...b},c,d);return g.eq(a,b);default:if(!e.isObject(a))return g.eq(a,b);if("string"==typeof b)return""===b;return!0}}function i(a,b,c,d){if(0===b.length)return!0;if(!Array.isArray(a))return!1;let e=new Set;for(let f=0;f<b.length;f++){let g=b[f],h=!1;for(let i=0;i<a.length;i++){if(e.has(i))continue;let j=a[i],k=!1;if(c(j,g,f,a,b,d)&&(k=!0),k){e.add(i),h=!0;break}}if(!h)return!1}return!0}function j(a,b,c,d){return 0===b.size||a instanceof Set&&i([...a],[...b],c,d)}b.isMatchWith=function(a,b,c){return"function"!=typeof c?d.isMatch(a,b):h(a,b,function a(b,d,e,f,g,i){let j=c(b,d,e,f,g,i);return void 0!==j?!!j:h(b,d,a,i)},new Map)},b.isSetMatch=j},90679:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(79999);b.cloneDeep=function(a){return d.cloneDeepWithImpl(a,void 0,a,new Map,void 0)}},90823:(a,b,c)=>{"use strict";var d=c(43210),e=c(52375),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;b.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},92120:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.debounce=function(a,b,{signal:c,edges:d}={}){let e,f=null,g=null!=d&&d.includes("leading"),h=null==d||d.includes("trailing"),i=()=>{null!==f&&(a.apply(e,f),e=void 0,f=null)},j=null,k=()=>{null!=j&&clearTimeout(j),j=setTimeout(()=>{j=null,h&&i(),l()},b)},l=()=>{null!==j&&(clearTimeout(j),j=null),e=void 0,f=null},m=function(...a){if(c?.aborted)return;e=this,f=a;let b=null==j;k(),g&&b&&i()};return m.schedule=k,m.cancel=l,m.flush=()=>{i()},c?.addEventListener("abort",l,{once:!0}),m}},92426:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(20504);b.toFinite=function(a){return a?(a=d.toNumber(a))===1/0||a===-1/0?(a<0?-1:1)*Number.MAX_VALUE:a==a?a:0:0===a?a:0}},94096:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(72259);b.throttle=function(a,b=0,c={}){let{leading:e=!0,trailing:f=!0}=c;return d.debounce(a,b,{leading:e,maxWait:b,trailing:f})}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(2202),f=c.n(e),g=c(64988),h=c.n(g);c(61135);let i={title:"Create Next App",description:"Generated by create next app"};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},95153:(a,b,c)=>{"use strict";a.exports=c(90823)},95733:()=>{},97471:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isTypedArray=function(a){return ArrayBuffer.isView(a)&&!(a instanceof DataView)}},97716:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(23249);b.property=function(a){return function(b){return d.get(b,a)}}},98323:(a,b,c)=>{a.exports=c(36450).sortBy},98583:(a,b,c)=>{a.exports=c(11728).isPlainObject}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,400],()=>b(b.s=16759));module.exports=c})();