import { Worker as JestWorker } from 'next/dist/compiled/jest-worker';
type FarmOptions = NonNullable<ConstructorParameters<typeof JestWorker>[1]>;
export declare function getNextBuildDebuggerPortOffset(_: {
    kind: 'export-page';
}): number;
export declare class Worker {
    private _worker;
    constructor(workerPath: string, options: Omit<FarmOptions, 'forkOptions'> & {
        forkOptions?: (Omit<NonNullable<FarmOptions['forkOptions']>, 'env'> & {
            env?: Partial<NodeJS.ProcessEnv> | undefined;
        }) | undefined;
        /**
         * `-1` if not inspectable
         */
        debuggerPortOffset: number;
        enableSourceMaps?: boolean;
        /**
         * True if `--max-old-space-size` should not be forwarded to the worker.
         */
        isolatedMemory: boolean;
        timeout?: number;
        onActivity?: () => void;
        onActivityAbort?: () => void;
        onRestart?: (method: string, args: any[], attempts: number) => void;
        logger?: Pick<typeof console, 'error' | 'info' | 'warn'>;
        exposedMethods: ReadonlyArray<string>;
        enableWorkerThreads?: boolean;
    });
    end(): ReturnType<JestWorker['end']>;
    /**
     * Quietly end the worker if it exists
     */
    close(): void;
}
export {};
