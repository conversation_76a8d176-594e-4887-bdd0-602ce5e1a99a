'use client';

import { useState, useEffect } from 'react';
import { VIPUser, VIPStats } from '@/types/vip';
import VIPUserCard from '@/components/VIPUserCard';
import VIPStatsCard from '@/components/VIPStatsCard';
import { Users, TrendingUp, TrendingDown, Wallet, BarChart3 } from 'lucide-react';
import Link from 'next/link';

export default function Home() {
  const [users, setUsers] = useState<VIPUser[]>([]);
  const [stats, setStats] = useState<VIPStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchVIPData();
  }, []);

  const fetchVIPData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/vip');
      const result = await response.json();

      if (result.success) {
        setUsers(result.data.users);
        setStats(result.data.stats);
      } else {
        setError(result.error || 'Failed to load data');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error fetching VIP data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载VIP数据中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">错误: {error}</p>
          <button
            onClick={fetchVIPData}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">VIP简表管理系统</h1>
            <p className="text-gray-600">管理和查看VIP用户的财务信息</p>
          </div>
          <Link
            href="/stats"
            className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors w-full sm:w-auto"
          >
            <BarChart3 className="h-5 w-5 mr-2" />
            详细统计
          </Link>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <VIPStatsCard
              title="总用户数"
              value={stats.totalUsers}
              icon={<Users className="h-6 w-6" />}
              color="blue"
            />
            <VIPStatsCard
              title="总存入"
              value={`¥${stats.totalDeposits.toLocaleString()}`}
              icon={<TrendingUp className="h-6 w-6" />}
              color="green"
            />
            <VIPStatsCard
              title="总消费"
              value={`¥${stats.totalConsumption.toLocaleString()}`}
              icon={<TrendingDown className="h-6 w-6" />}
              color="red"
            />
            <VIPStatsCard
              title="总余额"
              value={`¥${stats.totalBalance.toLocaleString()}`}
              icon={<Wallet className="h-6 w-6" />}
              color="purple"
            />
          </div>
        )}

        {/* VIP等级分布 */}
        {stats && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">VIP等级分布</h2>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.levelDistribution.VIP1}</div>
                <div className="text-sm text-gray-600">VIP1</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.levelDistribution.VIP2}</div>
                <div className="text-sm text-gray-600">VIP2</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{stats.levelDistribution.VIP3}</div>
                <div className="text-sm text-gray-600">VIP3</div>
              </div>
            </div>
          </div>
        )}

        {/* VIP用户列表 */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">VIP用户列表</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 p-4 sm:p-6">
            {users.map((user) => (
              <VIPUserCard key={user.id} user={user} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
