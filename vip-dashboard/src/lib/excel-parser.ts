import * as XLSX from 'xlsx';
import { VIPUser, VIPStats, TransactionRecord } from '@/types/vip';

export class ExcelParser {
  private workbook: XLSX.WorkBook | null = null;
  private data: any[][] = [];

  async loadFromFile(file: File): Promise<void> {
    const buffer = await file.arrayBuffer();
    this.workbook = XLSX.read(buffer, { type: 'buffer' });
    const firstSheetName = this.workbook.SheetNames[0];
    const worksheet = this.workbook.Sheets[firstSheetName];
    this.data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  }

  async loadFromPath(path: string): Promise<void> {
    const response = await fetch(path);
    const buffer = await response.arrayBuffer();
    this.workbook = XLSX.read(buffer, { type: 'buffer' });
    const firstSheetName = this.workbook.SheetNames[0];
    const worksheet = this.workbook.Sheets[firstSheetName];
    this.data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  }

  parseVIPUsers(): VIPUser[] {
    if (!this.data || this.data.length === 0) {
      return [];
    }

    const users: VIPUser[] = [];
    const headers = this.data[0] as string[];
    
    // 找到主要列的索引
    const vipIndex = headers.findIndex(h => h === 'VIP');
    const levelIndex = headers.findIndex(h => h === '等级');
    const prevBalanceIndex = headers.findIndex(h => h === '六月余额');
    const depositIndex = headers.findIndex(h => h === '本月存入');
    const consumptionIndex = headers.findIndex(h => h === '本月消费');
    const currentBalanceIndex = headers.findIndex(h => h === '剩余金额');

    // 处理每一行数据
    for (let i = 1; i < this.data.length; i++) {
      const row = this.data[i];
      
      // 只处理有VIP用户名的行
      if (row[vipIndex] && typeof row[vipIndex] === 'string' && row[vipIndex].trim() !== '') {
        const user: VIPUser = {
          id: `vip-${i}`,
          name: row[vipIndex].toString().trim(),
          level: this.parseLevel(row[levelIndex]),
          previousBalance: this.parseNumber(row[prevBalanceIndex]),
          monthlyDeposit: this.parseNumber(row[depositIndex]),
          monthlyConsumption: this.parseNumber(row[consumptionIndex]),
          currentBalance: this.parseNumber(row[currentBalanceIndex]),
        };

        users.push(user);
      }
    }

    return users;
  }

  parseTransactionRecords(): TransactionRecord[] {
    if (!this.data || this.data.length === 0) {
      return [];
    }

    const records: TransactionRecord[] = [];
    const headers = this.data[0] as string[];
    
    const vipIndex = headers.findIndex(h => h === 'VIP');
    const typeIndex = headers.findIndex(h => h === '类型');
    const totalIndex = headers.findIndex(h => h === '总计');

    // 找到日期列（1-31号）
    const dayColumns: { [day: string]: number } = {};
    for (let i = 0; i < headers.length; i++) {
      const header = headers[i];
      if (header && /^\d+$/.test(header.toString())) {
        const day = parseInt(header.toString());
        if (day >= 1 && day <= 31) {
          dayColumns[day.toString()] = i;
        }
      }
    }

    // 处理每一行数据
    for (let i = 1; i < this.data.length; i++) {
      const row = this.data[i];
      
      if (row[typeIndex] && ['本月存入', '本月消费', '剩余金额', '退存单'].includes(row[typeIndex])) {
        const dailyData: { [day: string]: number } = {};
        
        // 提取每日数据
        Object.entries(dayColumns).forEach(([day, colIndex]) => {
          const value = this.parseNumber(row[colIndex]);
          if (value !== 0) {
            dailyData[day] = value;
          }
        });

        const record: TransactionRecord = {
          vipName: row[vipIndex]?.toString().trim() || '',
          type: row[typeIndex],
          total: this.parseNumber(row[totalIndex]),
          dailyData,
        };

        records.push(record);
      }
    }

    return records;
  }

  calculateStats(users: VIPUser[]): VIPStats {
    const stats: VIPStats = {
      totalUsers: users.length,
      levelDistribution: {
        VIP1: 0,
        VIP2: 0,
        VIP3: 0,
      },
      totalDeposits: 0,
      totalConsumption: 0,
      totalBalance: 0,
      averageBalance: 0,
    };

    users.forEach(user => {
      stats.levelDistribution[user.level]++;
      stats.totalDeposits += user.monthlyDeposit;
      stats.totalConsumption += user.monthlyConsumption;
      stats.totalBalance += user.currentBalance;
    });

    stats.averageBalance = users.length > 0 ? stats.totalBalance / users.length : 0;

    return stats;
  }

  private parseLevel(value: any): 'VIP1' | 'VIP2' | 'VIP3' {
    if (!value) return 'VIP1';
    const str = value.toString().toUpperCase();
    if (str.includes('VIP3')) return 'VIP3';
    if (str.includes('VIP2')) return 'VIP2';
    return 'VIP1';
  }

  private parseNumber(value: any): number {
    if (value === null || value === undefined || value === '') return 0;
    const num = parseFloat(value.toString());
    return isNaN(num) ? 0 : num;
  }
}

// 单例实例
export const excelParser = new ExcelParser();
