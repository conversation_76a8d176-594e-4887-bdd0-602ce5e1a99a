// VIP用户数据类型定义

export interface VIPUser {
  id: string;
  name: string;
  level: 'VIP1' | 'VIP2' | 'VIP3';
  previousBalance: number; // 六月余额
  monthlyDeposit: number; // 本月存入
  monthlyConsumption: number; // 本月消费
  currentBalance: number; // 剩余金额
  dailyTransactions?: DailyTransaction[]; // 每日交易记录
}

export interface DailyTransaction {
  day: number; // 1-31号
  amount: number;
  type: 'deposit' | 'consumption';
}

export interface VIPStats {
  totalUsers: number;
  levelDistribution: {
    VIP1: number;
    VIP2: number;
    VIP3: number;
  };
  totalDeposits: number;
  totalConsumption: number;
  totalBalance: number;
  averageBalance: number;
}

export interface TransactionRecord {
  vipName: string;
  type: '本月存入' | '本月消费' | '剩余金额' | '退存单';
  total: number;
  dailyData: { [day: string]: number };
}
