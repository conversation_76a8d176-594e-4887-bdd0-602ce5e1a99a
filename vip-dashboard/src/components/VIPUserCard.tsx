import { VIPUser } from '@/types/vip';
import { Crown, TrendingUp, TrendingDown, Wallet } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface VIPUserCardProps {
  user: VIPUser;
}

export default function VIPUserCard({ user }: VIPUserCardProps) {
  const router = useRouter();

  const handleViewDetails = () => {
    router.push(`/vip/${user.id}`);
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'VIP1':
        return 'text-blue-600 bg-blue-100';
      case 'VIP2':
        return 'text-purple-600 bg-purple-100';
      case 'VIP3':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-green-600';
    if (balance < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 card-hover">
      {/* 用户头部信息 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2 sm:space-x-3">
          <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <Crown className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
          </div>
          <div>
            <h3 className="text-base sm:text-lg font-semibold text-gray-900">{user.name}</h3>
            <span className={`inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium ${getLevelColor(user.level)}`}>
              {user.level}
            </span>
          </div>
        </div>
      </div>

      {/* 财务信息 */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Wallet className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">六月余额</span>
          </div>
          <span className={`text-sm font-medium ${getBalanceColor(user.previousBalance)}`}>
            ¥{user.previousBalance.toLocaleString()}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm text-gray-600">本月存入</span>
          </div>
          <span className="text-sm font-medium text-green-600">
            ¥{user.monthlyDeposit.toLocaleString()}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <TrendingDown className="h-4 w-4 text-red-500" />
            <span className="text-sm text-gray-600">本月消费</span>
          </div>
          <span className="text-sm font-medium text-red-600">
            ¥{user.monthlyConsumption.toLocaleString()}
          </span>
        </div>

        <div className="border-t pt-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900">当前余额</span>
            <span className={`text-lg font-bold ${getBalanceColor(user.currentBalance)}`}>
              ¥{user.currentBalance.toLocaleString()}
            </span>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="mt-4 pt-4 border-t">
        <button
          onClick={handleViewDetails}
          className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          查看详情
        </button>
      </div>
    </div>
  );
}
